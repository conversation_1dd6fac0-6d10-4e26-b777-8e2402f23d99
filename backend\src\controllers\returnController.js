const { ReturnRequest, Product, OperationLog } = require('../models');
const returnPolicyService = require('../services/returnPolicyService');
const logger = require('../utils/logger');

/**
 * 退货控制器
 * 处理退货申请的创建、审核、处理等操作
 */
class ReturnController {
  /**
   * 创建退货申请
   */
  async createReturnRequest(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        customer_info,
        purchase_info,
        items,
        chat_session_id
      } = req.body;
      
      // 验证必需字段
      if (!customer_info || !customer_info.name || !customer_info.phone) {
        return res.status(400).json({
          success: false,
          message: '客户姓名和电话是必需的'
        });
      }
      
      if (!purchase_info || !purchase_info.purchase_date) {
        return res.status(400).json({
          success: false,
          message: '购买日期是必需的'
        });
      }
      
      if (!items || !Array.isArray(items) || items.length === 0) {
        return res.status(400).json({
          success: false,
          message: '退货商品列表是必需的'
        });
      }
      
      // 验证商品项目
      for (let item of items) {
        if (!item.product_name || !item.quantity || !item.reason) {
          return res.status(400).json({
            success: false,
            message: '每个商品项目必须包含产品名称、数量和退货原因'
          });
        }
        
        // 如果提供了product_id，验证产品是否存在
        if (item.product_id) {
          const product = await Product.findById(item.product_id);
          if (!product) {
            return res.status(404).json({
              success: false,
              message: `产品 ${item.product_id} 不存在`
            });
          }
        }
        
        // 计算总金额
        item.total_amount = item.quantity * (item.unit_price || 0);
      }
      
      // 创建退货申请
      const returnRequest = new ReturnRequest({
        customer_info,
        purchase_info: {
          ...purchase_info,
          purchase_date: new Date(purchase_info.purchase_date),
          pickup_date: purchase_info.pickup_date ? new Date(purchase_info.pickup_date) : null
        },
        items,
        chat_session_id,
        created_by: req.user?.userId
      });
      
      // 检查是否在退货时限内
      if (!returnRequest.isWithinReturnPeriod(24)) {
        return res.status(400).json({
          success: false,
          message: '超出退货时限（取货后24小时内）'
        });
      }
      
      // AI辅助退货分析
      const aiAnalysis = await this.performAIReturnAnalysis(returnRequest);

      // 将AI分析结果添加到退货申请中
      if (aiAnalysis.success) {
        returnRequest.ai_analysis = aiAnalysis;

        // 根据AI建议设置优先级
        if (aiAnalysis.suggestions && aiAnalysis.suggestions.priority) {
          returnRequest.priority = aiAnalysis.suggestions.priority;
        }
      }

      await returnRequest.save();

      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        req.user?.userId,
        'return.create',
        'return_request',
        returnRequest._id.toString(),
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          after_data: {
            request_number: returnRequest.request_number,
            customer_name: returnRequest.customer_info.name,
            total_amount: returnRequest.total_amount
          },
          description: `创建退货申请: ${returnRequest.request_number}`
        }
      );
      
      logger.info(`退货申请创建: ${returnRequest.request_number} by ${customer_info.name}`);
      
      res.status(201).json({
        success: true,
        message: '退货申请提交成功',
        data: { returnRequest }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user?.userId,
        'return.create',
        'return_request',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('创建退货申请错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取退货申请列表
   */
  async getReturnRequests(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        page = 1,
        limit = 20,
        status,
        priority,
        customer_phone,
        start_date,
        end_date,
        assigned_to,
        sort_by = 'createdAt',
        sort_order = 'desc'
      } = req.query;
      
      // 构建查询条件
      const query = {};
      
      if (status) {
        query.status = status;
      }
      
      if (priority) {
        query.priority = priority;
      }
      
      if (customer_phone) {
        query['customer_info.phone'] = { $regex: customer_phone, $options: 'i' };
      }
      
      if (assigned_to) {
        query.assigned_to = assigned_to;
      }
      
      if (start_date || end_date) {
        query.createdAt = {};
        if (start_date) {
          query.createdAt.$gte = new Date(start_date);
        }
        if (end_date) {
          query.createdAt.$lte = new Date(end_date);
        }
      }
      
      // 构建排序
      const sortOptions = {};
      sortOptions[sort_by] = sort_order === 'desc' ? -1 : 1;
      
      // 分页查询
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      const [requests, total] = await Promise.all([
        ReturnRequest.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('created_by', 'username name')
          .populate('assigned_to', 'username name')
          .populate('reviewed_by', 'username name'),
        ReturnRequest.countDocuments(query)
      ]);
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        req.user.userId,
        'return.view',
        'return_request',
        'list',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `查询退货申请列表: ${requests.length}条记录`
        }
      );
      
      res.json({
        success: true,
        data: {
          requests,
          pagination: {
            current_page: parseInt(page),
            per_page: parseInt(limit),
            total,
            total_pages: Math.ceil(total / parseInt(limit))
          }
        }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'return.view',
        'return_request',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime
        }
      );
      
      logger.error('获取退货申请列表错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取退货申请详情
   */
  async getReturnRequestById(req, res) {
    try {
      const { id } = req.params;
      
      const returnRequest = await ReturnRequest.findById(id)
        .populate('created_by', 'username name')
        .populate('assigned_to', 'username name')
        .populate('reviewed_by', 'username name')
        .populate('chat_session_id');
      
      if (!returnRequest) {
        return res.status(404).json({
          success: false,
          message: '退货申请不存在'
        });
      }
      
      res.json({
        success: true,
        data: { returnRequest }
      });
      
    } catch (error) {
      logger.error('获取退货申请详情错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 审核退货申请
   */
  async reviewReturnRequest(req, res) {
    const startTime = Date.now();
    
    try {
      const { id } = req.params;
      const { action, refund_amount, refund_method, notes } = req.body;
      const userId = req.user.userId;
      
      if (!action || !['approve', 'reject'].includes(action)) {
        return res.status(400).json({
          success: false,
          message: '审核动作必须是 approve 或 reject'
        });
      }
      
      const returnRequest = await ReturnRequest.findById(id);
      
      if (!returnRequest) {
        return res.status(404).json({
          success: false,
          message: '退货申请不存在'
        });
      }
      
      if (!['submitted', 'under_review'].includes(returnRequest.status)) {
        return res.status(400).json({
          success: false,
          message: '只能审核已提交或审核中的退货申请'
        });
      }
      
      // 保存更新前的数据
      const beforeData = returnRequest.toJSON();
      
      if (action === 'approve') {
        if (!refund_amount || refund_amount <= 0) {
          return res.status(400).json({
            success: false,
            message: '退款金额是必需的且必须大于0'
          });
        }
        
        returnRequest.approve(userId, refund_amount, refund_method, notes);
      } else {
        if (!notes) {
          return res.status(400).json({
            success: false,
            message: '拒绝原因是必需的'
          });
        }
        
        returnRequest.reject(userId, notes);
      }
      
      await returnRequest.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logDataChange(
        userId,
        'return.approve',
        'return_request',
        id,
        beforeData,
        returnRequest.toJSON(),
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `退货申请${action === 'approve' ? '审核通过' : '审核拒绝'}: ${returnRequest.request_number}`
        }
      );
      
      logger.info(`退货申请${action === 'approve' ? '审核通过' : '审核拒绝'}: ${returnRequest.request_number} by ${req.user.username}`);
      
      res.json({
        success: true,
        message: `退货申请${action === 'approve' ? '审核通过' : '审核拒绝'}`,
        data: { returnRequest }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'return.approve',
        'return_request',
        error,
        {
          resource_id: req.params.id,
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('审核退货申请错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * AI辅助退货分析
   */
  async performAIReturnAnalysis(returnRequest) {
    try {
      logger.info('开始AI退货分析', { requestId: returnRequest._id });

      const analysisResults = [];

      // 分析每个退货商品
      for (const item of returnRequest.items) {
        // 获取产品信息
        let productInfo = {};
        if (item.product_id) {
          const product = await Product.findById(item.product_id);
          if (product) {
            productInfo = {
              name: product.name,
              category: product.category,
              price: product.price
            };
          }
        }

        // AI原因分析
        const reasonAnalysis = await returnPolicyService.analyzeReturnReason(
          item.reason,
          productInfo,
          returnRequest.purchase_info
        );

        // 政策匹配
        const policyMatch = await returnPolicyService.matchReturnPolicy(
          reasonAnalysis,
          returnRequest
        );

        // AI决策生成
        const decision = await returnPolicyService.generateReturnDecision(
          reasonAnalysis,
          policyMatch,
          returnRequest
        );

        // 表单填充建议
        const suggestions = returnPolicyService.generateFormSuggestions(
          reasonAnalysis,
          decision
        );

        analysisResults.push({
          item_index: returnRequest.items.indexOf(item),
          product_name: item.product_name,
          reason_analysis: reasonAnalysis,
          policy_match: policyMatch,
          ai_decision: decision,
          suggestions
        });
      }

      // 生成整体分析结果
      const overallAnalysis = this.generateOverallAnalysis(analysisResults);

      logger.info('AI退货分析完成', {
        requestId: returnRequest._id,
        itemCount: analysisResults.length,
        overallDecision: overallAnalysis.overall_decision
      });

      return {
        success: true,
        timestamp: new Date(),
        item_analyses: analysisResults,
        overall_analysis: overallAnalysis,
        suggestions: this.generateOverallSuggestions(analysisResults)
      };

    } catch (error) {
      logger.error('AI退货分析失败', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * 生成整体分析结果
   */
  generateOverallAnalysis(analysisResults) {
    const decisions = analysisResults.map(result => result.ai_decision.decision);
    const confidences = analysisResults.map(result => result.ai_decision.confidence);
    const riskLevels = analysisResults.map(result => result.ai_decision.risk_level);

    // 计算整体决策
    let overallDecision = 'review_required';
    if (decisions.every(d => d === 'approve')) {
      overallDecision = 'approve';
    } else if (decisions.every(d => d === 'reject')) {
      overallDecision = 'reject';
    }

    // 计算平均置信度
    const avgConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;

    // 确定整体风险级别
    let overallRisk = 'low';
    if (riskLevels.includes('high')) {
      overallRisk = 'high';
    } else if (riskLevels.includes('medium')) {
      overallRisk = 'medium';
    }

    // 计算建议退款金额
    const totalRefundAmount = analysisResults.reduce((sum, result) => {
      return sum + (result.ai_decision.refund_amount || 0);
    }, 0);

    return {
      overall_decision: overallDecision,
      confidence: avgConfidence,
      risk_level: overallRisk,
      total_refund_amount: totalRefundAmount,
      quality_issues_count: analysisResults.filter(r => r.reason_analysis.is_quality_issue).length,
      policy_violations_count: analysisResults.filter(r => !r.reason_analysis.is_valid_reason).length,
      requires_manual_review: overallDecision === 'review_required' || overallRisk === 'high'
    };
  }

  /**
   * 生成整体建议
   */
  generateOverallSuggestions(analysisResults) {
    const allSuggestions = analysisResults.map(result => result.suggestions);

    // 确定最高优先级
    const priorities = allSuggestions.map(s => s.priority);
    let overallPriority = 'low';
    if (priorities.includes('high')) {
      overallPriority = 'high';
    } else if (priorities.includes('medium')) {
      overallPriority = 'medium';
    }

    // 合并所有后续动作
    const allFollowUpActions = allSuggestions.reduce((actions, suggestion) => {
      return actions.concat(suggestion.follow_up_actions || []);
    }, []);

    // 去重后续动作
    const uniqueFollowUpActions = [...new Set(allFollowUpActions)];

    // 生成整体处理备注
    const processingNotes = allSuggestions.map(s => s.processing_notes).join('; ');

    return {
      priority: overallPriority,
      processing_notes: processingNotes,
      follow_up_actions: uniqueFollowUpActions,
      recommended_refund_method: allSuggestions[0]?.refund_method || 'original',
      customer_communication: this.generateOverallCustomerMessage(analysisResults)
    };
  }

  /**
   * 生成整体客户沟通消息
   */
  generateOverallCustomerMessage(analysisResults) {
    const approvedCount = analysisResults.filter(r => r.ai_decision.decision === 'approve').length;
    const rejectedCount = analysisResults.filter(r => r.ai_decision.decision === 'reject').length;
    const reviewCount = analysisResults.filter(r => r.ai_decision.decision === 'review_required').length;

    if (approvedCount === analysisResults.length) {
      return '您的退货申请已通过AI初步审核，我们将尽快为您处理退款。';
    } else if (rejectedCount === analysisResults.length) {
      return '很抱歉，您的退货申请不符合我们的退货政策。如有疑问，请联系客服。';
    } else {
      return '我们正在审核您的退货申请，部分商品需要人工确认。预计在24小时内给您回复。';
    }
  }

  /**
   * 获取AI分析结果
   */
  async getAIAnalysis(req, res) {
    try {
      const { id } = req.params;

      const returnRequest = await ReturnRequest.findById(id);

      if (!returnRequest) {
        return res.status(404).json({
          success: false,
          message: '退货申请不存在'
        });
      }

      // 如果没有AI分析结果，执行分析
      if (!returnRequest.ai_analysis) {
        const aiAnalysis = await this.performAIReturnAnalysis(returnRequest);

        if (aiAnalysis.success) {
          returnRequest.ai_analysis = aiAnalysis;
          await returnRequest.save();
        }
      }

      res.json({
        success: true,
        data: {
          ai_analysis: returnRequest.ai_analysis,
          request_id: returnRequest._id,
          request_number: returnRequest.request_number
        }
      });

    } catch (error) {
      logger.error('获取AI分析结果失败', error);

      res.status(500).json({
        success: false,
        message: '获取AI分析结果失败'
      });
    }
  }

  /**
   * 应用AI建议
   */
  async applyAISuggestions(req, res) {
    try {
      const { id } = req.params;
      const { apply_decision = false, apply_suggestions = false } = req.body;

      const returnRequest = await ReturnRequest.findById(id);

      if (!returnRequest) {
        return res.status(404).json({
          success: false,
          message: '退货申请不存在'
        });
      }

      if (!returnRequest.ai_analysis || !returnRequest.ai_analysis.success) {
        return res.status(400).json({
          success: false,
          message: '没有可用的AI分析结果'
        });
      }

      const aiAnalysis = returnRequest.ai_analysis;
      let updatedFields = {};

      // 应用AI决策
      if (apply_decision && aiAnalysis.overall_analysis) {
        const overallDecision = aiAnalysis.overall_analysis.overall_decision;

        if (overallDecision === 'approve') {
          updatedFields.status = 'approved';
          updatedFields.refund_amount = aiAnalysis.overall_analysis.total_refund_amount;
          updatedFields.reviewed_by = req.user.userId;
          updatedFields.reviewed_at = new Date();
        } else if (overallDecision === 'reject') {
          updatedFields.status = 'rejected';
          updatedFields.reviewed_by = req.user.userId;
          updatedFields.reviewed_at = new Date();
        }
      }

      // 应用AI建议
      if (apply_suggestions && aiAnalysis.suggestions) {
        if (aiAnalysis.suggestions.priority) {
          updatedFields.priority = aiAnalysis.suggestions.priority;
        }

        if (aiAnalysis.suggestions.processing_notes) {
          updatedFields.notes = aiAnalysis.suggestions.processing_notes;
        }
      }

      // 更新退货申请
      Object.assign(returnRequest, updatedFields);
      await returnRequest.save();

      logger.info('应用AI建议成功', {
        requestId: id,
        appliedDecision: apply_decision,
        appliedSuggestions: apply_suggestions
      });

      res.json({
        success: true,
        message: 'AI建议应用成功',
        data: {
          returnRequest,
          applied_fields: Object.keys(updatedFields)
        }
      });

    } catch (error) {
      logger.error('应用AI建议失败', error);

      res.status(500).json({
        success: false,
        message: '应用AI建议失败'
      });
    }
  }

  /**
   * 批量审核退货申请
   */
  async batchReviewReturnRequests(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;
      const { request_ids, action, refund_method, notes } = req.body;

      logger.info('批量审核退货申请', {
        userId,
        requestCount: request_ids?.length,
        action
      });

      // 验证输入
      if (!request_ids || !Array.isArray(request_ids) || request_ids.length === 0) {
        return res.status(400).json({
          success: false,
          message: '退货申请ID列表不能为空'
        });
      }

      if (!action || !['approve', 'reject'].includes(action)) {
        return res.status(400).json({
          success: false,
          message: '审核动作必须是 approve 或 reject'
        });
      }

      // 获取所有退货申请
      const returnRequests = await ReturnRequest.find({
        _id: { $in: request_ids },
        status: { $in: ['submitted', 'under_review'] }
      });

      if (returnRequests.length === 0) {
        return res.status(404).json({
          success: false,
          message: '没有找到可审核的退货申请'
        });
      }

      const results = {
        success: [],
        failed: [],
        total: returnRequests.length
      };

      // 批量处理
      for (const returnRequest of returnRequests) {
        try {
          const beforeData = returnRequest.toJSON();

          if (action === 'approve') {
            // 使用AI分析的建议退款金额，如果没有则使用总金额
            const refundAmount = returnRequest.ai_analysis?.overall_analysis?.total_refund_amount ||
                               returnRequest.total_amount;

            returnRequest.approve(userId, refundAmount, refund_method, notes);
          } else {
            returnRequest.reject(userId, notes);
          }

          await returnRequest.save();

          // 记录操作日志
          await OperationLog.logDataChange(
            userId,
            'return.batch_review',
            'return_request',
            returnRequest._id.toString(),
            beforeData,
            returnRequest.toJSON(),
            {
              action: 'update',
              ip_address: req.ip,
              user_agent: req.get('User-Agent'),
              request_method: req.method,
              request_url: req.originalUrl,
              response_status: 200,
              description: `批量${action === 'approve' ? '审核通过' : '审核拒绝'}: ${returnRequest.request_number}`
            }
          );

          results.success.push({
            id: returnRequest._id,
            request_number: returnRequest.request_number,
            status: returnRequest.status
          });

        } catch (error) {
          logger.error(`批量审核失败 - ${returnRequest.request_number}:`, error);
          results.failed.push({
            id: returnRequest._id,
            request_number: returnRequest.request_number,
            error: error.message
          });
        }
      }

      const responseTime = Date.now() - startTime;

      // 记录整体操作日志
      await OperationLog.logSuccess(
        userId,
        'return.batch_review',
        'return_request',
        'batch',
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            total_requests: results.total,
            successful: results.success.length,
            failed: results.failed.length,
            action
          },
          description: `批量审核退货申请: ${results.success.length}/${results.total}成功`
        }
      );

      res.json({
        success: true,
        message: `批量审核完成: ${results.success.length}/${results.total}成功`,
        data: results
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('批量审核退货申请错误:', error);

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'return.batch_review',
        'return_request',
        'batch',
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '批量审核退货申请失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '批量审核失败',
        error: error.message
      });
    }
  }

  /**
   * 自动化审核规则配置
   */
  async configureAutomationRules(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;
      const { rules } = req.body;

      logger.info('配置自动化审核规则', { userId, rulesCount: rules?.length });

      // 验证规则格式
      if (!rules || !Array.isArray(rules)) {
        return res.status(400).json({
          success: false,
          message: '规则配置格式无效'
        });
      }

      // 验证每个规则
      for (const rule of rules) {
        if (!rule.name || !rule.conditions || !rule.action) {
          return res.status(400).json({
            success: false,
            message: '每个规则必须包含名称、条件和动作'
          });
        }
      }

      // 保存规则配置（这里简化处理，实际应该保存到数据库）
      const automationConfig = {
        rules,
        updated_by: userId,
        updated_at: new Date(),
        enabled: true
      };

      // 这里应该保存到配置表或文件
      // await AutomationConfig.findOneAndUpdate({type: 'return_review'}, automationConfig, {upsert: true});

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'return.configure_automation',
        'automation_config',
        'return_review',
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            rules_count: rules.length,
            enabled: true
          },
          description: `配置自动化审核规则: ${rules.length}条`
        }
      );

      res.json({
        success: true,
        message: '自动化规则配置成功',
        data: { automationConfig }
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('配置自动化审核规则错误:', error);

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'return.configure_automation',
        'automation_config',
        'return_review',
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '配置自动化审核规则失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '配置自动化规则失败',
        error: error.message
      });
    }
  }

  /**
   * 执行自动化审核
   */
  async executeAutomatedReview(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;
      const { dry_run = false } = req.query;

      logger.info('执行自动化审核', { userId, dryRun: dry_run });

      // 获取待审核的退货申请
      const pendingRequests = await ReturnRequest.find({
        status: { $in: ['submitted', 'under_review'] },
        ai_analysis: { $exists: true, $ne: null }
      }).limit(100); // 限制处理数量

      if (pendingRequests.length === 0) {
        return res.json({
          success: true,
          message: '没有待自动审核的退货申请',
          data: { processed: 0, results: [] }
        });
      }

      const results = {
        processed: 0,
        auto_approved: 0,
        auto_rejected: 0,
        manual_review: 0,
        failed: 0,
        details: []
      };

      // 自动化规则
      const automationRules = this.getAutomationRules();

      for (const returnRequest of pendingRequests) {
        try {
          const decision = this.evaluateAutomationRules(returnRequest, automationRules);

          if (!dry_run && decision.action !== 'manual_review') {
            const beforeData = returnRequest.toJSON();

            if (decision.action === 'auto_approve') {
              returnRequest.approve(
                userId,
                decision.refund_amount,
                decision.refund_method || 'original',
                `自动审核通过: ${decision.reason}`
              );
              results.auto_approved++;
            } else if (decision.action === 'auto_reject') {
              returnRequest.reject(userId, `自动审核拒绝: ${decision.reason}`);
              results.auto_rejected++;
            }

            await returnRequest.save();

            // 记录操作日志
            await OperationLog.logDataChange(
              userId,
              'return.automated_review',
              'return_request',
              returnRequest._id.toString(),
              beforeData,
              returnRequest.toJSON(),
              {
                action: 'update',
                description: `自动化审核: ${decision.action} - ${decision.reason}`
              }
            );
          } else {
            results.manual_review++;
          }

          results.details.push({
            request_number: returnRequest.request_number,
            decision: decision.action,
            reason: decision.reason,
            confidence: decision.confidence,
            dry_run: dry_run
          });

          results.processed++;

        } catch (error) {
          logger.error(`自动化审核失败 - ${returnRequest.request_number}:`, error);
          results.failed++;
          results.details.push({
            request_number: returnRequest.request_number,
            decision: 'failed',
            reason: error.message,
            dry_run: dry_run
          });
        }
      }

      const responseTime = Date.now() - startTime;

      // 记录整体操作日志
      await OperationLog.logSuccess(
        userId,
        'return.automated_review',
        'return_request',
        'automation',
        {
          action: 'execute',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            processed: results.processed,
            auto_approved: results.auto_approved,
            auto_rejected: results.auto_rejected,
            manual_review: results.manual_review,
            dry_run: dry_run
          },
          description: `自动化审核执行: 处理${results.processed}个申请`
        }
      );

      res.json({
        success: true,
        message: `自动化审核${dry_run ? '模拟' : ''}完成`,
        data: results
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('执行自动化审核错误:', error);

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'return.automated_review',
        'return_request',
        'automation',
        {
          action: 'execute',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '执行自动化审核失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '自动化审核执行失败',
        error: error.message
      });
    }
  }

  /**
   * 获取自动化规则
   */
  getAutomationRules() {
    // 默认自动化规则
    return [
      {
        name: '高置信度自动通过',
        conditions: {
          ai_confidence: { $gte: 0.9 },
          ai_decision: 'approve',
          risk_level: { $in: ['low', 'medium'] },
          total_amount: { $lte: 500 }
        },
        action: 'auto_approve',
        priority: 1
      },
      {
        name: '明显违规自动拒绝',
        conditions: {
          ai_confidence: { $gte: 0.8 },
          ai_decision: 'reject',
          policy_violations_count: { $gt: 0 }
        },
        action: 'auto_reject',
        priority: 2
      },
      {
        name: '高风险人工审核',
        conditions: {
          $or: [
            { risk_level: 'high' },
            { total_amount: { $gt: 1000 } },
            { ai_confidence: { $lt: 0.7 } }
          ]
        },
        action: 'manual_review',
        priority: 3
      }
    ];
  }

  /**
   * 评估自动化规则
   */
  evaluateAutomationRules(returnRequest, rules) {
    const aiAnalysis = returnRequest.ai_analysis;

    if (!aiAnalysis || !aiAnalysis.success) {
      return {
        action: 'manual_review',
        reason: '缺少AI分析结果',
        confidence: 0
      };
    }

    const context = {
      ai_confidence: aiAnalysis.overall_analysis?.confidence || 0,
      ai_decision: aiAnalysis.overall_analysis?.overall_decision,
      risk_level: aiAnalysis.overall_analysis?.risk_level,
      total_amount: returnRequest.total_amount,
      policy_violations_count: aiAnalysis.overall_analysis?.policy_violations_count || 0,
      quality_issues_count: aiAnalysis.overall_analysis?.quality_issues_count || 0
    };

    // 按优先级排序规则
    const sortedRules = rules.sort((a, b) => a.priority - b.priority);

    for (const rule of sortedRules) {
      if (this.evaluateConditions(context, rule.conditions)) {
        return {
          action: rule.action,
          reason: rule.name,
          confidence: context.ai_confidence,
          refund_amount: aiAnalysis.overall_analysis?.total_refund_amount,
          refund_method: aiAnalysis.suggestions?.recommended_refund_method
        };
      }
    }

    // 默认需要人工审核
    return {
      action: 'manual_review',
      reason: '未匹配任何自动化规则',
      confidence: context.ai_confidence
    };
  }

  /**
   * 评估条件
   */
  evaluateConditions(context, conditions) {
    for (const [key, condition] of Object.entries(conditions)) {
      if (key === '$or') {
        // OR条件
        const orResult = condition.some(orCondition =>
          this.evaluateConditions(context, orCondition)
        );
        if (!orResult) return false;
      } else if (key === '$and') {
        // AND条件
        const andResult = condition.every(andCondition =>
          this.evaluateConditions(context, andCondition)
        );
        if (!andResult) return false;
      } else {
        // 单个条件
        const value = context[key];
        if (!this.evaluateSingleCondition(value, condition)) {
          return false;
        }
      }
    }
    return true;
  }

  /**
   * 评估单个条件
   */
  evaluateSingleCondition(value, condition) {
    if (typeof condition === 'object') {
      for (const [operator, operand] of Object.entries(condition)) {
        switch (operator) {
          case '$gte':
            if (!(value >= operand)) return false;
            break;
          case '$lte':
            if (!(value <= operand)) return false;
            break;
          case '$gt':
            if (!(value > operand)) return false;
            break;
          case '$lt':
            if (!(value < operand)) return false;
            break;
          case '$eq':
            if (value !== operand) return false;
            break;
          case '$ne':
            if (value === operand) return false;
            break;
          case '$in':
            if (!operand.includes(value)) return false;
            break;
          case '$nin':
            if (operand.includes(value)) return false;
            break;
          default:
            return false;
        }
      }
    } else {
      // 直接比较
      if (value !== condition) return false;
    }
    return true;
  }
}

module.exports = new ReturnController();
