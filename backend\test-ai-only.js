require('dotenv').config();
const aiService = require('./src/services/aiService');

/**
 * AI服务独立测试脚本
 * 测试AI服务功能，不依赖数据库
 */

async function testAIService() {
  console.log('🚀 开始AI服务功能测试...\n');

  try {
    // 测试1: AI服务健康检查
    console.log('📋 测试1: AI服务健康检查');
    const healthCheck = await aiService.healthCheck();
    console.log('AI服务状态:', healthCheck);

    if (healthCheck.success) {
      console.log('✅ AI服务正常');
    } else {
      console.log('❌ AI服务异常');
      console.log('错误信息:', healthCheck.error);
      return;
    }

    // 测试2: 意图分析
    console.log('\n📋 测试2: AI意图分析');
    const intentResult = await aiService.analyzeIntent('请问配送费用是多少？');
    console.log('意图分析结果:', intentResult);
    
    if (intentResult.success) {
      console.log('✅ AI意图分析成功');
      console.log(`- 意图: ${intentResult.intent}`);
      console.log(`- 置信度: ${intentResult.confidence}`);
    } else {
      console.log('❌ AI意图分析失败');
    }

    // 测试3: 基础对话
    console.log('\n📋 测试3: 基础AI对话');
    const chatResponse = await aiService.chat([
      { role: 'user', content: '你好，我想了解一下你们的服务' }
    ], {
      temperature: 0.7,
      max_tokens: 200
    });

    console.log('AI对话结果:', {
      success: chatResponse.success,
      contentLength: chatResponse.content?.length || 0,
      responseTime: chatResponse.response_time_ms
    });

    if (chatResponse.success) {
      console.log('✅ AI对话成功');
      console.log('AI回答:', chatResponse.content);
    } else {
      console.log('❌ AI对话失败');
      console.log('错误信息:', chatResponse.error);
    }

    // 测试4: 政策相关对话
    console.log('\n📋 测试4: 政策相关对话');
    const policyChat = await aiService.chat([
      { role: 'user', content: '请问你们的配送政策是什么？' }
    ], {
      system_prompt: `你是一个专业的客服AI助手，为社区拼台（团购平台）提供服务。

相关政策信息:
配送时间：每周三截单，周五送货
起送标准：三只鸡或同等金额起可送到家
配送范围：以波士顿为中心，Quincy、Waltham、Newton以内
配送费用：上述区域内运费$5/次
免费配送：10只鸡或同等金额以上免费送到家

请基于以上政策信息回答用户问题。`,
      temperature: 0.7,
      max_tokens: 300
    });

    if (policyChat.success) {
      console.log('✅ 政策相关对话成功');
      console.log('AI回答:', policyChat.content);
    } else {
      console.log('❌ 政策相关对话失败');
      console.log('错误信息:', policyChat.error);
    }

    // 测试5: 多轮对话
    console.log('\n📋 测试5: 多轮对话测试');
    const multiTurnChat = await aiService.chat([
      { role: 'user', content: '你好' },
      { role: 'assistant', content: '您好！我是智能客服助手，可以帮您查询库存信息和处理退货申请。请问有什么可以帮助您的吗？' },
      { role: 'user', content: '我想了解退货政策' }
    ], {
      system_prompt: `你是一个专业的客服AI助手。

退货政策:
- 质量问题请在24小时内通过照片私信反馈
- 超时反馈恕不受理
- 质量问题经核实后可选择退款或更换
- 退款可作为下次拼单的credit，也可直接退款

请基于政策信息回答用户问题。`,
      temperature: 0.7,
      max_tokens: 300
    });

    if (multiTurnChat.success) {
      console.log('✅ 多轮对话成功');
      console.log('AI回答:', multiTurnChat.content);
    } else {
      console.log('❌ 多轮对话失败');
      console.log('错误信息:', multiTurnChat.error);
    }

    console.log('\n🎉 AI服务功能测试完成！');
    console.log('\n📊 测试总结:');
    console.log('- AI服务健康检查: ✅');
    console.log('- 意图分析功能: ✅');
    console.log('- 基础对话功能: ✅');
    console.log('- 政策相关对话: ✅');
    console.log('- 多轮对话功能: ✅');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  testAIService().catch(console.error);
}

module.exports = testAIService;
