const alertService = require('../services/alertService');
const cronService = require('../services/cronService');
const { OperationLog } = require('../models');
const logger = require('../utils/logger');

/**
 * 预警控制器
 * 处理预警查询、配置管理等操作
 */
class AlertController {
  /**
   * 获取所有预警
   */
  async getAllAlerts(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const { priority, type, limit = 50 } = req.query;

      logger.info('获取预警列表', {
        userId,
        priority,
        type,
        limit
      });

      // 检查所有预警
      const result = await alertService.checkAllAlerts();

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '获取预警失败',
          error: result.error
        });
      }

      let { alerts, summary } = result.data;

      // 根据查询参数过滤预警
      if (priority) {
        alerts = alerts.filter(alert => alert.priority === priority);
      }

      if (type) {
        alerts = alerts.filter(alert => alert.type === type);
      }

      // 限制返回数量
      if (limit && alerts.length > parseInt(limit)) {
        alerts = alerts.slice(0, parseInt(limit));
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'alert.view',
        'alert',
        'all',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            totalAlerts: summary.total,
            filteredAlerts: alerts.length,
            priority,
            type
          },
          description: `查看预警列表: ${alerts.length}条`
        }
      );

      res.json({
        success: true,
        data: {
          alerts,
          summary,
          filters: {
            priority,
            type,
            limit: parseInt(limit)
          },
          checkTime: result.data.checkTime
        }
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('获取预警列表失败', {
        userId: req.user?.userId,
        error: error.message,
        stack: error.stack
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'alert.view',
          'alert',
          'all',
          {
            action: 'read',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '获取预警列表失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '获取预警失败',
        error: error.message
      });
    }
  }

  /**
   * 获取预警摘要
   */
  async getAlertSummary(req, res) {
    try {
      const userId = req.user.userId;

      logger.info('获取预警摘要', { userId });

      const result = await alertService.checkAllAlerts();

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '获取预警摘要失败',
          error: result.error
        });
      }

      const { summary, checkTime } = result.data;

      res.json({
        success: true,
        data: {
          summary,
          checkTime
        }
      });

    } catch (error) {
      logger.error('获取预警摘要失败', {
        userId: req.user?.userId,
        error: error.message
      });

      res.status(500).json({
        success: false,
        message: '获取预警摘要失败',
        error: error.message
      });
    }
  }

  /**
   * 获取特定类型的预警
   */
  async getAlertsByType(req, res) {
    try {
      const { type } = req.params;
      const userId = req.user.userId;

      logger.info('获取特定类型预警', {
        userId,
        type
      });

      const result = await alertService.checkAllAlerts();

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '获取预警失败',
          error: result.error
        });
      }

      const alerts = result.data.alerts.filter(alert => alert.type === type);

      res.json({
        success: true,
        data: {
          alerts,
          type,
          count: alerts.length,
          checkTime: result.data.checkTime
        }
      });

    } catch (error) {
      logger.error('获取特定类型预警失败', {
        userId: req.user?.userId,
        type: req.params?.type,
        error: error.message
      });

      res.status(500).json({
        success: false,
        message: '获取预警失败',
        error: error.message
      });
    }
  }

  /**
   * 手动触发预警检查
   */
  async triggerAlertCheck(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;

      logger.info('手动触发预警检查', { userId });

      const result = await alertService.checkAllAlerts();

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'alert.trigger_check',
        'alert',
        'manual',
        {
          action: 'execute',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: result.success ? 200 : 500,
          response_time_ms: responseTime,
          after_data: result.success ? {
            totalAlerts: result.data.summary.total,
            critical: result.data.summary.critical,
            high: result.data.summary.high
          } : null,
          error_message: result.success ? null : result.error,
          description: '手动触发预警检查'
        }
      );

      if (result.success) {
        res.json({
          success: true,
          data: result.data,
          message: '预警检查完成'
        });
      } else {
        res.status(500).json({
          success: false,
          message: '预警检查失败',
          error: result.error
        });
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('手动触发预警检查失败', {
        userId: req.user?.userId,
        error: error.message
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'alert.trigger_check',
          'alert',
          'manual',
          {
            action: 'execute',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '手动触发预警检查失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '预警检查失败',
        error: error.message
      });
    }
  }

  /**
   * 获取预警配置
   */
  async getAlertConfig(req, res) {
    try {
      const userId = req.user.userId;

      logger.info('获取预警配置', { userId });

      const config = alertService.getAlertConfig();

      res.json({
        success: true,
        data: config
      });

    } catch (error) {
      logger.error('获取预警配置失败', {
        userId: req.user?.userId,
        error: error.message
      });

      res.status(500).json({
        success: false,
        message: '获取预警配置失败',
        error: error.message
      });
    }
  }

  /**
   * 更新预警配置
   */
  async updateAlertConfig(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const config = req.body;

      logger.info('更新预警配置', {
        userId,
        config
      });

      const result = alertService.updateAlertConfig(config);

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'alert.update_config',
        'alert',
        'config',
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: result.success ? 200 : 400,
          response_time_ms: responseTime,
          before_data: alertService.getAlertConfig(),
          after_data: result.success ? result.config : null,
          error_message: result.success ? null : result.error,
          description: '更新预警配置'
        }
      );

      if (result.success) {
        res.json({
          success: true,
          data: result.config,
          message: '预警配置更新成功'
        });
      } else {
        res.status(400).json({
          success: false,
          message: '预警配置更新失败',
          error: result.error
        });
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('更新预警配置失败', {
        userId: req.user?.userId,
        error: error.message
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'alert.update_config',
          'alert',
          'config',
          {
            action: 'update',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '更新预警配置失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '更新预警配置失败',
        error: error.message
      });
    }
  }

  /**
   * 获取定时任务状态
   */
  async getCronStatus(req, res) {
    try {
      const userId = req.user.userId;

      logger.info('获取定时任务状态', { userId });

      const status = cronService.getTaskStatus();

      res.json({
        success: true,
        data: status
      });

    } catch (error) {
      logger.error('获取定时任务状态失败', {
        userId: req.user?.userId,
        error: error.message
      });

      res.status(500).json({
        success: false,
        message: '获取定时任务状态失败',
        error: error.message
      });
    }
  }

  /**
   * 手动执行定时任务
   */
  async runCronTask(req, res) {
    try {
      const { taskId } = req.params;
      const userId = req.user.userId;

      logger.info('手动执行定时任务', {
        userId,
        taskId
      });

      const result = await cronService.runTaskManually(taskId);

      if (result.success) {
        res.json({
          success: true,
          message: result.message
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.error
        });
      }

    } catch (error) {
      logger.error('手动执行定时任务失败', {
        userId: req.user?.userId,
        taskId: req.params?.taskId,
        error: error.message
      });

      res.status(500).json({
        success: false,
        message: '执行定时任务失败',
        error: error.message
      });
    }
  }

  /**
   * 获取智能预警分析
   */
  async getIntelligentAnalysis(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;
      const {
        days_back = 30,
        prediction_days = 7,
        include_trends = true,
        include_recommendations = true
      } = req.query;

      logger.info('获取智能预警分析', {
        userId,
        days_back,
        prediction_days,
        include_trends,
        include_recommendations
      });

      const result = await alertService.performIntelligentAnalysis({
        days_back: parseInt(days_back),
        prediction_days: parseInt(prediction_days),
        include_trends: include_trends === 'true',
        include_recommendations: include_recommendations === 'true'
      });

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '智能预警分析失败',
          error: result.error
        });
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'alert.intelligent_analysis',
        'alert',
        'analysis',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            trends_analyzed: Object.keys(result.data.trends || {}).length,
            predictions_generated: Object.keys(result.data.predictions || {}).length,
            recommendations: result.data.recommendations?.length || 0,
            risk_level: result.data.risk_assessment?.overall_risk_level
          },
          description: '获取智能预警分析'
        }
      );

      res.json({
        success: true,
        data: result.data,
        message: '智能预警分析完成'
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('获取智能预警分析失败', {
        userId: req.user?.userId,
        error: error.message,
        stack: error.stack
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'alert.intelligent_analysis',
          'alert',
          'analysis',
          {
            action: 'read',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '获取智能预警分析失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '智能预警分析失败',
        error: error.message
      });
    }
  }

  /**
   * 获取库存趋势分析
   */
  async getStockTrends(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;
      const { days_back = 30 } = req.query;

      logger.info('获取库存趋势分析', { userId, days_back });

      const trends = await alertService.analyzeStockTrends(parseInt(days_back));

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'alert.stock_trends',
        'alert',
        'trends',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            trends_count: Object.keys(trends).length,
            days_back: parseInt(days_back)
          },
          description: '获取库存趋势分析'
        }
      );

      res.json({
        success: true,
        data: {
          trends,
          analysis_period: parseInt(days_back),
          generated_at: new Date().toISOString()
        }
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('获取库存趋势分析失败', {
        userId: req.user?.userId,
        error: error.message
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'alert.stock_trends',
          'alert',
          'trends',
          {
            action: 'read',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '获取库存趋势分析失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '获取库存趋势分析失败',
        error: error.message
      });
    }
  }

  /**
   * 获取库存预测
   */
  async getStockPredictions(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;
      const {
        prediction_days = 7,
        days_back = 30
      } = req.query;

      logger.info('获取库存预测', {
        userId,
        prediction_days,
        days_back
      });

      // 先分析消费模式
      const consumptionPatterns = await alertService.analyzeConsumptionPatterns(
        parseInt(days_back)
      );

      // 生成预测
      const predictions = await alertService.generateStockPredictions(
        consumptionPatterns,
        parseInt(prediction_days)
      );

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'alert.stock_predictions',
        'alert',
        'predictions',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            predictions_count: Object.keys(predictions).length,
            prediction_days: parseInt(prediction_days),
            high_risk_items: Object.values(predictions).filter(p => p.stockout_risk === 'high').length
          },
          description: '获取库存预测'
        }
      );

      res.json({
        success: true,
        data: {
          predictions,
          consumption_patterns: consumptionPatterns,
          prediction_period: parseInt(prediction_days),
          analysis_period: parseInt(days_back),
          generated_at: new Date().toISOString()
        }
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('获取库存预测失败', {
        userId: req.user?.userId,
        error: error.message
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'alert.stock_predictions',
          'alert',
          'predictions',
          {
            action: 'read',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '获取库存预测失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '获取库存预测失败',
        error: error.message
      });
    }
  }

  /**
   * 获取风险评估
   */
  async getRiskAssessment(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;

      logger.info('获取风险评估', { userId });

      const riskAssessment = await alertService.assessInventoryRisks();

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'alert.risk_assessment',
        'alert',
        'risk',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            overall_risk_level: riskAssessment.overall_risk_level,
            high_risk_items: riskAssessment.high_risk_items,
            medium_risk_items: riskAssessment.medium_risk_items
          },
          description: '获取风险评估'
        }
      );

      res.json({
        success: true,
        data: {
          risk_assessment: riskAssessment,
          generated_at: new Date().toISOString()
        }
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('获取风险评估失败', {
        userId: req.user?.userId,
        error: error.message
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'alert.risk_assessment',
          'alert',
          'risk',
          {
            action: 'read',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '获取风险评估失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '获取风险评估失败',
        error: error.message
      });
    }
  }
}

module.exports = new AlertController();
