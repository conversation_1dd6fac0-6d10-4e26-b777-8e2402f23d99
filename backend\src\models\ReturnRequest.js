const mongoose = require('mongoose');

const returnItemSchema = new mongoose.Schema({
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  },
  product_name: {
    type: String,
    required: [true, '产品名称是必需的'],
    trim: true
  },
  quantity: {
    type: Number,
    required: [true, '退货数量是必需的'],
    min: [1, '退货数量必须大于0']
  },
  unit_price: {
    type: Number,
    required: [true, '单价是必需的'],
    min: [0, '单价不能为负数']
  },
  total_amount: {
    type: Number,
    required: [true, '总金额是必需的'],
    min: [0, '总金额不能为负数']
  },
  reason: {
    type: String,
    required: [true, '退货原因是必需的'],
    enum: ['quality_issue', 'wrong_item', 'damaged', 'expired', 'customer_change', 'other'],
    index: true
  },
  condition: {
    type: String,
    enum: ['unopened', 'opened', 'damaged', 'expired'],
    required: [true, '商品状态是必需的']
  },
  photos: [{
    url: {
      type: String,
      required: true
    },
    description: {
      type: String,
      trim: true,
      maxlength: [200, '图片描述不能超过200个字符']
    },
    uploaded_at: {
      type: Date,
      default: Date.now
    }
  }],
  notes: {
    type: String,
    trim: true,
    maxlength: [500, '备注不能超过500个字符']
  }
}, { _id: false });

const returnRequestSchema = new mongoose.Schema({
  request_number: {
    type: String,
    unique: true,
    index: true
  },
  customer_info: {
    name: {
      type: String,
      required: [true, '客户姓名是必需的'],
      trim: true,
      maxlength: [50, '客户姓名不能超过50个字符']
    },
    phone: {
      type: String,
      required: [true, '客户电话是必需的'],
      trim: true,
      match: [/^1[3-9]\d{9}$/, '请输入有效的手机号码']
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
    },
    address: {
      type: String,
      trim: true,
      maxlength: [200, '地址不能超过200个字符']
    }
  },
  purchase_info: {
    order_number: {
      type: String,
      trim: true,
      maxlength: [50, '订单号不能超过50个字符']
    },
    purchase_date: {
      type: Date,
      required: [true, '购买日期是必需的'],
      index: true
    },
    pickup_date: {
      type: Date,
      index: true
    },
    payment_method: {
      type: String,
      enum: ['cash', 'wechat', 'alipay', 'venmo', 'credit_card', 'other'],
      default: 'cash'
    }
  },
  items: {
    type: [returnItemSchema],
    required: [true, '退货商品列表是必需的'],
    validate: {
      validator: function(items) {
        return items && items.length > 0;
      },
      message: '至少需要一个退货商品'
    }
  },
  total_quantity: {
    type: Number,
    min: [0, '总数量不能为负数'],
    default: 0
  },
  total_amount: {
    type: Number,
    min: [0, '总金额不能为负数'],
    default: 0
  },
  status: {
    type: String,
    enum: ['submitted', 'under_review', 'approved', 'rejected', 'processing', 'completed', 'cancelled'],
    default: 'submitted',
    index: true
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal',
    index: true
  },
  refund_method: {
    type: String,
    enum: ['original_payment', 'store_credit', 'venmo', 'cash', 'exchange'],
    default: 'original_payment'
  },
  refund_amount: {
    type: Number,
    min: [0, '退款金额不能为负数'],
    default: 0
  },
  processing_notes: {
    type: String,
    trim: true,
    maxlength: [1000, '处理备注不能超过1000个字符']
  },
  timeline: [{
    status: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    operator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: {
      type: String,
      trim: true,
      maxlength: [500, '备注不能超过500个字符']
    }
  }],
  chat_session_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ChatSession'
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  assigned_to: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  reviewed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewed_at: {
    type: Date,
    index: true
  },
  completed_at: {
    type: Date,
    index: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 生成退货申请号
returnRequestSchema.statics.generateRequestNumber = function() {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const timestamp = now.getTime().toString().slice(-6);
  
  return `RT${year}${month}${day}${timestamp}`;
};

// 计算总数量和总金额
returnRequestSchema.methods.calculateTotals = function() {
  this.total_quantity = this.items.reduce((sum, item) => sum + item.quantity, 0);
  this.total_amount = this.items.reduce((sum, item) => sum + item.total_amount, 0);
  return { total_quantity: this.total_quantity, total_amount: this.total_amount };
};

// 检查是否在退货时限内
returnRequestSchema.methods.isWithinReturnPeriod = function(hoursLimit = 24) {
  if (!this.purchase_info.pickup_date) return true; // 如果没有取货日期，默认允许
  
  const now = new Date();
  const pickupDate = new Date(this.purchase_info.pickup_date);
  const diffHours = (now - pickupDate) / (1000 * 60 * 60);
  
  return diffHours <= hoursLimit;
};

// 添加状态变更记录
returnRequestSchema.methods.addStatusChange = function(newStatus, operator, notes) {
  this.status = newStatus;
  this.timeline.push({
    status: newStatus,
    timestamp: new Date(),
    operator: operator,
    notes: notes
  });
  
  // 更新相关时间戳
  if (newStatus === 'approved' || newStatus === 'rejected') {
    this.reviewed_by = operator;
    this.reviewed_at = new Date();
  } else if (newStatus === 'completed') {
    this.completed_at = new Date();
  }
  
  return this;
};

// 审核通过
returnRequestSchema.methods.approve = function(operator, refundAmount, refundMethod, notes) {
  if (this.status !== 'under_review' && this.status !== 'submitted') {
    throw new Error('只能审核待审核或已提交状态的退货申请');
  }
  
  this.refund_amount = refundAmount || this.total_amount;
  this.refund_method = refundMethod || this.refund_method;
  this.processing_notes = notes;
  
  this.addStatusChange('approved', operator, notes);
  
  return this;
};

// 审核拒绝
returnRequestSchema.methods.reject = function(operator, reason) {
  if (this.status !== 'under_review' && this.status !== 'submitted') {
    throw new Error('只能审核待审核或已提交状态的退货申请');
  }
  
  this.processing_notes = reason;
  this.addStatusChange('rejected', operator, reason);
  
  return this;
};

// 复合索引
returnRequestSchema.index({ status: 1, createdAt: -1 });
returnRequestSchema.index({ 'customer_info.phone': 1 });
returnRequestSchema.index({ 'purchase_info.purchase_date': -1 });
returnRequestSchema.index({ priority: 1, status: 1 });
returnRequestSchema.index({ assigned_to: 1, status: 1 });

// 中间件：保存前生成申请号和计算总计
returnRequestSchema.pre('save', function(next) {
  if (this.isNew) {
    if (!this.request_number) {
      this.request_number = this.constructor.generateRequestNumber();
    }

    // 添加初始状态记录
    if (this.timeline.length === 0) {
      this.timeline.push({
        status: this.status,
        timestamp: new Date(),
        operator: this.created_by,
        notes: '退货申请已提交'
      });
    }
  }

  // 计算总计
  if (this.items && this.items.length > 0) {
    this.calculateTotals();
  }

  next();
});

module.exports = mongoose.model('ReturnRequest', returnRequestSchema);
