const express = require('express');
const authController = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

// @desc    用户登录
// @route   POST /api/auth/login
// @access  Public
router.post('/login', authController.login);

// @desc    用户注册
// @route   POST /api/auth/register
// @access  Public
router.post('/register', authController.register);

// @desc    获取当前用户信息
// @route   GET /api/auth/me
// @access  Private
router.get('/me', authenticateToken, authController.getCurrentUser);

// @desc    修改密码
// @route   PUT /api/auth/password
// @access  Private
router.put('/password', authenticateToken, authController.changePassword);

// @desc    用户登出
// @route   POST /api/auth/logout
// @access  Private
router.post('/logout', authenticateToken, authController.logout);

module.exports = router;
