const express = require('express');
const fileController = require('../controllers/fileController');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { 
  singleFileUpload, 
  multipleFileUpload, 
  validateUploadedFiles 
} = require('../middleware/fileUpload');

const router = express.Router();

// 所有文件路由都需要认证
router.use(authenticateToken);

/**
 * @desc    单文件上传
 * @route   POST /api/files/upload
 * @access  Private
 * @body    file: 文件, folder: 存储文件夹(可选), compress: 是否压缩(可选)
 */
router.post('/upload', 
  requirePermission('files.write'),
  singleFileUpload('file'),
  validateUploadedFiles,
  fileController.uploadSingleFile
);

/**
 * @desc    多文件上传
 * @route   POST /api/files/upload-multiple
 * @access  Private
 * @body    files: 文件数组, folder: 存储文件夹(可选), compress: 是否压缩(可选)
 */
router.post('/upload-multiple',
  requirePermission('files.write'),
  multipleFileUpload('files', 10),
  validateUploadedFiles,
  fileController.uploadMultipleFiles
);

/**
 * @desc    获取文件访问URL
 * @route   GET /api/files/:key/url
 * @access  Private
 * @params  key: 文件键
 * @query   expires: 过期时间(秒，默认3600)
 */
router.get('/:key/url',
  requirePermission('files.read'),
  fileController.getFileUrl
);

/**
 * @desc    删除文件
 * @route   DELETE /api/files/:key
 * @access  Private
 * @params  key: 文件键
 */
router.delete('/:key',
  requirePermission('files.delete'),
  fileController.deleteFile
);

module.exports = router;
