const { Policy, Product, Inventory } = require('../models');
const policyService = require('./policyService');
const logger = require('../utils/logger');

/**
 * RAG检索服务类
 * 实现检索增强生成，为AI提供相关上下文信息
 */
class RAGService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 10 * 60 * 1000; // 10分钟缓存
  }

  /**
   * 检索相关信息
   * @param {string} query - 查询内容
   * @param {Object} options - 检索选项
   * @returns {Promise<Object>} 检索结果
   */
  async retrieve(query, options = {}) {
    try {
      const {
        includePolicy = true,
        includeProduct = true,
        includeInventory = true,
        limit = 5
      } = options;

      const results = {
        policies: [],
        products: [],
        inventory: [],
        relevanceScore: 0
      };

      // 并行检索不同类型的信息
      const promises = [];

      if (includePolicy) {
        promises.push(this.retrievePolicies(query, limit));
      }

      if (includeProduct) {
        promises.push(this.retrieveProducts(query, limit));
      }

      if (includeInventory) {
        promises.push(this.retrieveInventory(query, limit));
      }

      const [policies, products, inventory] = await Promise.all(promises);

      if (policies) results.policies = policies;
      if (products) results.products = products;
      if (inventory) results.inventory = inventory;

      // 计算整体相关性分数
      results.relevanceScore = this.calculateRelevanceScore(results);

      logger.info('RAG检索完成', {
        query,
        policyCount: results.policies.length,
        productCount: results.products.length,
        inventoryCount: results.inventory.length,
        relevanceScore: results.relevanceScore
      });

      return results;

    } catch (error) {
      logger.error('RAG检索失败', error);
      return {
        policies: [],
        products: [],
        inventory: [],
        relevanceScore: 0,
        error: error.message
      };
    }
  }

  /**
   * 检索相关政策
   * @param {string} query - 查询内容
   * @param {number} limit - 结果限制
   * @returns {Promise<Array>} 政策列表
   */
  async retrievePolicies(query, limit = 5) {
    try {
      const cacheKey = `policies_${query}_${limit}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      // 使用政策服务搜索
      const policies = await policyService.searchPolicies(query, {
        activeOnly: true,
        limit
      });

      // 增强政策信息，添加相关性分数
      const enhancedPolicies = policies.map(policy => {
        const relevanceScore = this.calculatePolicyRelevance(query, policy);
        return {
          ...policy.toObject(),
          relevanceScore,
          type: 'policy'
        };
      });

      // 按相关性排序
      enhancedPolicies.sort((a, b) => b.relevanceScore - a.relevanceScore);

      this.setCache(cacheKey, enhancedPolicies);
      return enhancedPolicies;

    } catch (error) {
      logger.error('检索政策失败', error);
      return [];
    }
  }

  /**
   * 检索相关产品
   * @param {string} query - 查询内容
   * @param {number} limit - 结果限制
   * @returns {Promise<Array>} 产品列表
   */
  async retrieveProducts(query, limit = 5) {
    try {
      const cacheKey = `products_${query}_${limit}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      // 构建产品搜索查询
      const searchRegex = new RegExp(query, 'i');
      const products = await Product.find({
        $and: [
          { is_active: true },
          {
            $or: [
              { name: searchRegex },
              { description: searchRegex },
              { category: searchRegex },
              { tags: { $in: [searchRegex] } }
            ]
          }
        ]
      })
      .limit(limit)
      .select('name description category price tags barcode')
      .lean();

      // 添加相关性分数
      const enhancedProducts = products.map(product => {
        const relevanceScore = this.calculateProductRelevance(query, product);
        return {
          ...product,
          relevanceScore,
          type: 'product'
        };
      });

      // 按相关性排序
      enhancedProducts.sort((a, b) => b.relevanceScore - a.relevanceScore);

      this.setCache(cacheKey, enhancedProducts);
      return enhancedProducts;

    } catch (error) {
      logger.error('检索产品失败', error);
      return [];
    }
  }

  /**
   * 检索相关库存信息
   * @param {string} query - 查询内容
   * @param {number} limit - 结果限制
   * @returns {Promise<Array>} 库存列表
   */
  async retrieveInventory(query, limit = 5) {
    try {
      const cacheKey = `inventory_${query}_${limit}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      // 首先搜索相关产品
      const searchRegex = new RegExp(query, 'i');
      const products = await Product.find({
        $and: [
          { is_active: true },
          {
            $or: [
              { name: searchRegex },
              { description: searchRegex },
              { category: searchRegex }
            ]
          }
        ]
      })
      .limit(limit)
      .select('_id name')
      .lean();

      if (products.length === 0) {
        return [];
      }

      // 获取这些产品的库存信息
      const productIds = products.map(p => p._id);
      const inventory = await Inventory.find({
        product_id: { $in: productIds }
      })
      .populate('product_id', 'name description category price')
      .lean();

      // 添加相关性分数和类型
      const enhancedInventory = inventory.map(item => {
        const relevanceScore = this.calculateInventoryRelevance(query, item);
        return {
          ...item,
          relevanceScore,
          type: 'inventory'
        };
      });

      // 按相关性排序
      enhancedInventory.sort((a, b) => b.relevanceScore - a.relevanceScore);

      this.setCache(cacheKey, enhancedInventory);
      return enhancedInventory;

    } catch (error) {
      logger.error('检索库存失败', error);
      return [];
    }
  }

  /**
   * 生成上下文信息
   * @param {Object} retrievalResults - 检索结果
   * @param {string} query - 原始查询
   * @returns {string} 格式化的上下文信息
   */
  generateContext(retrievalResults, query) {
    try {
      let context = `用户查询: "${query}"\n\n`;

      // 添加政策信息
      if (retrievalResults.policies && retrievalResults.policies.length > 0) {
        context += "相关政策信息:\n";
        retrievalResults.policies.forEach((policy, index) => {
          context += `${index + 1}. ${policy.name} (${policy.category})\n`;
          if (Array.isArray(policy.current_content)) {
            policy.current_content.slice(0, 3).forEach(item => {
              context += `   - ${item}\n`;
            });
          }
          context += "\n";
        });
      }

      // 添加产品信息
      if (retrievalResults.products && retrievalResults.products.length > 0) {
        context += "相关产品信息:\n";
        retrievalResults.products.forEach((product, index) => {
          context += `${index + 1}. ${product.name}\n`;
          context += `   类别: ${product.category}\n`;
          if (product.description) {
            context += `   描述: ${product.description}\n`;
          }
          context += "\n";
        });
      }

      // 添加库存信息
      if (retrievalResults.inventory && retrievalResults.inventory.length > 0) {
        context += "相关库存信息:\n";
        retrievalResults.inventory.forEach((item, index) => {
          const product = item.product_id;
          context += `${index + 1}. ${product.name}\n`;
          context += `   当前库存: ${item.current_quantity}\n`;
          context += `   安全库存: ${item.safety_stock}\n`;
          if (item.expiry_date) {
            context += `   过期日期: ${new Date(item.expiry_date).toLocaleDateString()}\n`;
          }
          context += "\n";
        });
      }

      return context;

    } catch (error) {
      logger.error('生成上下文失败', error);
      return `用户查询: "${query}"\n\n暂无相关信息。`;
    }
  }

  /**
   * 计算政策相关性分数
   * @param {string} query - 查询内容
   * @param {Object} policy - 政策对象
   * @returns {number} 相关性分数 (0-1)
   */
  calculatePolicyRelevance(query, policy) {
    let score = 0;
    const queryLower = query.toLowerCase();

    // 名称匹配
    if (policy.name.toLowerCase().includes(queryLower)) {
      score += 0.4;
    }

    // 类别匹配
    if (policy.category.toLowerCase().includes(queryLower)) {
      score += 0.3;
    }

    // 标签匹配
    if (policy.tags && policy.tags.some(tag => tag.toLowerCase().includes(queryLower))) {
      score += 0.2;
    }

    // 关键词匹配
    if (policy.keywords && policy.keywords.some(keyword => keyword.toLowerCase().includes(queryLower))) {
      score += 0.1;
    }

    // 内容匹配
    if (Array.isArray(policy.current_content)) {
      const contentMatch = policy.current_content.some(item => 
        typeof item === 'string' && item.toLowerCase().includes(queryLower)
      );
      if (contentMatch) {
        score += 0.3;
      }
    }

    return Math.min(score, 1.0);
  }

  /**
   * 计算产品相关性分数
   * @param {string} query - 查询内容
   * @param {Object} product - 产品对象
   * @returns {number} 相关性分数 (0-1)
   */
  calculateProductRelevance(query, product) {
    let score = 0;
    const queryLower = query.toLowerCase();

    // 名称匹配
    if (product.name.toLowerCase().includes(queryLower)) {
      score += 0.5;
    }

    // 类别匹配
    if (product.category && product.category.toLowerCase().includes(queryLower)) {
      score += 0.3;
    }

    // 描述匹配
    if (product.description && product.description.toLowerCase().includes(queryLower)) {
      score += 0.2;
    }

    return Math.min(score, 1.0);
  }

  /**
   * 计算库存相关性分数
   * @param {string} query - 查询内容
   * @param {Object} inventory - 库存对象
   * @returns {number} 相关性分数 (0-1)
   */
  calculateInventoryRelevance(query, inventory) {
    let score = 0;
    const queryLower = query.toLowerCase();

    if (inventory.product_id) {
      const product = inventory.product_id;
      
      // 产品名称匹配
      if (product.name && product.name.toLowerCase().includes(queryLower)) {
        score += 0.6;
      }

      // 产品类别匹配
      if (product.category && product.category.toLowerCase().includes(queryLower)) {
        score += 0.4;
      }
    }

    return Math.min(score, 1.0);
  }

  /**
   * 计算整体相关性分数
   * @param {Object} results - 检索结果
   * @returns {number} 整体相关性分数 (0-1)
   */
  calculateRelevanceScore(results) {
    let totalScore = 0;
    let itemCount = 0;

    // 计算所有项目的平均相关性分数
    ['policies', 'products', 'inventory'].forEach(type => {
      if (results[type] && results[type].length > 0) {
        const typeScore = results[type].reduce((sum, item) => sum + (item.relevanceScore || 0), 0);
        totalScore += typeScore;
        itemCount += results[type].length;
      }
    });

    return itemCount > 0 ? totalScore / itemCount : 0;
  }

  // 缓存管理
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearCache() {
    this.cache.clear();
  }
}

module.exports = new RAGService();
