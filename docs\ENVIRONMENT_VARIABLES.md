# 🔧 环境变量配置说明

本文档详细说明了项目中使用的所有环境变量及其配置方法。

## 📋 概述

项目分为前端和后端两部分，各自有独立的环境变量配置：

- **后端**: `backend/.env` - Node.js/Express 服务配置
- **前端**: `frontend/.env` - Vite/React 应用配置

## 🔧 后端环境变量 (backend/.env)

### 🚀 服务器配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `PORT` | `4000` | 后端服务运行端口 |
| `NODE_ENV` | `development` | 运行环境 (development/production) |

### 🗄️ 数据库配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MONGO_URI` | `mongodb://localhost:27017/inventory_ai_db` | MongoDB连接字符串 |

**配置示例**:
```bash
# 本地MongoDB
MONGO_URI=mongodb://localhost:27017/inventory_ai_db

# MongoDB Atlas
MONGO_URI=mongodb+srv://username:<EMAIL>/inventory_ai_db?retryWrites=true&w=majority
```

### 🔐 JWT认证配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `JWT_SECRET` | `dev_inventory_ai_jwt_secret_key_2025_change_in_production` | JWT签名密钥 |
| `JWT_EXPIRES_IN` | `7d` | Token过期时间 |

### ☁️ AWS S3配置 (可选)

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `AWS_ACCESS_KEY_ID` | - | AWS访问密钥ID |
| `AWS_SECRET_ACCESS_KEY` | - | AWS访问密钥 |
| `AWS_REGION` | `us-east-1` | AWS区域 |
| `S3_BUCKET` | `inventory-assets-dev` | S3存储桶名称 |

### 🤖 LLM API配置 (可选)

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `LLM_PROVIDER` | `deepseek` | LLM服务提供商 |
| `LLM_API_KEY` | - | LLM API密钥 |
| `LLM_BASE_URL` | `https://api.deepseek.com/v1` | LLM API基础URL |

### 📧 邮件配置 (可选)

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SMTP_HOST` | `smtp.gmail.com` | SMTP服务器地址 |
| `SMTP_PORT` | `587` | SMTP端口 |
| `SMTP_USER` | - | 邮箱用户名 |
| `SMTP_PASS` | - | 邮箱密码或应用密码 |

### 🔒 安全配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `RATE_LIMIT_WINDOW_MS` | `900000` | 速率限制时间窗口(毫秒) |
| `RATE_LIMIT_MAX_REQUESTS` | `100` | 时间窗口内最大请求数 |

## 📱 前端环境变量 (frontend/.env)

### 🌐 API配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VITE_API_BASE_URL` | `http://localhost:4000/api` | 后端API基础URL |
| `VITE_SOCKET_URL` | `http://localhost:4000` | WebSocket连接地址 |

### 🎨 应用配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VITE_APP_TITLE` | `库存管理系统 \| HubGoodFood` | 应用标题 |
| `VITE_APP_VERSION` | `1.0.0` | 应用版本 |
| `VITE_PRIMARY_COLOR` | `#2C7AFF` | 主题色 |

### 🔧 功能开关

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VITE_ENABLE_AI_CHAT` | `true` | 是否启用AI客服功能 |
| `VITE_ENABLE_BARCODE_SCANNER` | `true` | 是否启用条码扫描 |
| `VITE_ENABLE_FILE_UPLOAD` | `true` | 是否启用文件上传 |
| `VITE_DEBUG` | `true` | 是否启用调试模式 |

## 🚀 快速配置

### 1. 复制模板文件

```bash
# 后端环境变量
cp backend/.env.sample backend/.env

# 前端环境变量
cp frontend/.env.sample frontend/.env
```

### 2. 基础配置 (最小可运行配置)

**后端 (backend/.env)**:
```bash
PORT=4000
NODE_ENV=development
MONGO_URI=mongodb://localhost:27017/inventory_ai_db
JWT_SECRET=your_jwt_secret_here
FRONTEND_URL=http://localhost:5173
CORS_ORIGIN=http://localhost:5173
```

**前端 (frontend/.env)**:
```bash
VITE_API_BASE_URL=http://localhost:4000/api
VITE_SOCKET_URL=http://localhost:4000
```

### 3. 完整功能配置

如需启用所有功能，请参考模板文件中的完整配置项。

## ⚠️ 安全注意事项

1. **不要提交 `.env` 文件到版本控制系统**
2. **生产环境使用强密码和密钥**
3. **定期轮换API密钥和JWT密钥**
4. **限制数据库和API的访问权限**

## 🔍 故障排除

### 常见问题

**1. 后端无法连接数据库**
- 检查 `MONGO_URI` 配置是否正确
- 确认MongoDB服务是否运行
- 检查网络连接和防火墙设置

**2. 前端无法访问后端API**
- 检查 `VITE_API_BASE_URL` 配置
- 确认后端服务是否运行在正确端口
- 检查CORS配置

**3. JWT认证失败**
- 检查 `JWT_SECRET` 配置
- 确认前后端使用相同的密钥
- 检查Token过期时间设置

## 📚 相关文档

- [环境配置指南](ENV_SETUP_GUIDE.md)
- [快速设置指南](QUICK_SETUP.md)
- [项目需求文档](inventory_ai_support_system.md)
