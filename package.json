{"name": "chat-ai-3.0", "version": "1.0.0", "description": "仓库库存管理及AI客服系统 - 完整项目", "private": true, "scripts": {"check-env": "node scripts/check-env.js", "install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "seed": "cd backend && npm run seed", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf node_modules package-lock.json", "clean:frontend": "cd frontend && rm -rf node_modules package-lock.json", "setup": "npm run check-env && npm run install:all && npm run seed", "build:prod": "npm run build:frontend", "deploy:frontend": "cd frontend && npm run deploy:vercel", "deploy:backend": "cd backend && pm2 start ecosystem.config.js --env production", "deploy:full": "npm run build:prod && npm run deploy:frontend && npm run deploy:backend", "health-check": "curl -f http://localhost:4000/health || exit 1"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["inventory-management", "ai-customer-service", "mern-stack", "warehouse-management", "chat-ai"], "author": "HubGoodFood", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/HubGoodFood/Chat-AI-3.0.git"}}