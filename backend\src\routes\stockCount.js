/**
 * 盘点管理路由
 * 处理盘点任务相关的API请求
 */

const express = require('express');
const router = express.Router();
const stockCountController = require('../controllers/stockCountController');
const { authenticateToken, requirePermission } = require('../middleware/auth');

// @desc    创建盘点计划
// @route   POST /api/stock-count/plans
// @access  Private (需要盘点创建权限)
router.post('/plans', 
  authenticateToken, 
  requirePermission('stockcount.create'), 
  stockCountController.createStockCountPlan
);

// @desc    获取盘点计划列表
// @route   GET /api/stock-count/plans
// @access  Private (需要盘点查看权限)
router.get('/plans', 
  authenticateToken, 
  requirePermission('stockcount.read'), 
  stockCountController.getStockCountPlans
);

// @desc    获取盘点计划详情
// @route   GET /api/stock-count/plans/:id
// @access  Private (需要盘点查看权限)
router.get('/plans/:id', 
  authenticateToken, 
  requirePermission('stockcount.read'), 
  stockCountController.getStockCountPlanById
);

// @desc    分配盘点任务
// @route   POST /api/stock-count/plans/:id/assign
// @access  Private (需要盘点分配权限)
router.post('/plans/:id/assign', 
  authenticateToken, 
  requirePermission('stockcount.assign'), 
  stockCountController.assignStockCountTask
);

// @desc    开始盘点
// @route   POST /api/stock-count/plans/:id/start
// @access  Private (需要盘点执行权限)
router.post('/plans/:id/start', 
  authenticateToken, 
  requirePermission('stockcount.execute'), 
  stockCountController.startStockCount
);

// @desc    提交盘点结果
// @route   POST /api/stock-count/plans/:id/submit
// @access  Private (需要盘点执行权限)
router.post('/plans/:id/submit', 
  authenticateToken, 
  requirePermission('stockcount.execute'), 
  stockCountController.submitCountResults
);

// @desc    获取盘点建议
// @route   GET /api/stock-count/recommendations
// @access  Private (需要盘点查看权限)
router.get('/recommendations', 
  authenticateToken, 
  requirePermission('stockcount.read'), 
  stockCountController.getCountRecommendations
);

module.exports = router;
