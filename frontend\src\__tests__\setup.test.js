/**
 * 测试环境配置验证
 * 确保Jest和React Testing Library正确配置
 */

import { render, screen } from '@testing-library/react';
import React from 'react';

// 简单的测试组件
const TestComponent = () => {
  return (
    <div>
      <h1>测试组件</h1>
      <p>这是一个用于验证测试环境的组件</p>
      <button>点击测试</button>
    </div>
  );
};

describe('测试环境配置验证', () => {
  test('Jest和React Testing Library正常工作', () => {
    render(<TestComponent />);
    
    // 验证元素是否正确渲染
    expect(screen.getByText('测试组件')).toBeInTheDocument();
    expect(screen.getByText('这是一个用于验证测试环境的组件')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '点击测试' })).toBeInTheDocument();
  });

  test('基础断言功能正常', () => {
    expect(1 + 1).toBe(2);
    expect('hello').toMatch(/hello/);
    expect(['apple', 'banana']).toContain('apple');
  });

  test('异步测试支持', async () => {
    const promise = Promise.resolve('测试完成');
    await expect(promise).resolves.toBe('测试完成');
  });
});
