{"name": "inventory-ai-backend", "version": "1.0.0", "description": "仓库库存管理及AI客服系统 - 后端API服务", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "seed": "node scripts/seed.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["inventory", "ai-customer-service", "mern", "express", "mongodb"], "author": "HubGoodFood", "license": "MIT", "dependencies": {"aws-sdk": "^2.1498.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "sharp": "^0.34.2", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"concurrently": "^8.2.2", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}