# 🔧 部署故障排除指南

## 🚨 常见部署问题及解决方案

### 🖥️ 后端部署问题（Render）

#### 1. 构建失败
**症状**: Render构建过程中出现错误

**可能原因**:
- Node.js版本不兼容
- 依赖包安装失败
- 环境变量缺失

**解决方案**:
```bash
# 检查Node.js版本
node --version

# 清理依赖重新安装
rm -rf node_modules package-lock.json
npm install

# 本地测试构建
npm run build
```

**Render配置检查**:
- 确认Build Command: `cd backend && npm ci --only=production`
- 确认Start Command: `cd backend && npm start`
- 检查环境变量是否完整配置

#### 2. 启动失败
**症状**: 服务构建成功但启动失败

**可能原因**:
- 端口配置错误
- 环境变量缺失
- 数据库连接失败

**解决方案**:
```javascript
// 检查server.js中的端口配置
const PORT = process.env.PORT || 4000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
});
```

**环境变量检查清单**:
- [ ] `NODE_ENV=production`
- [ ] `PORT=4000`
- [ ] `MONGO_URI` (完整的MongoDB Atlas连接字符串)
- [ ] `JWT_SECRET` (32字符强密码)

#### 3. 数据库连接失败
**症状**: 应用启动但数据库操作失败

**解决方案**:
1. **检查MongoDB Atlas配置**:
   - 确认IP白名单包含 `0.0.0.0/0` (允许所有IP)
   - 验证用户名和密码正确
   - 确认数据库名称正确

2. **测试连接字符串**:
```bash
# 使用MongoDB Compass或mongo shell测试
mongo "mongodb+srv://username:<EMAIL>/database"
```

3. **检查网络连接**:
```javascript
// 在代码中添加连接测试
mongoose.connection.on('connected', () => {
  console.log('MongoDB connected successfully');
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error:', err);
});
```

#### 4. 健康检查失败
**症状**: Render显示服务不健康

**解决方案**:
1. **确认健康检查端点**:
```javascript
// 确保/health端点存在且正确响应
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV
  });
});
```

2. **检查响应时间**:
   - 健康检查应在30秒内响应
   - 如果数据库查询慢，考虑简化健康检查

### 🌐 前端部署问题（Vercel）

#### 1. 构建失败
**症状**: Vercel构建过程失败

**可能原因**:
- 依赖包版本冲突
- 环境变量缺失
- 构建配置错误

**解决方案**:
```bash
# 本地测试构建
cd frontend
npm ci
npm run build:prod

# 检查构建输出
ls -la dist/
```

**Vercel配置检查**:
- Framework Preset: Vite
- Build Command: `npm run build:prod`
- Output Directory: `dist`
- Root Directory: `frontend`

#### 2. 环境变量问题
**症状**: 应用构建成功但功能异常

**解决方案**:
1. **检查环境变量前缀**:
   - 确保所有环境变量以 `VITE_` 开头
   - 在Vercel Dashboard中验证环境变量配置

2. **测试环境变量**:
```javascript
// 在组件中测试
console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL);
console.log('Socket URL:', import.meta.env.VITE_SOCKET_URL);
```

#### 3. API连接失败
**症状**: 前端加载正常但API请求失败

**可能原因**:
- API URL配置错误
- CORS配置问题
- 后端服务未启动

**解决方案**:
1. **检查API URL**:
```javascript
// 确认API基础URL正确
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
console.log('Connecting to:', API_BASE_URL);
```

2. **测试API连接**:
```bash
# 直接测试后端API
curl https://your-render-backend.onrender.com/health
```

3. **检查CORS配置**:
   - 确认后端CORS_ORIGIN包含前端域名
   - 在Render中更新环境变量

#### 4. 路由问题
**症状**: 直接访问子路径返回404

**解决方案**:
确认 `vercel.json` 配置正确:
```json
{
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

### 🔗 前后端集成问题

#### 1. WebSocket连接失败
**症状**: 实时聊天功能不工作

**解决方案**:
1. **检查Socket.io配置**:
```javascript
// 前端连接配置
const socket = io(import.meta.env.VITE_SOCKET_URL, {
  transports: ['websocket', 'polling']
});
```

2. **检查CORS配置**:
```javascript
// 后端Socket.io CORS配置
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGIN,
    methods: ["GET", "POST"]
  }
});
```

#### 2. 认证问题
**症状**: 登录后API请求返回401

**解决方案**:
1. **检查JWT配置**:
   - 确认前后端使用相同的JWT_SECRET
   - 验证token格式和过期时间

2. **检查请求头**:
```javascript
// 确认Authorization头正确设置
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
```

### 🔍 调试工具和技巧

#### 1. 日志查看
**Render日志**:
- 在Render Dashboard中查看实时日志
- 使用 `console.log` 添加调试信息

**Vercel日志**:
```bash
# 使用Vercel CLI查看日志
vercel logs
```

#### 2. 网络调试
**浏览器开发者工具**:
- Network标签查看API请求
- Console标签查看错误信息
- Application标签检查localStorage

**API测试工具**:
```bash
# 使用curl测试API
curl -X GET https://your-backend.onrender.com/api/products \
  -H "Authorization: Bearer YOUR_TOKEN"

# 使用Postman或Insomnia测试
```

#### 3. 性能监控
**前端性能**:
- 使用Lighthouse检查性能
- 监控Core Web Vitals
- 检查资源加载时间

**后端性能**:
- 监控API响应时间
- 检查数据库查询性能
- 观察内存使用情况

### 🚨 紧急故障处理

#### 1. 服务完全不可用
**立即行动**:
1. 检查服务状态（Render/Vercel Dashboard）
2. 查看最近的部署日志
3. 如果是配置问题，回滚到上一个工作版本
4. 通知相关人员

#### 2. 数据库连接丢失
**立即行动**:
1. 检查MongoDB Atlas状态
2. 验证网络连接
3. 重启应用服务
4. 如果问题持续，联系MongoDB支持

#### 3. 性能严重下降
**立即行动**:
1. 检查服务器资源使用情况
2. 查看错误日志
3. 临时增加服务器资源
4. 分析性能瓶颈

### 📞 获取帮助

#### 官方支持
- **Render**: [Render Support](https://render.com/docs)
- **Vercel**: [Vercel Support](https://vercel.com/support)
- **MongoDB Atlas**: [MongoDB Support](https://support.mongodb.com/)

#### 社区资源
- Stack Overflow
- GitHub Issues
- Discord/Slack社区

#### 内部支持
- 技术团队联系方式
- 紧急联系人
- 故障处理流程文档

---

**记住**: 遇到问题时保持冷静，系统性地排查问题，记录解决过程以便将来参考。
