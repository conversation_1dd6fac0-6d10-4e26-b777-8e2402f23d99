import React, { useState, useEffect } from 'react'
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Upload,
  message,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  Divider
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ScanOutlined,
  UploadOutlined,
  ExportOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { productsService } from '../services/products'

const { Title } = Typography
const { Option } = Select
const { Search } = Input

const Products = () => {
  // 状态管理
  const [loading, setLoading] = useState(false)
  const [products, setProducts] = useState([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchText, setSearchText] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [categories, setCategories] = useState([])
  const [stats, setStats] = useState({})

  // 模态框状态
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingProduct, setEditingProduct] = useState(null)
  const [form] = Form.useForm()

  // 初始化数据
  useEffect(() => {
    loadProducts()
    loadCategories()
    loadStats()
  }, [currentPage, pageSize, searchText, selectedCategory, selectedStatus])

  // 加载商品列表
  const loadProducts = async () => {
    setLoading(true)
    try {
      const response = await productsService.getProducts({
        page: currentPage,
        limit: pageSize,
        search: searchText,
        category: selectedCategory,
        status: selectedStatus
      })

      if (response.success) {
        setProducts(response.data.products || [])
        setTotal(response.data.total || 0)
      }
    } catch (error) {
      message.error('加载商品列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载商品分类
  const loadCategories = async () => {
    try {
      const response = await productsService.getCategories()
      if (response.success) {
        setCategories(response.data || [])
      }
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  }

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await productsService.getProductStats()
      if (response.success) {
        setStats(response.data || {})
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  // 处理分类筛选
  const handleCategoryChange = (value) => {
    setSelectedCategory(value)
    setCurrentPage(1)
  }

  // 处理状态筛选
  const handleStatusChange = (value) => {
    setSelectedStatus(value)
    setCurrentPage(1)
  }

  // 打开新增/编辑模态框
  const handleOpenModal = (product = null) => {
    setEditingProduct(product)
    setIsModalVisible(true)
    if (product) {
      form.setFieldsValue(product)
    } else {
      form.resetFields()
    }
  }

  // 关闭模态框
  const handleCloseModal = () => {
    setIsModalVisible(false)
    setEditingProduct(null)
    form.resetFields()
  }

  // 保存商品
  const handleSaveProduct = async (values) => {
    try {
      if (editingProduct) {
        await productsService.updateProduct(editingProduct.id, values)
        message.success('商品更新成功')
      } else {
        await productsService.createProduct(values)
        message.success('商品创建成功')
      }
      handleCloseModal()
      loadProducts()
      loadStats()
    } catch (error) {
      message.error(editingProduct ? '更新商品失败' : '创建商品失败')
    }
  }

  // 删除商品
  const handleDeleteProduct = async (id) => {
    try {
      await productsService.deleteProduct(id)
      message.success('商品删除成功')
      loadProducts()
      loadStats()
    } catch (error) {
      message.error('删除商品失败')
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '商品图片',
      dataIndex: 'image_url',
      key: 'image_url',
      width: 80,
      render: (url) => (
        <div style={{ width: 50, height: 50, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          {url ? (
            <img src={url} alt="商品图片" style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: 4 }} />
          ) : (
            <span style={{ color: '#999', fontSize: 12 }}>无图片</span>
          )}
        </div>
      )
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true
    },
    {
      title: '条码',
      dataIndex: 'barcode',
      key: 'barcode',
      width: 120
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => <Tag color="blue">{category}</Tag>
    },
    {
      title: '规格',
      dataIndex: 'specification',
      key: 'specification',
      width: 120,
      ellipsis: true
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80
    },
    {
      title: '成本价',
      dataIndex: 'cost_price',
      key: 'cost_price',
      width: 100,
      render: (price) => `¥${price?.toFixed(2) || '0.00'}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleOpenModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个商品吗？"
            onConfirm={() => handleDeleteProduct(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div className="fade-in">
      <div className="page-header">
        <Title level={2}>商品管理</Title>
        <p>管理商品信息、条码录入和商品分类</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="商品总数"
              value={stats.total || 0}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="启用商品"
              value={stats.active || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="禁用商品"
              value={stats.inactive || 0}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="商品分类"
              value={categories.length || 0}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card>
        {/* 工具栏 */}
        <div style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="搜索商品名称、条码"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={12} sm={6} md={4}>
              <Select
                placeholder="选择分类"
                allowClear
                value={selectedCategory}
                onChange={handleCategoryChange}
                style={{ width: '100%' }}
              >
                {categories.map(category => (
                  <Option key={category} value={category}>{category}</Option>
                ))}
              </Select>
            </Col>
            <Col xs={12} sm={6} md={4}>
              <Select
                placeholder="选择状态"
                allowClear
                value={selectedStatus}
                onChange={handleStatusChange}
                style={{ width: '100%' }}
              >
                <Option value="active">启用</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </Col>
            <Col xs={24} sm={24} md={8}>
              <Space wrap>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleOpenModal()}
                >
                  新增商品
                </Button>
                <Button
                  icon={<ScanOutlined />}
                  onClick={() => message.info('条码扫描功能开发中')}
                >
                  扫码录入
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadProducts}
                >
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Divider />

        {/* 商品表格 */}
        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            }
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 新增/编辑商品模态框 */}
      <Modal
        title={editingProduct ? '编辑商品' : '新增商品'}
        open={isModalVisible}
        onCancel={handleCloseModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveProduct}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="商品名称"
                rules={[{ required: true, message: '请输入商品名称' }]}
              >
                <Input placeholder="请输入商品名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="barcode"
                label="商品条码"
                rules={[{ required: true, message: '请输入商品条码' }]}
              >
                <Input placeholder="请输入商品条码" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="商品分类"
                rules={[{ required: true, message: '请选择商品分类' }]}
              >
                <Select placeholder="请选择商品分类">
                  {categories.map(category => (
                    <Option key={category} value={category}>{category}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="计量单位"
                rules={[{ required: true, message: '请输入计量单位' }]}
              >
                <Input placeholder="如：个、包、箱" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="specification"
            label="商品规格"
          >
            <Input placeholder="请输入商品规格" />
          </Form.Item>

          <Form.Item
            name="description"
            label="商品描述"
          >
            <Input.TextArea rows={3} placeholder="请输入商品描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cost_price"
                label="成本价格"
                rules={[{ required: true, message: '请输入成本价格' }]}
              >
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="商品状态"
                initialValue="active"
              >
                <Select>
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingProduct ? '更新' : '创建'}
              </Button>
              <Button onClick={handleCloseModal}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Products
