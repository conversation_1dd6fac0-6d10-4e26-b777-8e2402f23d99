# 🔧 环境变量配置指南

本文档将指导您如何获取和配置Chat-AI-3.0项目所需的各种API密钥和环境变量。

## 📋 快速开始

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 根据下面的指南填写各项配置
3. 确保`.env`文件不会被提交到Git仓库

## 🔑 必需的API密钥获取指南

### 1. MongoDB Atlas 数据库配置

**获取步骤：**
1. 访问 [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. 注册账号并创建免费集群
3. 创建数据库用户
4. 配置网络访问（允许所有IP：0.0.0.0/0）
5. 获取连接字符串

**配置示例：**
```env
MONGO_URI=mongodb+srv://username:<EMAIL>/inventory_ai_db
```

### 2. AWS S3 文件存储配置

**获取步骤：**
1. 登录 [AWS控制台](https://aws.amazon.com/console/)
2. 创建S3存储桶：`hubgoodfood-assets`
3. 创建IAM用户并分配S3权限
4. 生成访问密钥

**配置示例：**
```env
AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE
AWS_SECRET_ACCESS_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
S3_BUCKET=hubgoodfood-assets
```

### 3. AI/LLM 服务配置

#### DeepSeek API（推荐）
**获取步骤：**
1. 访问 [DeepSeek开放平台](https://platform.deepseek.com/)
2. 注册账号并实名认证
3. 创建API密钥
4. 充值账户余额

**配置示例：**
```env
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

#### OpenAI API（备用）
**获取步骤：**
1. 访问 [OpenAI平台](https://platform.openai.com/)
2. 注册账号并验证
3. 创建API密钥
4. 设置使用限额

**配置示例：**
```env
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

#### Google Gemini API（备用）
**获取步骤：**
1. 访问 [Google AI Studio](https://makersuite.google.com/)
2. 创建项目并启用Gemini API
3. 生成API密钥

**配置示例：**
```env
GEMINI_API_KEY=AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

## 📧 邮件服务配置

### Gmail SMTP配置
**获取步骤：**
1. 启用Gmail的两步验证
2. 生成应用专用密码
3. 配置SMTP设置

**配置示例：**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_specific_password
```

## 💳 支付服务配置

### Venmo API配置
**获取步骤：**
1. 访问 [Venmo开发者平台](https://developer.venmo.com/)
2. 创建应用并获取凭据
3. 配置回调URL

**配置示例：**
```env
VENMO_CLIENT_ID=your_venmo_client_id
VENMO_CLIENT_SECRET=your_venmo_client_secret
```

## 🔍 可选服务配置

### 条码查询API
推荐使用：[UPC Database](https://upcdatabase.org/) 或 [Barcode Lookup](https://www.barcodelookup.com/)

### 短信服务
推荐使用：[Twilio](https://www.twilio.com/) 或 [阿里云短信服务](https://www.aliyun.com/product/sms)

### Redis缓存
推荐使用：[Redis Cloud](https://redis.com/redis-enterprise-cloud/) 或本地安装

## 🛡️ 安全配置

### JWT密钥生成
```bash
# 生成强随机密钥
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### 加密密钥生成
```bash
# 生成32字符加密密钥
node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"
```

## 🚀 部署环境配置

### Render平台配置
1. 在Render控制台添加环境变量
2. 确保所有必需的变量都已配置
3. 设置自动部署

### 生产环境注意事项
- 使用强密码和复杂的密钥
- 定期轮换API密钥
- 启用所有安全功能
- 配置监控和告警

## 📊 监控配置

### Elastic APM（可选）
**获取步骤：**
1. 创建Elastic Cloud账号
2. 部署APM服务器
3. 获取服务器URL和密钥

## ✅ 配置验证

创建一个简单的验证脚本来检查配置：

```javascript
// verify-config.js
const requiredVars = [
  'MONGO_URI',
  'JWT_SECRET',
  'AWS_ACCESS_KEY_ID',
  'AWS_SECRET_ACCESS_KEY',
  'S3_BUCKET',
  'LLM_PROVIDER',
  'DEEPSEEK_API_KEY'
];

requiredVars.forEach(varName => {
  if (!process.env[varName]) {
    console.error(`❌ 缺少必需的环境变量: ${varName}`);
  } else {
    console.log(`✅ ${varName} 已配置`);
  }
});
```

运行验证：
```bash
node -r dotenv/config verify-config.js
```

## 🆘 常见问题

### Q: MongoDB连接失败
A: 检查网络访问设置，确保允许当前IP访问

### Q: S3上传失败
A: 验证IAM用户权限，确保有S3读写权限

### Q: AI API调用失败
A: 检查API密钥是否正确，账户是否有足够余额

### Q: 邮件发送失败
A: 确认SMTP设置正确，Gmail需要使用应用专用密码

## 📞 技术支持

如果在配置过程中遇到问题，请：
1. 检查相关服务的官方文档
2. 验证API密钥的有效性
3. 查看应用日志获取详细错误信息
4. 联系相应服务的技术支持

---

**⚠️ 安全提醒：**
- 永远不要将`.env`文件提交到版本控制系统
- 定期更换API密钥
- 使用最小权限原则配置服务权限
- 在生产环境中启用所有安全功能
