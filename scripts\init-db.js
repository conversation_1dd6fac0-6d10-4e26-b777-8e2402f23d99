#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 创建初始用户和基础数据
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./src/models/User');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 初始用户数据
const initialUsers = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    name: '系统管理员',
    role: 'admin',
    phone: '13800138000'
  },
  {
    username: 'manager',
    email: '<EMAIL>',
    password: 'manager123',
    name: '仓库经理',
    role: 'manager',
    phone: '13800138001'
  },
  {
    username: 'staff',
    email: '<EMAIL>',
    password: 'staff123',
    name: '仓库员工',
    role: 'staff',
    phone: '13800138002'
  }
];

async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    log('✅ 数据库连接成功', 'green');
    log(`📊 数据库名称: ${mongoose.connection.name}`, 'blue');
  } catch (error) {
    log('❌ 数据库连接失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

async function createUsers() {
  log('\n👥 创建初始用户...', 'blue');
  
  for (const userData of initialUsers) {
    try {
      // 检查用户是否已存在
      const existingUser = await User.findOne({
        $or: [
          { username: userData.username },
          { email: userData.email }
        ]
      });
      
      if (existingUser) {
        log(`⚠️  用户 ${userData.username} 已存在，跳过创建`, 'yellow');
        continue;
      }
      
      // 创建新用户
      const user = new User(userData);
      user.setDefaultPermissions();
      await user.save();
      
      log(`✅ 创建用户: ${userData.username} (${userData.role})`, 'green');
    } catch (error) {
      log(`❌ 创建用户 ${userData.username} 失败:`, 'red');
      log(error.message, 'red');
    }
  }
}

async function testDatabase() {
  log('\n🧪 测试数据库操作...', 'blue');
  
  try {
    // 测试查询
    const userCount = await User.countDocuments();
    log(`📊 用户总数: ${userCount}`, 'green');
    
    // 测试管理员用户
    const adminUser = await User.findOne({ username: 'admin' });
    if (adminUser) {
      log(`✅ 管理员用户验证成功: ${adminUser.name}`, 'green');
      
      // 测试密码验证
      const isPasswordValid = await adminUser.comparePassword('admin123');
      if (isPasswordValid) {
        log('✅ 密码验证功能正常', 'green');
      } else {
        log('❌ 密码验证功能异常', 'red');
      }
      
      // 测试权限检查
      const hasPermission = adminUser.hasPermission('products.read');
      if (hasPermission) {
        log('✅ 权限检查功能正常', 'green');
      } else {
        log('❌ 权限检查功能异常', 'red');
      }
    }
    
  } catch (error) {
    log('❌ 数据库测试失败:', 'red');
    log(error.message, 'red');
  }
}

async function showConnectionInfo() {
  log('\n📋 数据库连接信息:', 'blue');
  log(`🔗 连接状态: ${mongoose.connection.readyState === 1 ? '已连接' : '未连接'}`, 'green');
  log(`🏠 主机: ${mongoose.connection.host}`, 'blue');
  log(`📊 数据库: ${mongoose.connection.name}`, 'blue');
  log(`👥 集合数量: ${Object.keys(mongoose.connection.collections).length}`, 'blue');
}

async function main() {
  log('🚀 数据库初始化开始', 'blue');
  log('=' * 50, 'blue');
  
  try {
    // 连接数据库
    await connectDatabase();
    
    // 显示连接信息
    await showConnectionInfo();
    
    // 创建初始用户
    await createUsers();
    
    // 测试数据库功能
    await testDatabase();
    
    log('\n🎉 数据库初始化完成！', 'green');
    log('\n📝 登录信息:', 'blue');
    log('管理员: admin / admin123', 'green');
    log('经理: manager / manager123', 'green');
    log('员工: staff / staff123', 'green');
    
  } catch (error) {
    log('\n❌ 初始化失败:', 'red');
    log(error.message, 'red');
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    log('\n🔌 数据库连接已关闭', 'blue');
  }
}

if (require.main === module) {
  main();
}

module.exports = { connectDatabase, createUsers, testDatabase };
