const logger = require('../utils/logger');

/**
 * 条码服务类
 * 处理条码生成、验证、格式检查等功能
 */
class BarcodeService {
  constructor() {
    // 支持的条码格式
    this.supportedFormats = {
      EAN13: {
        name: 'EAN-13',
        length: 13,
        pattern: /^\d{13}$/,
        description: '欧洲商品条码，13位数字'
      },
      EAN8: {
        name: 'EAN-8',
        length: 8,
        pattern: /^\d{8}$/,
        description: '欧洲商品条码，8位数字'
      },
      CODE128: {
        name: 'Code 128',
        pattern: /^[\x00-\x7F]+$/,
        description: '128码，支持ASCII字符'
      },
      CODE39: {
        name: 'Code 39',
        pattern: /^[A-Z0-9\-\.\$\/\+\%\s]+$/,
        description: '39码，支持大写字母、数字和特殊字符'
      },
      UPCA: {
        name: 'UPC-A',
        length: 12,
        pattern: /^\d{12}$/,
        description: '美国通用产品代码，12位数字'
      }
    };

    // 默认条码格式
    this.defaultFormat = 'EAN13';

    logger.info('BarcodeService初始化完成', {
      supportedFormats: Object.keys(this.supportedFormats).length,
      defaultFormat: this.defaultFormat
    });
  }

  /**
   * 生成条码
   * @param {string} format - 条码格式
   * @param {string} prefix - 前缀（可选）
   * @returns {string} 生成的条码
   */
  generateBarcode(format = this.defaultFormat, prefix = '') {
    try {
      const formatConfig = this.supportedFormats[format];
      
      if (!formatConfig) {
        throw new Error(`不支持的条码格式: ${format}`);
      }

      let barcode;

      switch (format) {
        case 'EAN13':
          barcode = this.generateEAN13(prefix);
          break;
        case 'EAN8':
          barcode = this.generateEAN8(prefix);
          break;
        case 'CODE128':
          barcode = this.generateCode128(prefix);
          break;
        case 'CODE39':
          barcode = this.generateCode39(prefix);
          break;
        case 'UPCA':
          barcode = this.generateUPCA(prefix);
          break;
        default:
          barcode = this.generateEAN13(prefix);
      }

      logger.info('条码生成成功', {
        format,
        barcode,
        prefix
      });

      return barcode;

    } catch (error) {
      logger.error('条码生成失败', {
        format,
        prefix,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 生成EAN-13条码
   * @param {string} prefix - 前缀
   * @returns {string} EAN-13条码
   */
  generateEAN13(prefix = '') {
    // 如果有前缀，确保总长度不超过12位（留1位给校验位）
    const maxDataLength = 12;
    let data = prefix;

    // 补充随机数字到12位
    while (data.length < maxDataLength) {
      data += Math.floor(Math.random() * 10).toString();
    }

    // 如果超过12位，截取前12位
    if (data.length > maxDataLength) {
      data = data.substring(0, maxDataLength);
    }

    // 计算校验位
    const checkDigit = this.calculateEAN13CheckDigit(data);
    
    return data + checkDigit;
  }

  /**
   * 生成EAN-8条码
   * @param {string} prefix - 前缀
   * @returns {string} EAN-8条码
   */
  generateEAN8(prefix = '') {
    const maxDataLength = 7;
    let data = prefix;

    while (data.length < maxDataLength) {
      data += Math.floor(Math.random() * 10).toString();
    }

    if (data.length > maxDataLength) {
      data = data.substring(0, maxDataLength);
    }

    const checkDigit = this.calculateEAN8CheckDigit(data);
    
    return data + checkDigit;
  }

  /**
   * 生成Code 128条码
   * @param {string} prefix - 前缀
   * @returns {string} Code 128条码
   */
  generateCode128(prefix = '') {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    
    return prefix + timestamp + random;
  }

  /**
   * 生成Code 39条码
   * @param {string} prefix - 前缀
   * @returns {string} Code 39条码
   */
  generateCode39(prefix = '') {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    
    return (prefix + timestamp + random).toUpperCase();
  }

  /**
   * 生成UPC-A条码
   * @param {string} prefix - 前缀
   * @returns {string} UPC-A条码
   */
  generateUPCA(prefix = '') {
    const maxDataLength = 11;
    let data = prefix;

    while (data.length < maxDataLength) {
      data += Math.floor(Math.random() * 10).toString();
    }

    if (data.length > maxDataLength) {
      data = data.substring(0, maxDataLength);
    }

    const checkDigit = this.calculateUPCACheckDigit(data);
    
    return data + checkDigit;
  }

  /**
   * 计算EAN-13校验位
   * @param {string} data - 12位数据
   * @returns {string} 校验位
   */
  calculateEAN13CheckDigit(data) {
    let sum = 0;
    
    for (let i = 0; i < 12; i++) {
      const digit = parseInt(data[i]);
      sum += (i % 2 === 0) ? digit : digit * 3;
    }
    
    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit.toString();
  }

  /**
   * 计算EAN-8校验位
   * @param {string} data - 7位数据
   * @returns {string} 校验位
   */
  calculateEAN8CheckDigit(data) {
    let sum = 0;
    
    for (let i = 0; i < 7; i++) {
      const digit = parseInt(data[i]);
      sum += (i % 2 === 0) ? digit * 3 : digit;
    }
    
    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit.toString();
  }

  /**
   * 计算UPC-A校验位
   * @param {string} data - 11位数据
   * @returns {string} 校验位
   */
  calculateUPCACheckDigit(data) {
    let sum = 0;
    
    for (let i = 0; i < 11; i++) {
      const digit = parseInt(data[i]);
      sum += (i % 2 === 0) ? digit * 3 : digit;
    }
    
    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit.toString();
  }

  /**
   * 验证条码格式
   * @param {string} barcode - 条码
   * @param {string} format - 期望的格式（可选）
   * @returns {Object} 验证结果
   */
  validateBarcode(barcode, format = null) {
    try {
      if (!barcode || typeof barcode !== 'string') {
        return {
          isValid: false,
          format: null,
          error: '条码不能为空'
        };
      }

      // 如果指定了格式，只验证该格式
      if (format) {
        const formatConfig = this.supportedFormats[format];
        if (!formatConfig) {
          return {
            isValid: false,
            format: null,
            error: `不支持的条码格式: ${format}`
          };
        }

        const isValid = this.validateSpecificFormat(barcode, format);
        return {
          isValid,
          format: isValid ? format : null,
          error: isValid ? null : `条码不符合${formatConfig.name}格式`
        };
      }

      // 自动检测格式
      for (const [formatName, config] of Object.entries(this.supportedFormats)) {
        if (this.validateSpecificFormat(barcode, formatName)) {
          return {
            isValid: true,
            format: formatName,
            error: null
          };
        }
      }

      return {
        isValid: false,
        format: null,
        error: '条码格式不被支持'
      };

    } catch (error) {
      logger.error('条码验证失败', {
        barcode,
        format,
        error: error.message
      });

      return {
        isValid: false,
        format: null,
        error: error.message
      };
    }
  }

  /**
   * 验证特定格式的条码
   * @param {string} barcode - 条码
   * @param {string} format - 格式
   * @returns {boolean} 是否有效
   */
  validateSpecificFormat(barcode, format) {
    const config = this.supportedFormats[format];
    
    if (!config) {
      return false;
    }

    // 检查长度（如果有长度要求）
    if (config.length && barcode.length !== config.length) {
      return false;
    }

    // 检查模式
    if (!config.pattern.test(barcode)) {
      return false;
    }

    // 对于有校验位的格式，验证校验位
    switch (format) {
      case 'EAN13':
        return this.validateEAN13CheckDigit(barcode);
      case 'EAN8':
        return this.validateEAN8CheckDigit(barcode);
      case 'UPCA':
        return this.validateUPCACheckDigit(barcode);
      default:
        return true;
    }
  }

  /**
   * 验证EAN-13校验位
   * @param {string} barcode - 13位条码
   * @returns {boolean} 校验位是否正确
   */
  validateEAN13CheckDigit(barcode) {
    if (barcode.length !== 13) {
      return false;
    }

    const data = barcode.substring(0, 12);
    const providedCheckDigit = barcode[12];
    const calculatedCheckDigit = this.calculateEAN13CheckDigit(data);

    return providedCheckDigit === calculatedCheckDigit;
  }

  /**
   * 验证EAN-8校验位
   * @param {string} barcode - 8位条码
   * @returns {boolean} 校验位是否正确
   */
  validateEAN8CheckDigit(barcode) {
    if (barcode.length !== 8) {
      return false;
    }

    const data = barcode.substring(0, 7);
    const providedCheckDigit = barcode[7];
    const calculatedCheckDigit = this.calculateEAN8CheckDigit(data);

    return providedCheckDigit === calculatedCheckDigit;
  }

  /**
   * 验证UPC-A校验位
   * @param {string} barcode - 12位条码
   * @returns {boolean} 校验位是否正确
   */
  validateUPCACheckDigit(barcode) {
    if (barcode.length !== 12) {
      return false;
    }

    const data = barcode.substring(0, 11);
    const providedCheckDigit = barcode[11];
    const calculatedCheckDigit = this.calculateUPCACheckDigit(data);

    return providedCheckDigit === calculatedCheckDigit;
  }

  /**
   * 获取支持的条码格式列表
   * @returns {Array} 格式列表
   */
  getSupportedFormats() {
    return Object.entries(this.supportedFormats).map(([key, config]) => ({
      code: key,
      name: config.name,
      description: config.description,
      length: config.length || null
    }));
  }

  /**
   * 批量生成条码
   * @param {number} count - 生成数量
   * @param {string} format - 条码格式
   * @param {string} prefix - 前缀
   * @returns {Array} 条码数组
   */
  generateBatchBarcodes(count, format = this.defaultFormat, prefix = '') {
    try {
      if (count <= 0 || count > 1000) {
        throw new Error('生成数量必须在1-1000之间');
      }

      const barcodes = [];
      const generated = new Set(); // 确保唯一性

      let attempts = 0;
      const maxAttempts = count * 10; // 最大尝试次数

      while (barcodes.length < count && attempts < maxAttempts) {
        const barcode = this.generateBarcode(format, prefix);
        
        if (!generated.has(barcode)) {
          generated.add(barcode);
          barcodes.push(barcode);
        }
        
        attempts++;
      }

      if (barcodes.length < count) {
        logger.warn('批量生成条码未达到预期数量', {
          requested: count,
          generated: barcodes.length,
          attempts
        });
      }

      logger.info('批量生成条码完成', {
        count: barcodes.length,
        format,
        prefix
      });

      return barcodes;

    } catch (error) {
      logger.error('批量生成条码失败', {
        count,
        format,
        prefix,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = new BarcodeService();
