const { ChatSession, Product, Inventory, OperationLog } = require('../models');
const aiService = require('../services/aiService');
const ragService = require('../services/ragService');
const policyService = require('../services/policyService');
const logger = require('../utils/logger');

/**
 * 聊天控制器
 * 处理AI客服聊天会话管理
 */
class ChatController {
  /**
   * 创建新的聊天会话
   */
  async createChatSession(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        customer_info = {},
        channel = 'web_widget',
        language = 'zh-CN'
      } = req.body;
      
      // 创建聊天会话
      const chatSession = new ChatSession({
        customer_info: {
          ...customer_info,
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        },
        channel,
        language,
        status: 'active'
      });
      
      // 添加欢迎消息
      chatSession.addMessage(
        'ai',
        '您好！我是智能客服助手，可以帮您查询库存信息和处理退货申请。请问有什么可以帮助您的吗？',
        'text',
        {
          intent: 'greeting',
          confidence: 1.0
        }
      );
      
      await chatSession.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        null, // 客户端创建，没有用户ID
        'chat.start',
        'chat_session',
        chatSession._id.toString(),
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          description: `创建聊天会话: ${chatSession.session_id}`
        }
      );
      
      logger.info(`新聊天会话创建: ${chatSession.session_id} (${req.ip})`);
      
      res.status(201).json({
        success: true,
        message: '聊天会话创建成功',
        data: {
          session_id: chatSession.session_id,
          messages: chatSession.messages
        }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        null,
        'chat.start',
        'chat_session',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('创建聊天会话错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 发送聊天消息
   */
  async sendMessage(req, res) {
    const startTime = Date.now();
    
    try {
      const { session_id } = req.params;
      const { message, message_type = 'text' } = req.body;
      
      if (!message) {
        return res.status(400).json({
          success: false,
          message: '消息内容是必需的'
        });
      }
      
      // 查找聊天会话
      const chatSession = await ChatSession.findOne({ session_id });
      
      if (!chatSession) {
        return res.status(404).json({
          success: false,
          message: '聊天会话不存在'
        });
      }
      
      if (chatSession.status === 'closed') {
        return res.status(400).json({
          success: false,
          message: '聊天会话已关闭'
        });
      }
      
      // 添加用户消息
      const userMessage = chatSession.addMessage('user', message, message_type);
      
      // 处理AI响应
      const aiResponse = await this.processAIResponse(chatSession, message, message_type);
      
      // 添加AI响应消息
      const aiMessage = chatSession.addMessage(
        'ai',
        aiResponse.content,
        'text',
        {
          intent: aiResponse.intent,
          confidence: aiResponse.confidence,
          entities: aiResponse.entities,
          action_taken: aiResponse.action_taken,
          response_time_ms: Date.now() - startTime
        }
      );
      
      await chatSession.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        null,
        'chat.message',
        'chat_session',
        chatSession._id.toString(),
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `聊天消息处理: ${session_id}`
        }
      );
      
      res.json({
        success: true,
        data: {
          user_message: userMessage,
          ai_message: aiMessage,
          session_status: chatSession.status
        }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        null,
        'chat.message',
        'chat_session',
        error,
        {
          resource_id: req.params.session_id,
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('发送聊天消息错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取聊天会话历史
   */
  async getChatHistory(req, res) {
    try {
      const { session_id } = req.params;
      const { limit = 50 } = req.query;
      
      const chatSession = await ChatSession.findOne({ session_id });
      
      if (!chatSession) {
        return res.status(404).json({
          success: false,
          message: '聊天会话不存在'
        });
      }
      
      // 获取最近的消息
      const messages = chatSession.getRecentMessages(parseInt(limit));
      
      res.json({
        success: true,
        data: {
          session_id: chatSession.session_id,
          status: chatSession.status,
          messages,
          customer_info: chatSession.customer_info,
          context: chatSession.context
        }
      });
      
    } catch (error) {
      logger.error('获取聊天历史错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 升级到人工客服
   */
  async escalateToAgent(req, res) {
    const startTime = Date.now();
    
    try {
      const { session_id } = req.params;
      const { reason } = req.body;
      
      const chatSession = await ChatSession.findOne({ session_id });
      
      if (!chatSession) {
        return res.status(404).json({
          success: false,
          message: '聊天会话不存在'
        });
      }
      
      // 升级到人工客服
      chatSession.escalateToAgent(reason, req.user?.userId);
      await chatSession.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        req.user?.userId,
        'chat.escalate',
        'chat_session',
        chatSession._id.toString(),
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `聊天会话升级: ${session_id}, 原因: ${reason}`
        }
      );
      
      logger.info(`聊天会话升级: ${session_id}, 原因: ${reason}`);
      
      res.json({
        success: true,
        message: '已转接至人工客服',
        data: {
          status: chatSession.status,
          escalation_reason: reason
        }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user?.userId,
        'chat.escalate',
        'chat_session',
        error,
        {
          resource_id: req.params.session_id,
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('升级聊天会话错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 处理AI响应（集成真实AI服务和RAG检索）
   */
  async processAIResponse(chatSession, userMessage, messageType = 'text') {
    try {
      logger.info('开始处理AI响应', {
        session_id: chatSession.session_id,
        message: userMessage.substring(0, 100)
      });

      // 1. 意图分析
      const intentResult = await aiService.analyzeIntent(userMessage, {
        session_context: chatSession.context
      });

      logger.info('意图分析结果', intentResult);

      // 2. RAG检索相关信息
      const retrievalResults = await ragService.retrieve(userMessage, {
        includePolicy: true,
        includeProduct: intentResult.intent === 'inventory_query' || intentResult.intent === 'product_info',
        includeInventory: intentResult.intent === 'inventory_query',
        limit: 3
      });

      logger.info('RAG检索结果', {
        policyCount: retrievalResults.policies.length,
        productCount: retrievalResults.products.length,
        inventoryCount: retrievalResults.inventory.length,
        relevanceScore: retrievalResults.relevanceScore
      });

      // 3. 生成上下文信息
      const context = ragService.generateContext(retrievalResults, userMessage);

      // 4. 构建对话历史
      const conversationHistory = this.buildConversationHistory(chatSession, 5);

      // 5. 构建系统提示词
      const systemPrompt = this.buildSystemPrompt(intentResult.intent, context);

      // 6. 调用AI服务生成响应
      const aiResponse = await aiService.chat(
        [
          ...conversationHistory,
          { role: 'user', content: userMessage }
        ],
        {
          system_prompt: systemPrompt,
          temperature: 0.7,
          max_tokens: 1000
        }
      );

      if (!aiResponse.success) {
        logger.error('AI服务调用失败', aiResponse);
        return this.getFallbackResponse(intentResult.intent);
      }

      // 7. 处理特定意图的后续动作
      const actionResult = await this.handleIntentAction(
        intentResult.intent,
        userMessage,
        retrievalResults,
        chatSession
      );

      // 8. 增加相关政策的使用计数
      if (retrievalResults.policies.length > 0) {
        for (const policy of retrievalResults.policies) {
          await policyService.incrementUsage(policy.policy_id);
        }
      }

      return {
        content: aiResponse.content,
        intent: intentResult.intent,
        confidence: intentResult.confidence,
        entities: intentResult.entities || [],
        action_taken: actionResult.action_taken,
        referenced_policies: retrievalResults.policies.map(p => ({
          policy_id: p.policy_id,
          name: p.name,
          category: p.category
        })),
        ai_usage: aiResponse.usage,
        retrieval_score: retrievalResults.relevanceScore
      };

    } catch (error) {
      logger.error('处理AI响应错误:', error);
      return this.getFallbackResponse('general_question');
    }
  }

  /**
   * 构建对话历史
   */
  buildConversationHistory(chatSession, limit = 5) {
    const recentMessages = chatSession.getRecentMessages(limit * 2);
    const history = [];

    for (const msg of recentMessages) {
      if (msg.sender === 'user') {
        history.push({ role: 'user', content: msg.content });
      } else if (msg.sender === 'ai') {
        history.push({ role: 'assistant', content: msg.content });
      }
    }

    return history.slice(-limit * 2); // 保持用户和AI消息的平衡
  }

  /**
   * 构建系统提示词
   */
  buildSystemPrompt(intent, context) {
    let basePrompt = `你是一个专业的客服AI助手，为社区拼台（团购平台）提供服务。

你的职责包括：
1. 回答关于产品、配送、付款、取货等问题
2. 协助处理退货和售后问题
3. 解释平台政策和规则
4. 查询库存信息
5. 提供友好、专业的客户服务

回答要求：
- 使用简体中文
- 语气友好、专业
- 回答准确、简洁
- 优先引用相关政策条款来支持回答
- 如果不确定，请说明并建议联系人工客服

当前用户意图：${intent}

相关信息：
${context}

请基于以上信息回答用户问题。如果有相关政策，请在回答中引用具体的政策条款。`;

    // 根据意图调整提示词
    switch (intent) {
      case 'inventory_query':
        basePrompt += '\n\n特别注意：如果有库存信息，请提供准确的数量和状态。';
        break;
      case 'return_request':
        basePrompt += '\n\n特别注意：请根据退货政策指导用户，说明退货条件和流程。';
        break;
      case 'policy_question':
        basePrompt += '\n\n特别注意：请详细解释相关政策，并引用具体条款。';
        break;
    }

    return basePrompt;
  }

  /**
   * 处理意图相关的动作
   */
  async handleIntentAction(intent, userMessage, retrievalResults, chatSession) {
    try {
      switch (intent) {
        case 'inventory_query':
          return await this.handleInventoryQuery(userMessage, retrievalResults);

        case 'return_request':
          return await this.handleReturnRequest(userMessage, chatSession);

        case 'product_info':
          return await this.handleProductInfo(userMessage, retrievalResults);

        default:
          return { action_taken: 'general_response' };
      }
    } catch (error) {
      logger.error('处理意图动作失败', error);
      return { action_taken: 'error_handling' };
    }
  }

  /**
   * 处理库存查询
   */
  async handleInventoryQuery(userMessage, retrievalResults) {
    if (retrievalResults.inventory.length > 0) {
      return { action_taken: 'inventory_search' };
    }
    return { action_taken: 'inventory_not_found' };
  }

  /**
   * 处理退货请求
   */
  async handleReturnRequest(userMessage, chatSession) {
    // 更新会话上下文，标记为退货相关
    chatSession.context.current_topic = 'return';
    return { action_taken: 'return_guidance' };
  }

  /**
   * 处理产品信息查询
   */
  async handleProductInfo(userMessage, retrievalResults) {
    if (retrievalResults.products.length > 0) {
      return { action_taken: 'product_info_provided' };
    }
    return { action_taken: 'product_not_found' };
  }

  /**
   * 获取备用响应
   */
  getFallbackResponse(intent) {
    const fallbackResponses = {
      'inventory_query': {
        content: '抱歉，我暂时无法查询库存信息。请稍后再试或联系人工客服。',
        intent: 'inventory_query',
        confidence: 0.1,
        entities: [],
        action_taken: 'fallback_response'
      },
      'return_request': {
        content: '抱歉，我暂时无法处理退货申请。请联系人工客服获得帮助。',
        intent: 'return_request',
        confidence: 0.1,
        entities: [],
        action_taken: 'fallback_response'
      },
      'general_question': {
        content: '抱歉，我暂时无法理解您的问题。请稍后再试或联系人工客服。',
        intent: 'general_question',
        confidence: 0.1,
        entities: [],
        action_taken: 'fallback_response'
      }
    };

    return fallbackResponses[intent] || fallbackResponses['general_question'];
  }
}

module.exports = new ChatController();
