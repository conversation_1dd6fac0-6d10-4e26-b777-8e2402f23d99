const express = require('express');
const router = express.Router();
const policyController = require('../controllers/policyController');
const { authenticateToken, requireRole } = require('../middleware/auth');

/**
 * 政策管理路由
 * 提供政策的增删改查、搜索、导入等功能
 */

// @desc    获取所有政策
// @route   GET /api/policy
// @access  Public (可根据需要调整为Private)
router.get('/', policyController.getAllPolicies);

// @desc    搜索政策
// @route   GET /api/policy/search
// @access  Public
router.get('/search', policyController.searchPolicies);

// @desc    获取政策统计信息
// @route   GET /api/policy/statistics
// @access  Private (需要管理员权限)
router.get('/statistics', authenticateToken, requireRole(['admin']), policyController.getStatistics);

// @desc    根据ID获取政策详情
// @route   GET /api/policy/:policy_id
// @access  Public
router.get('/:policy_id', policyController.getPolicyById);

// @desc    创建新政策
// @route   POST /api/policy
// @access  Private (需要管理员权限)
router.post('/', authenticateToken, requireRole(['admin']), policyController.createPolicy);

// @desc    从policy.json导入政策
// @route   POST /api/policy/import
// @access  Private (需要管理员权限)
router.post('/import', authenticateToken, requireRole(['admin']), policyController.importPolicies);

// @desc    更新政策
// @route   PUT /api/policy/:policy_id
// @access  Private (需要管理员权限)
router.put('/:policy_id', authenticateToken, requireRole(['admin']), policyController.updatePolicy);

// @desc    删除政策
// @route   DELETE /api/policy/:policy_id
// @access  Private (需要管理员权限)
router.delete('/:policy_id', authenticateToken, requireRole(['admin']), policyController.deletePolicy);

module.exports = router;
