const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { User, Product, Inventory, StockInRecord, ReturnRequest, ChatSession, OperationLog, Report } = require('../src/models');

/**
 * 模型单元测试
 * 测试所有数据模型的基本功能
 */

describe('数据模型测试', () => {
  let mongoServer;
  let adminUser;

  beforeAll(async () => {
    // 启动内存数据库
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    // 清理并关闭数据库
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // 清空所有集合
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      await collections[key].deleteMany({});
    }

    // 创建测试用户
    adminUser = new User({
      username: 'testadmin',
      email: '<EMAIL>',
      password: 'password123',
      name: '测试管理员',
      role: 'admin'
    });
    adminUser.setDefaultPermissions();
    await adminUser.save();
  });

  describe('User模型测试', () => {
    test('应该能够创建用户', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        name: '测试用户',
        role: 'staff'
      };

      const user = new User(userData);
      user.setDefaultPermissions();
      await user.save();

      expect(user._id).toBeDefined();
      expect(user.username).toBe('testuser');
      expect(user.role).toBe('staff');
      expect(user.permissions).toBeDefined();
      expect(user.permissions.length).toBeGreaterThan(0);
    });

    test('应该能够验证密码', async () => {
      const password = 'testpassword';
      const user = new User({
        username: 'testuser',
        email: '<EMAIL>',
        password: password,
        name: '测试用户',
        role: 'staff'
      });
      await user.save();

      const isValid = await user.comparePassword(password);
      const isInvalid = await user.comparePassword('wrongpassword');

      expect(isValid).toBe(true);
      expect(isInvalid).toBe(false);
    });
  });

  describe('Product模型测试', () => {
    test('应该能够创建产品', async () => {
      const productData = {
        name: '测试产品',
        category: 'produce',
        size: '1kg',
        description: '测试产品描述',
        unit_price: 10.99,
        supplier: '测试供应商',
        safety_stock: 20,
        created_by: adminUser._id,
        updated_by: adminUser._id
      };

      const product = new Product(productData);
      await product.save();

      expect(product._id).toBeDefined();
      expect(product.name).toBe('测试产品');
      expect(product.barcode).toBeDefined(); // 应该自动生成条码
      expect(product.barcode).toMatch(/^P\d{9}$/); // 格式验证
    });

    test('应该能够检查是否需要补货', async () => {
      const product = new Product({
        name: '测试产品',
        category: 'produce',
        safety_stock: 10,
        created_by: adminUser._id
      });
      await product.save();

      expect(product.needsRestock(5)).toBe(true);
      expect(product.needsRestock(15)).toBe(false);
    });

    test('应该能够检查是否临期', async () => {
      const product = new Product({
        name: '测试产品',
        category: 'produce',
        expiry_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15天后过期
        created_by: adminUser._id
      });
      await product.save();

      expect(product.isNearExpiry(30)).toBe(true); // 30天阈值
      expect(product.isNearExpiry(10)).toBe(false); // 10天阈值
    });
  });

  describe('Inventory模型测试', () => {
    let product;

    beforeEach(async () => {
      product = new Product({
        name: '测试产品',
        category: 'produce',
        created_by: adminUser._id
      });
      await product.save();
    });

    test('应该能够创建库存记录', async () => {
      const inventoryData = {
        product_id: product._id,
        current_stock: 100,
        reorder_point: 20,
        cost_per_unit: 5.99,
        updated_by: adminUser._id
      };

      const inventory = new Inventory(inventoryData);
      await inventory.save();

      expect(inventory._id).toBeDefined();
      expect(inventory.current_stock).toBe(100);
      expect(inventory.available_stock).toBe(100);
      expect(inventory.total_value).toBe(599); // 100 * 5.99
    });

    test('应该能够执行入库操作', async () => {
      const inventory = new Inventory({
        product_id: product._id,
        current_stock: 50,
        cost_per_unit: 5.00,
        updated_by: adminUser._id
      });
      await inventory.save();

      inventory.stockIn(30, 6.00, adminUser._id);

      expect(inventory.current_stock).toBe(80);
      expect(inventory.cost_per_unit).toBeCloseTo(5.375); // 加权平均成本
      expect(inventory.last_stock_in).toBeDefined();
    });

    test('应该能够执行出库操作', async () => {
      const inventory = new Inventory({
        product_id: product._id,
        current_stock: 50,
        cost_per_unit: 5.00,
        updated_by: adminUser._id
      });
      await inventory.save();

      inventory.stockOut(20, adminUser._id);

      expect(inventory.current_stock).toBe(30);
      expect(inventory.available_stock).toBe(30);
      expect(inventory.last_stock_out).toBeDefined();
    });

    test('出库数量不能超过可用库存', async () => {
      const inventory = new Inventory({
        product_id: product._id,
        current_stock: 10,
        reserved_stock: 5,
        updated_by: adminUser._id
      });
      await inventory.save();

      expect(() => {
        inventory.stockOut(10, adminUser._id); // 可用库存只有5
      }).toThrow('出库数量不能超过可用库存');
    });
  });

  describe('StockInRecord模型测试', () => {
    let product;

    beforeEach(async () => {
      product = new Product({
        name: '测试产品',
        category: 'produce',
        created_by: adminUser._id
      });
      await product.save();
    });

    test('应该能够创建入库记录', async () => {
      const stockInData = {
        supplier: '测试供应商',
        items: [{
          product_id: product._id,
          quantity: 50,
          unit_cost: 5.99,
          total_cost: 299.50
        }],
        created_by: adminUser._id
      };

      const stockInRecord = new StockInRecord(stockInData);
      await stockInRecord.save();

      expect(stockInRecord._id).toBeDefined();
      expect(stockInRecord.record_number).toBeDefined();
      expect(stockInRecord.record_number).toMatch(/^IN\d+$/);
      expect(stockInRecord.total_quantity).toBe(50);
      expect(stockInRecord.total_amount).toBe(299.50);
    });

    test('应该能够处理入库记录', async () => {
      const stockInRecord = new StockInRecord({
        supplier: '测试供应商',
        items: [{
          product_id: product._id,
          quantity: 50,
          unit_cost: 5.99,
          total_cost: 299.50
        }],
        created_by: adminUser._id
      });
      await stockInRecord.save();

      await stockInRecord.process(adminUser._id);

      expect(stockInRecord.status).toBe('processing');
      expect(stockInRecord.processed_by).toEqual(adminUser._id);
      expect(stockInRecord.processed_date).toBeDefined();
    });
  });

  describe('ReturnRequest模型测试', () => {
    test('应该能够创建退货申请', async () => {
      const returnData = {
        customer_info: {
          name: '张三',
          phone: '13800138000',
          email: '<EMAIL>'
        },
        purchase_info: {
          purchase_date: new Date(),
          pickup_date: new Date()
        },
        items: [{
          product_name: '测试产品',
          quantity: 2,
          unit_price: 10.99,
          total_amount: 21.98,
          reason: 'quality_issue',
          condition: 'damaged'
        }],
        created_by: adminUser._id
      };

      const returnRequest = new ReturnRequest(returnData);
      await returnRequest.save();

      expect(returnRequest._id).toBeDefined();
      expect(returnRequest.request_number).toBeDefined();
      expect(returnRequest.request_number).toMatch(/^RT\d+$/);
      expect(returnRequest.total_quantity).toBe(2);
      expect(returnRequest.total_amount).toBe(21.98);
      expect(returnRequest.timeline.length).toBe(1); // 初始状态记录
    });

    test('应该能够审核通过退货申请', async () => {
      const returnRequest = new ReturnRequest({
        customer_info: {
          name: '张三',
          phone: '13800138000'
        },
        purchase_info: {
          purchase_date: new Date()
        },
        items: [{
          product_name: '测试产品',
          quantity: 1,
          unit_price: 10.99,
          total_amount: 10.99,
          reason: 'quality_issue',
          condition: 'damaged'
        }],
        created_by: adminUser._id
      });
      await returnRequest.save();

      returnRequest.approve(adminUser._id, 10.99, 'original_payment', '质量问题，同意退货');

      expect(returnRequest.status).toBe('approved');
      expect(returnRequest.refund_amount).toBe(10.99);
      expect(returnRequest.refund_method).toBe('original_payment');
      expect(returnRequest.reviewed_by).toEqual(adminUser._id);
      expect(returnRequest.timeline.length).toBe(2); // 初始状态 + 审核状态
    });
  });

  describe('ChatSession模型测试', () => {
    test('应该能够创建聊天会话', async () => {
      const chatSession = new ChatSession({
        customer_info: {
          name: '张三',
          phone: '13800138000'
        },
        channel: 'web_widget',
        language: 'zh-CN'
      });
      await chatSession.save();

      expect(chatSession._id).toBeDefined();
      expect(chatSession.session_id).toBeDefined();
      expect(chatSession.session_id).toMatch(/^chat_/);
      expect(chatSession.status).toBe('active');
    });

    test('应该能够添加消息', async () => {
      const chatSession = new ChatSession({
        customer_info: {
          name: '张三',
          phone: '13800138000'
        }
      });
      await chatSession.save();

      const message = chatSession.addMessage('user', '你好', 'text', { intent: 'greeting' });

      expect(message.message_id).toBeDefined();
      expect(message.sender).toBe('user');
      expect(message.content).toBe('你好');
      expect(chatSession.messages.length).toBe(1);
      expect(chatSession.metrics.total_messages).toBe(1);
      expect(chatSession.metrics.user_messages).toBe(1);
    });

    test('应该能够升级到人工客服', async () => {
      const chatSession = new ChatSession({
        customer_info: {
          name: '张三',
          phone: '13800138000'
        }
      });
      await chatSession.save();

      chatSession.escalateToAgent('复杂问题需要人工处理', adminUser._id);

      expect(chatSession.status).toBe('escalated');
      expect(chatSession.context.escalation_reason).toBe('复杂问题需要人工处理');
      expect(chatSession.assigned_agent).toEqual(adminUser._id);
      expect(chatSession.escalated_at).toBeDefined();
    });
  });

  describe('OperationLog模型测试', () => {
    test('应该能够记录操作日志', async () => {
      const logData = {
        user_id: adminUser._id,
        operation_type: 'product.create',
        resource_type: 'product',
        resource_id: 'test_product_id',
        action: 'create',
        status: 'success',
        ip_address: '127.0.0.1',
        description: '创建测试产品'
      };

      const log = await OperationLog.logOperation(logData);

      expect(log).toBeDefined();
      expect(log.operation_id).toBeDefined();
      expect(log.operation_id).toMatch(/^op_/);
      expect(log.user_id).toEqual(adminUser._id);
      expect(log.operation_type).toBe('product.create');
    });

    test('应该能够记录成功操作', async () => {
      const log = await OperationLog.logSuccess(
        adminUser._id,
        'product.view',
        'product',
        'test_product_id',
        {
          ip_address: '127.0.0.1',
          description: '查看产品详情'
        }
      );

      expect(log).toBeDefined();
      expect(log.status).toBe('success');
      expect(log.operation_type).toBe('product.view');
    });

    test('应该能够记录失败操作', async () => {
      const error = new Error('测试错误');
      const log = await OperationLog.logFailure(
        adminUser._id,
        'product.create',
        'product',
        error,
        {
          ip_address: '127.0.0.1',
          description: '创建产品失败'
        }
      );

      expect(log).toBeDefined();
      expect(log.status).toBe('failure');
      expect(log.error_details.error_message).toBe('测试错误');
    });
  });

  describe('Report模型测试', () => {
    test('应该能够创建报表', async () => {
      const reportData = {
        report_type: 'weekly',
        title: '周报测试',
        description: '测试周报',
        period: {
          start_date: new Date('2023-11-01'),
          end_date: new Date('2023-11-07')
        },
        generated_by: adminUser._id
      };

      const report = new Report(reportData);
      await report.save();

      expect(report._id).toBeDefined();
      expect(report.report_id).toBeDefined();
      expect(report.report_id).toMatch(/^WR\d+$/);
      expect(report.period.year).toBe(2023);
      expect(report.period.month).toBe(10);
    });

    test('应该能够添加数据章节', async () => {
      const report = new Report({
        report_type: 'weekly',
        title: '周报测试',
        period: {
          start_date: new Date('2023-12-01'),
          end_date: new Date('2023-12-07')
        },
        generated_by: adminUser._id
      });
      await report.save();

      report.addDataSection('summary', '摘要', { total_products: 10 });

      expect(report.data_sections.length).toBe(1);
      expect(report.data_sections[0].section).toBe('summary');
      expect(report.data_sections[0].title).toBe('摘要');
      expect(report.data_sections[0].data.total_products).toBe(10);
    });

    test('应该能够标记报表完成', async () => {
      const report = new Report({
        report_type: 'weekly',
        title: '周报测试',
        period: {
          start_date: new Date('2023-12-01'),
          end_date: new Date('2023-12-07')
        },
        generated_by: adminUser._id
      });
      await report.save();

      report.markCompleted(5000); // 5秒生成时间

      expect(report.status).toBe('completed');
      expect(report.generation_time_ms).toBe(5000);
    });
  });
});
