/**
 * 报表功能测试脚本
 * 测试报表生成、查询等功能
 */

require('dotenv').config();
const mongoose = require('mongoose');
const reportService = require('./src/services/reportService');
const { Report, User } = require('./src/models');
const logger = require('./src/utils/logger');

// 测试配置
const TEST_CONFIG = {
  MONGO_URI: process.env.MONGO_URI || 'mongodb://localhost:27017/inventory_ai_db',
  TEST_USER_ID: null
};

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(TEST_CONFIG.MONGO_URI);
    console.log('✅ 数据库连接成功');
    
    // 获取测试用户
    const testUser = await User.findOne({ role: 'admin' });
    if (testUser) {
      TEST_CONFIG.TEST_USER_ID = testUser._id;
      console.log(`✅ 找到测试用户: ${testUser.username}`);
    } else {
      console.log('⚠️  未找到管理员用户，将创建测试用户');
      const newUser = new User({
        username: 'test_admin',
        email: '<EMAIL>',
        password: 'test123',
        role: 'admin',
        name: '测试管理员'
      });
      await newUser.save();
      TEST_CONFIG.TEST_USER_ID = newUser._id;
      console.log('✅ 创建测试用户成功');
    }
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 测试周报生成
 */
async function testWeeklyReportGeneration() {
  console.log('\n📊 测试周报生成...');
  
  try {
    // 计算上周的时间范围
    const now = new Date();
    const lastWeekStart = new Date(now);
    lastWeekStart.setDate(now.getDate() - 7);
    lastWeekStart.setHours(0, 0, 0, 0);
    
    const lastWeekEnd = new Date(now);
    lastWeekEnd.setHours(23, 59, 59, 999);
    
    const result = await reportService.generateWeeklyReport({
      startDate: lastWeekStart,
      endDate: lastWeekEnd,
      generatedBy: TEST_CONFIG.TEST_USER_ID,
      filters: {}
    });
    
    if (result.success) {
      console.log('✅ 周报生成成功');
      console.log(`   报表ID: ${result.data.report.report_id}`);
      console.log(`   生成时间: ${result.data.generationTime}ms`);
      console.log(`   数据章节: ${result.data.report.data_sections.length}个`);
      return result.data.report;
    } else {
      console.log('❌ 周报生成失败:', result.error);
      return null;
    }
    
  } catch (error) {
    console.error('❌ 周报生成测试失败:', error.message);
    return null;
  }
}

/**
 * 测试月报生成
 */
async function testMonthlyReportGeneration() {
  console.log('\n📊 测试月报生成...');
  
  try {
    // 计算上个月的时间范围
    const now = new Date();
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
    lastMonthEnd.setHours(23, 59, 59, 999);
    
    const result = await reportService.generateMonthlyReport({
      startDate: lastMonthStart,
      endDate: lastMonthEnd,
      generatedBy: TEST_CONFIG.TEST_USER_ID,
      filters: {}
    });
    
    if (result.success) {
      console.log('✅ 月报生成成功');
      console.log(`   报表ID: ${result.data.report.report_id}`);
      console.log(`   生成时间: ${result.data.generationTime}ms`);
      console.log(`   数据章节: ${result.data.report.data_sections.length}个`);
      return result.data.report;
    } else {
      console.log('❌ 月报生成失败:', result.error);
      return null;
    }
    
  } catch (error) {
    console.error('❌ 月报生成测试失败:', error.message);
    return null;
  }
}

/**
 * 测试自定义报表生成
 */
async function testCustomReportGeneration() {
  console.log('\n📊 测试自定义报表生成...');
  
  try {
    const now = new Date();
    const startDate = new Date(now);
    startDate.setDate(now.getDate() - 3);
    startDate.setHours(0, 0, 0, 0);
    
    const endDate = new Date(now);
    endDate.setHours(23, 59, 59, 999);
    
    const result = await reportService.generateCustomReport({
      startDate,
      endDate,
      generatedBy: TEST_CONFIG.TEST_USER_ID,
      title: '测试自定义报表',
      description: '这是一个测试用的自定义报表',
      sections: ['summary', 'inventory', 'alerts'],
      filters: {}
    });
    
    if (result.success) {
      console.log('✅ 自定义报表生成成功');
      console.log(`   报表ID: ${result.data.report.report_id}`);
      console.log(`   生成时间: ${result.data.generationTime}ms`);
      console.log(`   数据章节: ${result.data.report.data_sections.length}个`);
      return result.data.report;
    } else {
      console.log('❌ 自定义报表生成失败:', result.error);
      return null;
    }
    
  } catch (error) {
    console.error('❌ 自定义报表生成测试失败:', error.message);
    return null;
  }
}

/**
 * 测试报表查询
 */
async function testReportQuery() {
  console.log('\n🔍 测试报表查询...');
  
  try {
    // 查询所有报表
    const reports = await Report.find({})
      .sort({ generated_at: -1 })
      .limit(5)
      .populate('generated_by', 'username name');
    
    console.log(`✅ 查询到 ${reports.length} 个报表`);
    
    reports.forEach((report, index) => {
      console.log(`   ${index + 1}. ${report.report_id} (${report.report_type}) - ${report.status}`);
      console.log(`      生成时间: ${report.generated_at.toLocaleString()}`);
      console.log(`      生成者: ${report.generated_by?.name || '未知'}`);
    });
    
    return reports;
    
  } catch (error) {
    console.error('❌ 报表查询测试失败:', error.message);
    return [];
  }
}

/**
 * 测试报表详情查看
 */
async function testReportDetails(reportId) {
  console.log('\n📋 测试报表详情查看...');
  
  try {
    const report = await Report.findById(reportId)
      .populate('generated_by', 'username name');
    
    if (report) {
      console.log('✅ 报表详情获取成功');
      console.log(`   报表ID: ${report.report_id}`);
      console.log(`   标题: ${report.title}`);
      console.log(`   类型: ${report.report_type}`);
      console.log(`   状态: ${report.status}`);
      console.log(`   时间范围: ${report.period.start_date.toLocaleDateString()} - ${report.period.end_date.toLocaleDateString()}`);
      console.log(`   数据章节数: ${report.data_sections.length}`);
      
      // 显示摘要数据
      if (report.summary) {
        console.log('   摘要数据:');
        console.log(`     产品总数: ${report.summary.total_products}`);
        console.log(`     库存总值: ${report.summary.total_stock_value}`);
        console.log(`     入库次数: ${report.summary.stock_in_count}`);
        console.log(`     退货申请: ${report.summary.return_requests}`);
      }
      
      return report;
    } else {
      console.log('❌ 报表不存在');
      return null;
    }
    
  } catch (error) {
    console.error('❌ 报表详情查看测试失败:', error.message);
    return null;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始报表功能测试\n');
  
  try {
    // 连接数据库
    await connectDatabase();
    
    // 测试报表生成
    const weeklyReport = await testWeeklyReportGeneration();
    const monthlyReport = await testMonthlyReportGeneration();
    const customReport = await testCustomReportGeneration();
    
    // 测试报表查询
    await testReportQuery();
    
    // 测试报表详情查看
    if (weeklyReport) {
      await testReportDetails(weeklyReport._id);
    }
    
    console.log('\n🎉 所有测试完成！');
    
    // 测试结果统计
    const successCount = [weeklyReport, monthlyReport, customReport].filter(Boolean).length;
    console.log(`\n📊 测试结果统计:`);
    console.log(`   成功生成报表: ${successCount}/3`);
    console.log(`   测试通过率: ${(successCount / 3 * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('\n✅ 数据库连接已关闭');
    process.exit(0);
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  testWeeklyReportGeneration,
  testMonthlyReportGeneration,
  testCustomReportGeneration,
  testReportQuery,
  testReportDetails
};
