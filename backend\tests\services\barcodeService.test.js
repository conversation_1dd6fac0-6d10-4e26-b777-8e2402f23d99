const barcodeService = require('../../src/services/barcodeService');

describe('BarcodeService', () => {
  describe('generateBarcode', () => {
    it('应该生成有效的EAN-13条码', () => {
      const barcode = barcodeService.generateBarcode('EAN13');
      
      expect(barcode).toHaveLength(13);
      expect(/^\d{13}$/.test(barcode)).toBe(true);
      
      // 验证校验位
      const validation = barcodeService.validateBarcode(barcode, 'EAN13');
      expect(validation.isValid).toBe(true);
    });

    it('应该生成有效的EAN-8条码', () => {
      const barcode = barcodeService.generateBarcode('EAN8');
      
      expect(barcode).toHaveLength(8);
      expect(/^\d{8}$/.test(barcode)).toBe(true);
      
      // 验证校验位
      const validation = barcodeService.validateBarcode(barcode, 'EAN8');
      expect(validation.isValid).toBe(true);
    });

    it('应该生成有效的UPC-A条码', () => {
      const barcode = barcodeService.generateBarcode('UPCA');
      
      expect(barcode).toHaveLength(12);
      expect(/^\d{12}$/.test(barcode)).toBe(true);
      
      // 验证校验位
      const validation = barcodeService.validateBarcode(barcode, 'UPCA');
      expect(validation.isValid).toBe(true);
    });

    it('应该生成Code 128条码', () => {
      const barcode = barcodeService.generateBarcode('CODE128');
      
      expect(barcode).toBeTruthy();
      expect(typeof barcode).toBe('string');
      
      const validation = barcodeService.validateBarcode(barcode, 'CODE128');
      expect(validation.isValid).toBe(true);
    });

    it('应该生成Code 39条码', () => {
      const barcode = barcodeService.generateBarcode('CODE39');
      
      expect(barcode).toBeTruthy();
      expect(typeof barcode).toBe('string');
      expect(/^[A-Z0-9\-\.\$\/\+\%\s]+$/.test(barcode)).toBe(true);
      
      const validation = barcodeService.validateBarcode(barcode, 'CODE39');
      expect(validation.isValid).toBe(true);
    });

    it('应该使用前缀生成条码', () => {
      const prefix = '123';
      const barcode = barcodeService.generateBarcode('EAN13', prefix);
      
      expect(barcode.startsWith(prefix)).toBe(true);
      expect(barcode).toHaveLength(13);
      
      const validation = barcodeService.validateBarcode(barcode, 'EAN13');
      expect(validation.isValid).toBe(true);
    });

    it('应该处理不支持的格式', () => {
      expect(() => {
        barcodeService.generateBarcode('INVALID_FORMAT');
      }).toThrow('不支持的条码格式: INVALID_FORMAT');
    });

    it('应该使用默认格式', () => {
      const barcode = barcodeService.generateBarcode();
      
      expect(barcode).toHaveLength(13);
      expect(/^\d{13}$/.test(barcode)).toBe(true);
    });
  });

  describe('validateBarcode', () => {
    it('应该验证有效的EAN-13条码', () => {
      // 使用已知的有效EAN-13条码
      const validEAN13 = '1234567890128'; // 校验位正确
      const result = barcodeService.validateBarcode(validEAN13, 'EAN13');
      
      expect(result.isValid).toBe(true);
      expect(result.format).toBe('EAN13');
      expect(result.error).toBeNull();
    });

    it('应该验证无效的EAN-13条码', () => {
      const invalidEAN13 = '1234567890123'; // 校验位错误
      const result = barcodeService.validateBarcode(invalidEAN13, 'EAN13');
      
      expect(result.isValid).toBe(false);
      expect(result.format).toBeNull();
      expect(result.error).toBeTruthy();
    });

    it('应该验证有效的EAN-8条码', () => {
      const validEAN8 = '12345670'; // 校验位正确
      const result = barcodeService.validateBarcode(validEAN8, 'EAN8');
      
      expect(result.isValid).toBe(true);
      expect(result.format).toBe('EAN8');
    });

    it('应该验证有效的UPC-A条码', () => {
      const validUPCA = '123456789012'; // 校验位正确
      const result = barcodeService.validateBarcode(validUPCA, 'UPCA');
      
      expect(result.isValid).toBe(true);
      expect(result.format).toBe('UPCA');
    });

    it('应该自动检测条码格式', () => {
      const ean13 = '1234567890128';
      const result = barcodeService.validateBarcode(ean13);
      
      expect(result.isValid).toBe(true);
      expect(result.format).toBe('EAN13');
    });

    it('应该处理空条码', () => {
      const result = barcodeService.validateBarcode('');
      
      expect(result.isValid).toBe(false);
      expect(result.format).toBeNull();
      expect(result.error).toBe('条码不能为空');
    });

    it('应该处理null条码', () => {
      const result = barcodeService.validateBarcode(null);
      
      expect(result.isValid).toBe(false);
      expect(result.format).toBeNull();
      expect(result.error).toBe('条码不能为空');
    });

    it('应该处理不支持的格式', () => {
      const result = barcodeService.validateBarcode('123456789', 'INVALID');
      
      expect(result.isValid).toBe(false);
      expect(result.format).toBeNull();
      expect(result.error).toBe('不支持的条码格式: INVALID');
    });
  });

  describe('calculateEAN13CheckDigit', () => {
    it('应该正确计算EAN-13校验位', () => {
      const data = '123456789012';
      const checkDigit = barcodeService.calculateEAN13CheckDigit(data);
      
      expect(checkDigit).toBe('8');
    });

    it('应该为不同数据计算不同校验位', () => {
      const data1 = '123456789012';
      const data2 = '987654321098';
      
      const checkDigit1 = barcodeService.calculateEAN13CheckDigit(data1);
      const checkDigit2 = barcodeService.calculateEAN13CheckDigit(data2);
      
      expect(checkDigit1).not.toBe(checkDigit2);
    });
  });

  describe('calculateEAN8CheckDigit', () => {
    it('应该正确计算EAN-8校验位', () => {
      const data = '1234567';
      const checkDigit = barcodeService.calculateEAN8CheckDigit(data);
      
      expect(checkDigit).toBe('0');
    });
  });

  describe('calculateUPCACheckDigit', () => {
    it('应该正确计算UPC-A校验位', () => {
      const data = '12345678901';
      const checkDigit = barcodeService.calculateUPCACheckDigit(data);
      
      expect(checkDigit).toBe('2');
    });
  });

  describe('getSupportedFormats', () => {
    it('应该返回支持的格式列表', () => {
      const formats = barcodeService.getSupportedFormats();
      
      expect(Array.isArray(formats)).toBe(true);
      expect(formats.length).toBeGreaterThan(0);
      
      formats.forEach(format => {
        expect(format).toHaveProperty('code');
        expect(format).toHaveProperty('name');
        expect(format).toHaveProperty('description');
      });
    });

    it('应该包含EAN-13格式', () => {
      const formats = barcodeService.getSupportedFormats();
      const ean13 = formats.find(f => f.code === 'EAN13');
      
      expect(ean13).toBeTruthy();
      expect(ean13.name).toBe('EAN-13');
      expect(ean13.length).toBe(13);
    });
  });

  describe('generateBatchBarcodes', () => {
    it('应该生成指定数量的条码', () => {
      const count = 5;
      const barcodes = barcodeService.generateBatchBarcodes(count);
      
      expect(barcodes).toHaveLength(count);
      
      // 验证每个条码都是有效的
      barcodes.forEach(barcode => {
        const validation = barcodeService.validateBarcode(barcode);
        expect(validation.isValid).toBe(true);
      });
    });

    it('应该生成唯一的条码', () => {
      const count = 10;
      const barcodes = barcodeService.generateBatchBarcodes(count);
      
      const uniqueBarcodes = new Set(barcodes);
      expect(uniqueBarcodes.size).toBe(count);
    });

    it('应该使用指定的格式', () => {
      const count = 3;
      const format = 'EAN8';
      const barcodes = barcodeService.generateBatchBarcodes(count, format);
      
      barcodes.forEach(barcode => {
        expect(barcode).toHaveLength(8);
        const validation = barcodeService.validateBarcode(barcode, format);
        expect(validation.isValid).toBe(true);
      });
    });

    it('应该使用前缀', () => {
      const count = 3;
      const prefix = '999';
      const barcodes = barcodeService.generateBatchBarcodes(count, 'EAN13', prefix);
      
      barcodes.forEach(barcode => {
        expect(barcode.startsWith(prefix)).toBe(true);
      });
    });

    it('应该处理无效的数量', () => {
      expect(() => {
        barcodeService.generateBatchBarcodes(0);
      }).toThrow('生成数量必须在1-1000之间');

      expect(() => {
        barcodeService.generateBatchBarcodes(1001);
      }).toThrow('生成数量必须在1-1000之间');
    });
  });
});
