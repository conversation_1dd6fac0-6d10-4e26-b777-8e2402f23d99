#!/bin/bash

# ===========================================
# Chat-AI-3.0 项目部署脚本
# ===========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

# 检查必需的工具
check_requirements() {
    log "检查部署环境..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js 未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        error "npm 未安装"
        exit 1
    fi
    
    # 检查git
    if ! command -v git &> /dev/null; then
        error "git 未安装"
        exit 1
    fi
    
    success "环境检查通过"
}

# 部署前端到Vercel
deploy_frontend() {
    log "开始部署前端到Vercel..."
    
    cd frontend
    
    # 检查Vercel CLI
    if ! command -v vercel &> /dev/null; then
        warning "Vercel CLI 未安装，正在安装..."
        npm install -g vercel
    fi
    
    # 构建前端
    log "构建前端应用..."
    npm run build:prod
    
    # 部署到Vercel
    log "部署到Vercel..."
    vercel --prod --yes
    
    cd ..
    success "前端部署完成"
}

# 部署后端到Render
deploy_backend() {
    log "开始部署后端..."
    
    cd backend
    
    # 运行测试
    log "运行测试..."
    npm test
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        warning "PM2 未安装，正在安装..."
        npm install -g pm2
    fi
    
    # 启动PM2进程
    log "启动后端服务..."
    pm2 start ecosystem.config.js --env production
    
    cd ..
    success "后端部署完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    # 等待服务启动
    sleep 10
    
    # 检查后端健康状态
    if curl -f http://localhost:4000/health > /dev/null 2>&1; then
        success "后端服务健康检查通过"
    else
        error "后端服务健康检查失败"
        exit 1
    fi
}

# 主部署函数
main() {
    log "🚀 开始 Chat-AI-3.0 项目部署"
    
    # 检查参数
    DEPLOY_TARGET=${1:-"all"}
    
    case $DEPLOY_TARGET in
        "frontend")
            check_requirements
            deploy_frontend
            ;;
        "backend")
            check_requirements
            deploy_backend
            health_check
            ;;
        "all")
            check_requirements
            deploy_frontend
            deploy_backend
            health_check
            ;;
        *)
            error "无效的部署目标: $DEPLOY_TARGET"
            echo "用法: $0 [frontend|backend|all]"
            exit 1
            ;;
    esac
    
    success "🎉 部署完成！"
    
    # 显示访问信息
    log "访问信息:"
    log "  前端: https://your-vercel-domain.vercel.app"
    log "  后端: https://your-render-domain.onrender.com"
    log "  健康检查: https://your-render-domain.onrender.com/health"
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
