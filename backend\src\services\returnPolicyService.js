const policyService = require('./policyService');
const aiService = require('./aiService');
const logger = require('../utils/logger');

/**
 * 退货政策服务类
 * 处理退货政策匹配、AI辅助判断和智能建议
 */
class ReturnPolicyService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 10 * 60 * 1000; // 10分钟缓存
  }

  /**
   * AI辅助退货原因分析
   * @param {string} reason - 退货原因描述
   * @param {Object} productInfo - 产品信息
   * @param {Object} purchaseInfo - 购买信息
   * @returns {Promise<Object>} 分析结果
   */
  async analyzeReturnReason(reason, productInfo = {}, purchaseInfo = {}) {
    try {
      logger.info('开始AI退货原因分析', { reason: reason.substring(0, 50) });

      // 构建分析提示词
      const analysisPrompt = `请分析以下退货原因，并返回JSON格式的分析结果：

退货原因："${reason}"

产品信息：
- 产品名称：${productInfo.name || '未提供'}
- 产品类别：${productInfo.category || '未提供'}
- 产品价格：${productInfo.price || '未提供'}

购买信息：
- 购买日期：${purchaseInfo.purchase_date || '未提供'}
- 取货日期：${purchaseInfo.pickup_date || '未提供'}

请返回以下格式的JSON：
{
  "category": "退货类别",
  "severity": "严重程度",
  "is_quality_issue": true/false,
  "is_valid_reason": true/false,
  "confidence": 0.8,
  "suggested_action": "建议处理方式",
  "policy_match": "匹配的政策条款",
  "refund_eligibility": "退款资格评估",
  "keywords": ["关键词1", "关键词2"]
}

退货类别包括：
- quality_issue: 质量问题
- damaged: 商品损坏
- wrong_item: 商品错误
- not_satisfied: 不满意
- expired: 过期
- other: 其他

严重程度：low, medium, high
建议处理方式：full_refund, partial_refund, replacement, reject

只返回JSON，不要其他内容。`;

      const aiResponse = await aiService.chat([
        { role: 'user', content: analysisPrompt }
      ], {
        temperature: 0.3,
        max_tokens: 500
      });

      if (aiResponse.success) {
        try {
          // 尝试提取JSON内容
          let jsonContent = aiResponse.content;

          // 如果响应包含非JSON内容，尝试提取JSON部分
          const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            jsonContent = jsonMatch[0];
          }

          const analysis = JSON.parse(jsonContent);

          // 验证和补充分析结果
          const validatedAnalysis = this.validateAnalysisResult(analysis, reason);

          logger.info('AI退货原因分析完成', {
            category: validatedAnalysis.category,
            severity: validatedAnalysis.severity,
            confidence: validatedAnalysis.confidence
          });

          return {
            success: true,
            ...validatedAnalysis,
            ai_response_time: aiResponse.response_time_ms
          };

        } catch (parseError) {
          logger.error('AI分析结果解析失败', parseError);
          logger.error('原始AI响应:', aiResponse.content);
          return this.getFallbackAnalysis(reason);
        }
      }

      return this.getFallbackAnalysis(reason);

    } catch (error) {
      logger.error('AI退货原因分析失败', error);
      return this.getFallbackAnalysis(reason);
    }
  }

  /**
   * 匹配退货政策
   * @param {Object} analysisResult - AI分析结果
   * @param {Object} returnRequest - 退货申请信息
   * @returns {Promise<Object>} 政策匹配结果
   */
  async matchReturnPolicy(analysisResult, returnRequest) {
    try {
      logger.info('开始退货政策匹配', { category: analysisResult.category });

      // 搜索相关退货政策
      const policies = await policyService.searchPolicies('退货 售后', {
        category: 'after_sale',
        activeOnly: true,
        limit: 5
      });

      if (policies.length === 0) {
        return {
          success: false,
          message: '未找到相关退货政策',
          policies: []
        };
      }

      // 分析政策匹配度
      const policyMatches = [];
      
      for (const policy of policies) {
        const matchScore = this.calculatePolicyMatchScore(
          analysisResult, 
          returnRequest, 
          policy
        );

        if (matchScore > 0.3) { // 只保留匹配度较高的政策
          policyMatches.push({
            policy,
            matchScore,
            applicableRules: this.extractApplicableRules(policy, analysisResult)
          });
        }
      }

      // 按匹配度排序
      policyMatches.sort((a, b) => b.matchScore - a.matchScore);

      logger.info('退货政策匹配完成', {
        totalPolicies: policies.length,
        matchedPolicies: policyMatches.length
      });

      return {
        success: true,
        policies: policyMatches,
        bestMatch: policyMatches[0] || null
      };

    } catch (error) {
      logger.error('退货政策匹配失败', error);
      return {
        success: false,
        message: '政策匹配失败',
        policies: []
      };
    }
  }

  /**
   * 生成AI退货判断
   * @param {Object} analysisResult - 原因分析结果
   * @param {Object} policyMatch - 政策匹配结果
   * @param {Object} returnRequest - 退货申请
   * @returns {Promise<Object>} AI判断结果
   */
  async generateReturnDecision(analysisResult, policyMatch, returnRequest) {
    try {
      logger.info('开始生成AI退货判断');

      // 构建判断上下文
      const context = this.buildDecisionContext(analysisResult, policyMatch, returnRequest);

      const decisionPrompt = `基于以下信息，请对退货申请做出判断并返回JSON格式结果：

${context}

请返回以下格式的JSON：
{
  "decision": "approve/reject/review_required",
  "confidence": 0.8,
  "reasoning": "判断理由",
  "recommended_action": "建议处理方式",
  "refund_amount": 退款金额,
  "refund_percentage": 退款比例,
  "conditions": ["退款条件1", "退款条件2"],
  "next_steps": ["后续步骤1", "后续步骤2"],
  "policy_references": ["引用的政策条款"],
  "risk_level": "low/medium/high"
}

判断标准：
- approve: 符合退货政策，建议批准
- reject: 不符合退货政策，建议拒绝
- review_required: 需要人工审核

只返回JSON，不要其他内容。`;

      const aiResponse = await aiService.chat([
        { role: 'user', content: decisionPrompt }
      ], {
        temperature: 0.2,
        max_tokens: 800
      });

      if (aiResponse.success) {
        try {
          // 尝试提取JSON内容
          let jsonContent = aiResponse.content;

          // 如果响应包含非JSON内容，尝试提取JSON部分
          const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            jsonContent = jsonMatch[0];
          }

          const decision = JSON.parse(jsonContent);

          // 验证和补充决策结果
          const validatedDecision = this.validateDecisionResult(decision, returnRequest);

          logger.info('AI退货判断完成', {
            decision: validatedDecision.decision,
            confidence: validatedDecision.confidence,
            refundAmount: validatedDecision.refund_amount
          });

          return {
            success: true,
            ...validatedDecision,
            ai_response_time: aiResponse.response_time_ms
          };

        } catch (parseError) {
          logger.error('AI判断结果解析失败', parseError);
          logger.error('原始AI响应:', aiResponse.content);
          return this.getFallbackDecision(analysisResult, returnRequest);
        }
      }

      return this.getFallbackDecision(analysisResult, returnRequest);

    } catch (error) {
      logger.error('生成AI退货判断失败', error);
      return this.getFallbackDecision(analysisResult, returnRequest);
    }
  }

  /**
   * 生成智能表单填充建议
   * @param {Object} analysisResult - 原因分析结果
   * @param {Object} decisionResult - 判断结果
   * @returns {Object} 表单填充建议
   */
  generateFormSuggestions(analysisResult, decisionResult) {
    try {
      const suggestions = {
        priority: this.suggestPriority(analysisResult),
        refund_method: this.suggestRefundMethod(analysisResult, decisionResult),
        processing_notes: this.generateProcessingNotes(analysisResult, decisionResult),
        follow_up_actions: this.suggestFollowUpActions(analysisResult, decisionResult),
        customer_communication: this.generateCustomerMessage(analysisResult, decisionResult)
      };

      logger.info('生成表单填充建议完成');
      return suggestions;

    } catch (error) {
      logger.error('生成表单填充建议失败', error);
      return {
        priority: 'medium',
        refund_method: 'original',
        processing_notes: '请人工审核',
        follow_up_actions: [],
        customer_communication: '我们正在处理您的退货申请，请耐心等待。'
      };
    }
  }

  /**
   * 验证分析结果
   */
  validateAnalysisResult(analysis, originalReason) {
    const validCategories = ['quality_issue', 'damaged', 'wrong_item', 'not_satisfied', 'expired', 'other'];
    const validSeverities = ['low', 'medium', 'high'];
    const validActions = ['full_refund', 'partial_refund', 'replacement', 'reject'];

    return {
      category: validCategories.includes(analysis.category) ? analysis.category : 'other',
      severity: validSeverities.includes(analysis.severity) ? analysis.severity : 'medium',
      is_quality_issue: Boolean(analysis.is_quality_issue),
      is_valid_reason: Boolean(analysis.is_valid_reason),
      confidence: Math.min(Math.max(analysis.confidence || 0.5, 0), 1),
      suggested_action: validActions.includes(analysis.suggested_action) ? analysis.suggested_action : 'full_refund',
      policy_match: analysis.policy_match || '需要进一步审核',
      refund_eligibility: analysis.refund_eligibility || '待评估',
      keywords: Array.isArray(analysis.keywords) ? analysis.keywords : []
    };
  }

  /**
   * 验证决策结果
   */
  validateDecisionResult(decision, returnRequest) {
    const validDecisions = ['approve', 'reject', 'review_required'];
    const validRiskLevels = ['low', 'medium', 'high'];

    return {
      decision: validDecisions.includes(decision.decision) ? decision.decision : 'review_required',
      confidence: Math.min(Math.max(decision.confidence || 0.5, 0), 1),
      reasoning: decision.reasoning || '需要进一步分析',
      recommended_action: decision.recommended_action || '人工审核',
      refund_amount: Math.max(decision.refund_amount || 0, 0),
      refund_percentage: Math.min(Math.max(decision.refund_percentage || 100, 0), 100),
      conditions: Array.isArray(decision.conditions) ? decision.conditions : [],
      next_steps: Array.isArray(decision.next_steps) ? decision.next_steps : [],
      policy_references: Array.isArray(decision.policy_references) ? decision.policy_references : [],
      risk_level: validRiskLevels.includes(decision.risk_level) ? decision.risk_level : 'medium'
    };
  }

  /**
   * 获取备用分析结果
   */
  getFallbackAnalysis(reason) {
    const keywords = reason.toLowerCase().split(/\s+/);
    let category = 'other';
    let severity = 'medium';
    let isQualityIssue = false;

    // 简单的关键词匹配
    if (keywords.some(word => ['质量', '坏', '烂', '变质'].includes(word))) {
      category = 'quality_issue';
      severity = 'high';
      isQualityIssue = true;
    } else if (keywords.some(word => ['错', '不对', '发错'].includes(word))) {
      category = 'wrong_item';
      severity = 'medium';
    } else if (keywords.some(word => ['不满意', '不喜欢'].includes(word))) {
      category = 'not_satisfied';
      severity = 'low';
    }

    return {
      success: true,
      category,
      severity,
      is_quality_issue: isQualityIssue,
      is_valid_reason: true,
      confidence: 0.6,
      suggested_action: isQualityIssue ? 'full_refund' : 'partial_refund',
      policy_match: '基于基础规则匹配',
      refund_eligibility: '符合基本退货条件',
      keywords: keywords.slice(0, 5)
    };
  }

  /**
   * 获取备用决策结果
   */
  getFallbackDecision(analysisResult, returnRequest) {
    return {
      success: true,
      decision: 'review_required',
      confidence: 0.5,
      reasoning: '需要人工审核以确保准确性',
      recommended_action: '人工审核',
      refund_amount: returnRequest.total_amount || 0,
      refund_percentage: 100,
      conditions: ['需要提供购买凭证', '需要确认商品状态'],
      next_steps: ['联系客户确认详情', '安排人工审核'],
      policy_references: ['标准退货政策'],
      risk_level: 'medium'
    };
  }

  /**
   * 计算政策匹配分数
   */
  calculatePolicyMatchScore(analysisResult, returnRequest, policy) {
    let score = 0;

    // 基于类别匹配
    if (policy.category === 'after_sale') {
      score += 0.3;
    }

    // 基于关键词匹配
    const policyContent = JSON.stringify(policy.current_content).toLowerCase();
    const keywords = analysisResult.keywords || [];
    
    for (const keyword of keywords) {
      if (policyContent.includes(keyword.toLowerCase())) {
        score += 0.1;
      }
    }

    // 基于质量问题匹配
    if (analysisResult.is_quality_issue && policyContent.includes('质量')) {
      score += 0.2;
    }

    return Math.min(score, 1.0);
  }

  /**
   * 提取适用规则
   */
  extractApplicableRules(policy, analysisResult) {
    const rules = [];
    
    if (Array.isArray(policy.current_content)) {
      for (const item of policy.current_content) {
        if (typeof item === 'string') {
          const itemLower = item.toLowerCase();
          
          // 检查是否与分析结果相关
          if (analysisResult.is_quality_issue && itemLower.includes('质量')) {
            rules.push(item);
          } else if (analysisResult.category === 'expired' && itemLower.includes('时间')) {
            rules.push(item);
          } else if (itemLower.includes('退货') || itemLower.includes('退款')) {
            rules.push(item);
          }
        }
      }
    }

    return rules;
  }

  /**
   * 构建决策上下文
   */
  buildDecisionContext(analysisResult, policyMatch, returnRequest) {
    let context = `退货原因分析：
- 类别：${analysisResult.category}
- 严重程度：${analysisResult.severity}
- 是否质量问题：${analysisResult.is_quality_issue ? '是' : '否'}
- 置信度：${analysisResult.confidence}

`;

    if (policyMatch.success && policyMatch.bestMatch) {
      context += `匹配的政策：
- 政策名称：${policyMatch.bestMatch.policy.name}
- 匹配度：${policyMatch.bestMatch.matchScore.toFixed(2)}
- 适用规则：${policyMatch.bestMatch.applicableRules.join('; ')}

`;
    }

    context += `退货申请信息：
- 申请金额：${returnRequest.total_amount || 0}
- 商品数量：${returnRequest.items?.length || 0}
- 购买时间：${returnRequest.purchase_info?.purchase_date || '未知'}

`;

    return context;
  }

  /**
   * 建议优先级
   */
  suggestPriority(analysisResult) {
    if (analysisResult.severity === 'high' || analysisResult.is_quality_issue) {
      return 'high';
    } else if (analysisResult.severity === 'medium') {
      return 'medium';
    }
    return 'low';
  }

  /**
   * 建议退款方式
   */
  suggestRefundMethod(analysisResult, decisionResult) {
    if (analysisResult.is_quality_issue) {
      return 'original'; // 原路退回
    } else if (decisionResult.refund_percentage < 100) {
      return 'credit'; // 积分退款
    }
    return 'original';
  }

  /**
   * 生成处理备注
   */
  generateProcessingNotes(analysisResult, decisionResult) {
    const notes = [];
    
    notes.push(`AI分析：${analysisResult.category} (置信度: ${analysisResult.confidence})`);
    notes.push(`建议：${decisionResult.recommended_action}`);
    
    if (analysisResult.is_quality_issue) {
      notes.push('质量问题，优先处理');
    }
    
    if (decisionResult.risk_level === 'high') {
      notes.push('高风险申请，需要额外审核');
    }

    return notes.join('; ');
  }

  /**
   * 建议后续动作
   */
  suggestFollowUpActions(analysisResult, decisionResult) {
    const actions = [];
    
    if (analysisResult.is_quality_issue) {
      actions.push('联系供应商反馈质量问题');
      actions.push('更新产品质量记录');
    }
    
    if (decisionResult.decision === 'approve') {
      actions.push('处理退款');
      actions.push('更新库存');
    }
    
    actions.push('发送处理结果通知给客户');
    
    return actions;
  }

  /**
   * 生成客户沟通消息
   */
  generateCustomerMessage(analysisResult, decisionResult) {
    if (decisionResult.decision === 'approve') {
      return `您的退货申请已通过审核。我们将按照${decisionResult.refund_percentage}%的比例进行退款处理。感谢您的理解。`;
    } else if (decisionResult.decision === 'reject') {
      return `很抱歉，您的退货申请不符合我们的退货政策。如有疑问，请联系客服。`;
    } else {
      return `我们正在审核您的退货申请，预计在24小时内给您回复。感谢您的耐心等待。`;
    }
  }
}

module.exports = new ReturnPolicyService();
