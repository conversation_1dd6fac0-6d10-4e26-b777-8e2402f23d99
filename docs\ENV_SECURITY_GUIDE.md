# 🔒 环境变量安全管理指南

## 📋 概述
本指南提供生产环境中环境变量的安全管理最佳实践。

## 🔐 安全原则

### 1. 密钥管理
- ✅ 使用强密码（至少32字符）
- ✅ 定期轮换密钥
- ✅ 不同环境使用不同密钥
- ❌ 永远不要在代码中硬编码密钥

### 2. 访问控制
- ✅ 最小权限原则
- ✅ 基于角色的访问控制
- ✅ 审计日志记录
- ❌ 避免共享账户

### 3. 存储安全
- ✅ 使用专业的密钥管理服务
- ✅ 加密存储敏感信息
- ✅ 定期备份配置
- ❌ 不要将.env文件提交到版本控制

## 🛠️ 推荐的密钥管理服务

### 云服务提供商
1. **AWS Secrets Manager**
   - 自动密钥轮换
   - 细粒度访问控制
   - 审计日志

2. **Azure Key Vault**
   - 硬件安全模块(HSM)
   - 证书管理
   - 密钥版本控制

3. **Google Secret Manager**
   - 自动加密
   - IAM集成
   - 全球复制

### 开源解决方案
1. **HashiCorp Vault**
   - 动态密钥生成
   - 密钥租约管理
   - 多种认证方式

2. **Docker Secrets**
   - 容器化环境
   - 运行时注入
   - 加密传输

## 🔧 部署平台配置

### Vercel（前端）
```bash
# 通过Vercel CLI设置环境变量
vercel env add VITE_API_BASE_URL production
vercel env add VITE_SOCKET_URL production
```

### Render（后端）
```bash
# 在Render Dashboard中配置环境变量
# 1. 进入Service Settings
# 2. 点击Environment
# 3. 添加环境变量
```

### 阿里云ECS
```bash
# 使用阿里云参数存储
aliyun oos PutParameter --Name "/app/jwt-secret" --Value "your-secret" --Type "SecureString"
```

## 🚨 安全检查清单

### 部署前检查
- [ ] 所有默认密码已更改
- [ ] JWT_SECRET使用强密码
- [ ] 数据库连接使用加密
- [ ] API密钥已轮换
- [ ] CORS配置限制具体域名
- [ ] 速率限制已配置
- [ ] HTTPS已启用
- [ ] 安全头已配置

### 运行时监控
- [ ] 异常登录监控
- [ ] API调用频率监控
- [ ] 错误率监控
- [ ] 性能指标监控

### 定期维护
- [ ] 密钥轮换（每90天）
- [ ] 访问权限审查（每月）
- [ ] 安全漏洞扫描（每周）
- [ ] 备份验证（每月）

## 🔄 密钥轮换流程

### 1. 准备阶段
```bash
# 生成新密钥
NEW_JWT_SECRET=$(node -e "console.log(require('crypto').randomBytes(32).toString('hex'))")
echo "新密钥: $NEW_JWT_SECRET"
```

### 2. 更新阶段
```bash
# 更新环境变量
# 1. 在密钥管理服务中创建新版本
# 2. 更新应用配置
# 3. 重启服务
```

### 3. 验证阶段
```bash
# 验证新密钥工作正常
curl -H "Authorization: Bearer $NEW_TOKEN" https://api.your-domain.com/health
```

### 4. 清理阶段
```bash
# 删除旧密钥（保留一个版本作为回滚）
# 更新文档
# 通知团队
```

## 🚨 应急响应

### 密钥泄露处理
1. **立即行动**
   - 禁用泄露的密钥
   - 生成新密钥
   - 更新所有服务

2. **影响评估**
   - 检查访问日志
   - 识别潜在的未授权访问
   - 评估数据泄露风险

3. **恢复操作**
   - 部署新密钥
   - 验证服务正常
   - 监控异常活动

### 联系信息
- **安全团队**: <EMAIL>
- **运维团队**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx

## 📚 相关文档
- [环境变量配置说明](./ENVIRONMENT_VARIABLES.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)
- [安全策略](./SECURITY_POLICY.md)
