# 🚀 Render平台后端部署指南

## 📋 部署前准备

### 1. 确保代码已推送到GitHub
```bash
git add .
git commit -m "Production deployment ready"
git push origin main
```

### 2. 准备环境变量
以下环境变量需要在Render Dashboard中配置：

#### 必需的环境变量
```bash
# 服务器配置
NODE_ENV=production
PORT=4000

# 数据库配置（需要实际的MongoDB Atlas连接字符串）
MONGO_URI=mongodb+srv://prod_user:<EMAIL>/inventory_ai_prod?retryWrites=true&w=majority

# JWT认证（已生成的强密码）
JWT_SECRET=71c255c5340f0bd7a75a6c8c6e1a376d7b904688f49696dbfcb480cfcc177417
JWT_EXPIRES_IN=7d

# AWS S3配置（需要实际的生产环境密钥）
AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_KEY
AWS_REGION=us-east-1
S3_BUCKET=inventory-assets-prod

# DeepSeek AI配置（需要实际的API密钥）
DEEPSEEK_API_KEY=YOUR_DEEPSEEK_API_KEY
DEEPSEEK_BASE_URL=https://llm.chutes.ai/v1
DEEPSEEK_MODEL=deepseek-ai/DeepSeek-V3-0324
AI_MOCK_MODE=false

# 前端配置（部署后更新）
FRONTEND_URL=https://your-vercel-domain.vercel.app
CORS_ORIGIN=https://your-vercel-domain.vercel.app

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200
LOG_LEVEL=warn
ENABLE_HTTPS_REDIRECT=true
TRUST_PROXY=true
```

## 🔧 Render部署步骤

### 步骤1: 创建新的Web Service
1. 登录 [Render Dashboard](https://dashboard.render.com)
2. 点击 "New +" → "Web Service"
3. 连接您的GitHub仓库
4. 选择 `Chat-AI-3.0` 仓库

### 步骤2: 配置服务设置
```yaml
Name: inventory-ai-backend
Environment: Node
Region: Singapore (或选择离用户最近的区域)
Branch: main
Root Directory: backend
```

### 步骤3: 配置构建和启动命令
```bash
# Build Command
npm ci --only=production

# Start Command  
npm start
```

### 步骤4: 配置环境变量
在Render Dashboard的Environment页面中添加上述所有环境变量。

### 步骤5: 配置健康检查
```yaml
Health Check Path: /health
```

### 步骤6: 部署服务
点击 "Create Web Service" 开始部署。

## 📊 部署验证

### 1. 检查部署状态
- 在Render Dashboard中查看部署日志
- 确认服务状态为 "Live"

### 2. 健康检查验证
```bash
curl https://your-render-domain.onrender.com/health
```

期望返回：
```json
{
  "status": "ok",
  "timestamp": "2025-06-15T...",
  "uptime": 123.456,
  "environment": "production"
}
```

### 3. API端点测试
```bash
# 测试基础API
curl https://your-render-domain.onrender.com/api/auth/health

# 测试数据库连接（通过任何需要数据库的端点）
curl -X POST https://your-render-domain.onrender.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'
```

## 🔍 故障排除

### 常见问题

#### 1. 构建失败
- 检查 `package.json` 中的依赖
- 确认Node.js版本兼容性
- 查看构建日志中的错误信息

#### 2. 启动失败
- 检查环境变量配置
- 验证MongoDB连接字符串
- 查看应用日志

#### 3. 健康检查失败
- 确认 `/health` 端点正常工作
- 检查端口配置（必须使用PORT环境变量）
- 验证应用是否正确启动

### 日志查看
```bash
# 在Render Dashboard中查看实时日志
# 或使用Render CLI
render logs -s your-service-id
```

## 📝 部署后配置

### 1. 记录服务URL
部署成功后，记录Render分配的URL：
```
https://inventory-ai-backend-xxx.onrender.com
```

### 2. 更新前端配置
将此URL用于前端的API_BASE_URL配置。

### 3. 配置自定义域名（可选）
在Render Dashboard中可以配置自定义域名。

## 🔒 安全注意事项

1. **环境变量安全**
   - 不要在代码中硬编码敏感信息
   - 使用Render的环境变量功能

2. **数据库安全**
   - 确保MongoDB Atlas配置了IP白名单
   - 使用强密码

3. **API安全**
   - 确认CORS配置正确
   - 验证速率限制生效

## 📞 支持

如果遇到问题：
1. 查看Render官方文档
2. 检查应用日志
3. 验证环境变量配置
4. 测试本地环境是否正常
