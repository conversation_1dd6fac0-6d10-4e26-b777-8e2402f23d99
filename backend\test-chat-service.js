require('dotenv').config();
const mongoose = require('mongoose');
const chatService = require('./src/services/chatService');
const { User, ChatSession } = require('./src/models');

/**
 * 聊天服务功能测试脚本
 * 测试实时聊天功能，不依赖Socket.io
 */

// 模拟Socket对象
class MockSocket {
  constructor(id) {
    this.id = id;
    this.rooms = new Set();
    this.events = {};
    this.emittedEvents = [];
  }

  join(room) {
    this.rooms.add(room);
    console.log(`Socket ${this.id} 加入房间: ${room}`);
  }

  to(room) {
    return {
      emit: (event, data) => {
        this.emittedEvents.push({ type: 'to', room, event, data });
        console.log(`Socket ${this.id} 向房间 ${room} 发送事件: ${event}`);
      }
    };
  }

  emit(event, data) {
    this.emittedEvents.push({ type: 'emit', event, data });
    console.log(`Socket ${this.id} 发送事件: ${event}`);
  }

  on(event, handler) {
    this.events[event] = handler;
  }

  getEmittedEvents() {
    return this.emittedEvents;
  }

  clearEvents() {
    this.emittedEvents = [];
  }
}

async function testChatService() {
  console.log('🚀 开始聊天服务功能测试...\n');

  try {
    // 连接数据库（使用内存数据库进行测试）
    await mongoose.connect('mongodb://localhost:27017/test_chat_db');
    console.log('✅ 测试数据库连接成功');

    // 清理测试数据
    await ChatSession.deleteMany({});
    await User.deleteMany({ email: { $regex: /test.*@example\.com/ } });

    // 创建测试用户
    const testUser = new User({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    });
    await testUser.save();
    console.log('✅ 测试用户创建成功');

    // 测试1: 用户连接处理
    console.log('\n📋 测试1: 用户连接处理');
    const socket1 = new MockSocket('socket_001');
    const sessionId = 'test_session_001';
    const userId = testUser._id.toString();

    const chatSession = await chatService.handleUserConnect(socket1, userId, sessionId);
    console.log('✅ 用户连接处理成功');
    console.log(`- 会话ID: ${chatSession.session_id}`);
    console.log(`- 用户ID: ${chatSession.user_id}`);
    console.log(`- 状态: ${chatSession.status}`);

    // 验证Socket事件
    const connectEvents = socket1.getEmittedEvents();
    console.log(`- 发送事件数量: ${connectEvents.length}`);

    // 测试2: 聊天消息处理
    console.log('\n📋 测试2: 聊天消息处理');
    socket1.clearEvents();

    const messageData = {
      sessionId,
      message: '你好，我想了解配送政策',
      messageType: 'text',
      userId
    };

    await chatService.handleChatMessage(socket1, messageData);
    console.log('✅ 聊天消息处理成功');

    // 验证消息保存
    const updatedSession = await ChatSession.findOne({ session_id: sessionId });
    console.log(`- 消息数量: ${updatedSession.messages.length}`);
    
    if (updatedSession.messages.length >= 2) {
      const userMessage = updatedSession.messages[updatedSession.messages.length - 2];
      const aiMessage = updatedSession.messages[updatedSession.messages.length - 1];
      
      console.log(`- 用户消息: ${userMessage.content.substring(0, 50)}...`);
      console.log(`- AI回复: ${aiMessage.content.substring(0, 50)}...`);
      console.log(`- AI意图: ${aiMessage.metadata?.intent || '未识别'}`);
    }

    // 验证Socket事件
    const messageEvents = socket1.getEmittedEvents();
    console.log(`- 发送事件数量: ${messageEvents.length}`);

    // 测试3: 多轮对话
    console.log('\n📋 测试3: 多轮对话测试');
    socket1.clearEvents();

    const messages = [
      '我想查询库存',
      '有什么新鲜蔬菜吗？',
      '退货政策是什么？'
    ];

    for (let i = 0; i < messages.length; i++) {
      const msgData = {
        sessionId,
        message: messages[i],
        messageType: 'text',
        userId
      };

      await chatService.handleChatMessage(socket1, msgData);
      console.log(`✅ 处理消息 ${i + 1}: ${messages[i]}`);
    }

    // 验证对话历史
    const finalSession = await ChatSession.findOne({ session_id: sessionId });
    console.log(`- 总消息数量: ${finalSession.messages.length}`);

    // 测试4: 会话状态管理
    console.log('\n📋 测试4: 会话状态管理');
    
    // 获取活跃会话统计
    const stats = chatService.getActiveSessionStats();
    console.log('活跃会话统计:', stats);
    console.log('✅ 会话状态管理正常');

    // 测试5: 用户断开连接
    console.log('\n📋 测试5: 用户断开连接处理');
    await chatService.handleUserDisconnect(socket1, userId);
    console.log('✅ 用户断开连接处理成功');

    // 验证会话状态更新
    const disconnectedSession = await ChatSession.findOne({ session_id: sessionId });
    console.log(`- 会话状态: ${disconnectedSession.status}`);

    // 测试6: 多用户会话
    console.log('\n📋 测试6: 多用户会话测试');
    
    // 创建第二个用户
    const testUser2 = new User({
      username: 'testuser2',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    });
    await testUser2.save();

    const socket2 = new MockSocket('socket_002');
    const userId2 = testUser2._id.toString();

    // 第二个用户加入同一会话
    await chatService.handleUserConnect(socket2, userId2, sessionId);
    console.log('✅ 第二个用户加入会话成功');

    // 第二个用户发送消息
    const user2MessageData = {
      sessionId,
      message: '我也想了解一下产品信息',
      messageType: 'text',
      userId: userId2
    };

    await chatService.handleChatMessage(socket2, user2MessageData);
    console.log('✅ 多用户消息处理成功');

    // 验证最终会话状态
    const multiUserSession = await ChatSession.findOne({ session_id: sessionId });
    console.log(`- 最终消息数量: ${multiUserSession.messages.length}`);

    // 获取最终统计
    const finalStats = chatService.getActiveSessionStats();
    console.log('最终统计:', finalStats);

    console.log('\n🎉 聊天服务功能测试完成！');
    console.log('\n📊 测试总结:');
    console.log('- 用户连接处理: ✅');
    console.log('- 聊天消息处理: ✅');
    console.log('- AI响应生成: ✅');
    console.log('- 多轮对话: ✅');
    console.log('- 会话状态管理: ✅');
    console.log('- 用户断开连接: ✅');
    console.log('- 多用户会话: ✅');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 清理测试数据
    try {
      await ChatSession.deleteMany({});
      await User.deleteMany({ email: { $regex: /test.*@example\.com/ } });
      console.log('✅ 测试数据清理完成');
    } catch (cleanupError) {
      console.error('清理测试数据失败:', cleanupError);
    }

    await mongoose.disconnect();
    console.log('✅ 数据库连接已关闭');
  }
}

// 运行测试
if (require.main === module) {
  testChatService().catch(console.error);
}

module.exports = testChatService;
