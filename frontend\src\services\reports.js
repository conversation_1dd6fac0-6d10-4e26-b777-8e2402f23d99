/**
 * 报表管理 API 服务
 */
import { apiRequest } from './api'

export const reportsService = {
  // 获取报表列表
  getReports: async (params = {}) => {
    const { page = 1, limit = 10, type, status, startDate, endDate } = params
    return apiRequest.get('/reports', {
      page,
      limit,
      type,
      status,
      startDate,
      endDate
    })
  },

  // 获取报表详情
  getReport: async (id) => {
    return apiRequest.get(`/reports/${id}`)
  },

  // 生成报表
  generateReport: async (type, params = {}) => {
    return apiRequest.post('/reports/generate', {
      type,
      ...params
    })
  },

  // 获取周报
  getWeeklyReport: async (params = {}) => {
    const { startDate, endDate } = params
    return apiRequest.get('/reports/weekly', {
      startDate,
      endDate
    })
  },

  // 获取月报
  getMonthlyReport: async (params = {}) => {
    const { year, month } = params
    return apiRequest.get('/reports/monthly', {
      year,
      month
    })
  },

  // 获取库存报表
  getInventoryReport: async (params = {}) => {
    const { type = 'current', category, includeZero = false } = params
    return apiRequest.get('/reports/inventory', {
      type,
      category,
      includeZero
    })
  },

  // 获取销售报表
  getSalesReport: async (params = {}) => {
    const { period = 'month', startDate, endDate, groupBy = 'day' } = params
    return apiRequest.get('/reports/sales', {
      period,
      startDate,
      endDate,
      groupBy
    })
  },

  // 获取退货报表
  getReturnReport: async (params = {}) => {
    const { period = 'month', startDate, endDate, groupBy = 'reason' } = params
    return apiRequest.get('/reports/returns', {
      period,
      startDate,
      endDate,
      groupBy
    })
  },

  // 获取AI客服报表
  getChatReport: async (params = {}) => {
    const { period = 'week', startDate, endDate } = params
    return apiRequest.get('/reports/chat', {
      period,
      startDate,
      endDate
    })
  },

  // 删除报表
  deleteReport: async (id) => {
    return apiRequest.delete(`/reports/${id}`)
  },

  // 导出报表
  exportReport: async (id, format = 'excel') => {
    return apiRequest.get(`/reports/${id}/export`, {
      format,
      responseType: 'blob'
    })
  },

  // 获取报表模板
  getReportTemplates: async () => {
    return apiRequest.get('/reports/templates')
  },

  // 创建报表模板
  createReportTemplate: async (template) => {
    return apiRequest.post('/reports/templates', template)
  },

  // 更新报表模板
  updateReportTemplate: async (id, template) => {
    return apiRequest.put(`/reports/templates/${id}`, template)
  },

  // 删除报表模板
  deleteReportTemplate: async (id) => {
    return apiRequest.delete(`/reports/templates/${id}`)
  },

  // 获取仪表盘数据
  getDashboardData: async () => {
    return apiRequest.get('/reports/dashboard')
  },

  // 获取实时统计数据
  getRealTimeStats: async () => {
    return apiRequest.get('/reports/realtime-stats')
  }
}

export default reportsService
