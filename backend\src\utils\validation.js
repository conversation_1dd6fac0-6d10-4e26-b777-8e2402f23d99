const Joi = require('joi');
const { 
  PRODUCT_CATEGORIES, 
  RETURN_REASONS, 
  RETURN_PRIORITY, 
  REFUND_METHODS,
  PAYMENT_METHODS,
  PRODUCT_CONDITIONS,
  USER_ROLES
} = require('./constants');

/**
 * 数据验证工具
 * 使用Joi进行数据验证
 */

// 通用验证规则
const commonValidation = {
  objectId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).message('无效的ID格式'),
  phone: Joi.string().pattern(/^1[3-9]\d{9}$/).message('请输入有效的手机号码'),
  email: Joi.string().email().message('请输入有效的邮箱地址'),
  password: Joi.string().min(6).message('密码至少需要6个字符'),
  barcode: Joi.string().trim().max(50),
  url: Joi.string().uri().message('请输入有效的URL'),
  positiveNumber: Joi.number().min(0).message('数值不能为负数'),
  positiveInteger: Joi.number().integer().min(0).message('必须是非负整数'),
  date: Joi.date().message('请输入有效的日期'),
  pagination: {
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }
};

// 用户验证规则
const userValidation = {
  register: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required().messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过30个字符',
      'any.required': '用户名是必需的'
    }),
    email: commonValidation.email.required(),
    password: commonValidation.password.required(),
    name: Joi.string().trim().max(50).required().messages({
      'string.max': '姓名不能超过50个字符',
      'any.required': '姓名是必需的'
    }),
    role: Joi.string().valid(...Object.values(USER_ROLES)).default('staff'),
    phone: commonValidation.phone
  }),
  
  login: Joi.object({
    username: Joi.string().required().messages({
      'any.required': '用户名是必需的'
    }),
    password: Joi.string().required().messages({
      'any.required': '密码是必需的'
    })
  }),
  
  changePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': '当前密码是必需的'
    }),
    newPassword: commonValidation.password.required()
  })
};

// 产品验证规则
const productValidation = {
  create: Joi.object({
    name: Joi.string().trim().max(100).required().messages({
      'string.max': '产品名称不能超过100个字符',
      'any.required': '产品名称是必需的'
    }),
    category: Joi.string().valid(...Object.values(PRODUCT_CATEGORIES)).required().messages({
      'any.only': '无效的产品类别',
      'any.required': '产品类别是必需的'
    }),
    size: Joi.string().trim().max(50),
    description: Joi.string().trim().max(500),
    barcode: commonValidation.barcode,
    photo_url: commonValidation.url,
    expiry_date: commonValidation.date,
    unit_price: commonValidation.positiveNumber,
    supplier: Joi.string().trim().max(100),
    safety_stock: commonValidation.positiveInteger,
    storage_location: Joi.string().trim().max(50)
  }),
  
  update: Joi.object({
    name: Joi.string().trim().max(100),
    category: Joi.string().valid(...Object.values(PRODUCT_CATEGORIES)),
    size: Joi.string().trim().max(50),
    description: Joi.string().trim().max(500),
    barcode: commonValidation.barcode,
    photo_url: commonValidation.url,
    expiry_date: commonValidation.date,
    unit_price: commonValidation.positiveNumber,
    supplier: Joi.string().trim().max(100),
    safety_stock: commonValidation.positiveInteger,
    storage_location: Joi.string().trim().max(50),
    is_active: Joi.boolean()
  }),
  
  query: Joi.object({
    page: commonValidation.pagination.page,
    limit: commonValidation.pagination.limit,
    keyword: Joi.string().trim().max(100),
    category: Joi.string().valid(...Object.values(PRODUCT_CATEGORIES)),
    is_active: Joi.boolean(),
    sort_by: Joi.string().valid('name', 'category', 'createdAt', 'updatedAt').default('createdAt'),
    sort_order: Joi.string().valid('asc', 'desc').default('desc')
  })
};

// 库存验证规则
const inventoryValidation = {
  stockInRecord: Joi.object({
    supplier: Joi.string().trim().max(100),
    supplier_invoice: Joi.string().trim().max(50),
    items: Joi.array().items(
      Joi.object({
        product_id: commonValidation.objectId.required(),
        quantity: Joi.number().integer().min(1).required().messages({
          'number.min': '入库数量必须大于0',
          'any.required': '入库数量是必需的'
        }),
        unit_cost: commonValidation.positiveNumber.required().messages({
          'any.required': '单位成本是必需的'
        }),
        expiry_date: commonValidation.date,
        batch_number: Joi.string().trim().max(50),
        location: Joi.string().trim().max(50),
        notes: Joi.string().trim().max(200)
      })
    ).min(1).required().messages({
      'array.min': '至少需要一个入库商品',
      'any.required': '入库商品列表是必需的'
    }),
    warehouse_location: Joi.string().trim().max(100),
    delivery_note: Joi.string().trim().max(50),
    notes: Joi.string().trim().max(1000)
  }),
  
  query: Joi.object({
    page: commonValidation.pagination.page,
    limit: commonValidation.pagination.limit,
    keyword: Joi.string().trim().max(100),
    category: Joi.string().valid(...Object.values(PRODUCT_CATEGORIES)),
    location: Joi.string().trim().max(50),
    low_stock: Joi.boolean(),
    sort_by: Joi.string().valid('updatedAt', 'current_stock', 'product_name').default('updatedAt'),
    sort_order: Joi.string().valid('asc', 'desc').default('desc')
  })
};

// 退货验证规则
const returnValidation = {
  create: Joi.object({
    customer_info: Joi.object({
      name: Joi.string().trim().max(50).required().messages({
        'string.max': '客户姓名不能超过50个字符',
        'any.required': '客户姓名是必需的'
      }),
      phone: commonValidation.phone.required(),
      email: commonValidation.email,
      address: Joi.string().trim().max(200)
    }).required(),
    
    purchase_info: Joi.object({
      order_number: Joi.string().trim().max(50),
      purchase_date: commonValidation.date.required().messages({
        'any.required': '购买日期是必需的'
      }),
      pickup_date: commonValidation.date,
      payment_method: Joi.string().valid(...Object.values(PAYMENT_METHODS)).default('cash')
    }).required(),
    
    items: Joi.array().items(
      Joi.object({
        product_id: commonValidation.objectId,
        product_name: Joi.string().trim().required().messages({
          'any.required': '产品名称是必需的'
        }),
        quantity: Joi.number().integer().min(1).required().messages({
          'number.min': '退货数量必须大于0',
          'any.required': '退货数量是必需的'
        }),
        unit_price: commonValidation.positiveNumber.required().messages({
          'any.required': '单价是必需的'
        }),
        reason: Joi.string().valid(...Object.values(RETURN_REASONS)).required().messages({
          'any.only': '无效的退货原因',
          'any.required': '退货原因是必需的'
        }),
        condition: Joi.string().valid(...Object.values(PRODUCT_CONDITIONS)).required().messages({
          'any.only': '无效的商品状态',
          'any.required': '商品状态是必需的'
        }),
        photos: Joi.array().items(
          Joi.object({
            url: commonValidation.url.required(),
            description: Joi.string().trim().max(200)
          })
        ),
        notes: Joi.string().trim().max(500)
      })
    ).min(1).required().messages({
      'array.min': '至少需要一个退货商品',
      'any.required': '退货商品列表是必需的'
    }),
    
    chat_session_id: commonValidation.objectId
  }),
  
  review: Joi.object({
    action: Joi.string().valid('approve', 'reject').required().messages({
      'any.only': '审核动作必须是 approve 或 reject',
      'any.required': '审核动作是必需的'
    }),
    refund_amount: Joi.when('action', {
      is: 'approve',
      then: commonValidation.positiveNumber.min(0.01).required().messages({
        'number.min': '退款金额必须大于0',
        'any.required': '退款金额是必需的'
      }),
      otherwise: Joi.forbidden()
    }),
    refund_method: Joi.when('action', {
      is: 'approve',
      then: Joi.string().valid(...Object.values(REFUND_METHODS)),
      otherwise: Joi.forbidden()
    }),
    notes: Joi.when('action', {
      is: 'reject',
      then: Joi.string().trim().max(1000).required().messages({
        'any.required': '拒绝原因是必需的'
      }),
      otherwise: Joi.string().trim().max(1000)
    })
  }),
  
  query: Joi.object({
    page: commonValidation.pagination.page,
    limit: commonValidation.pagination.limit,
    status: Joi.string(),
    priority: Joi.string().valid(...Object.values(RETURN_PRIORITY)),
    customer_phone: Joi.string(),
    start_date: commonValidation.date,
    end_date: commonValidation.date,
    assigned_to: commonValidation.objectId,
    sort_by: Joi.string().valid('createdAt', 'priority', 'status').default('createdAt'),
    sort_order: Joi.string().valid('asc', 'desc').default('desc')
  })
};

// 聊天验证规则
const chatValidation = {
  createSession: Joi.object({
    customer_info: Joi.object({
      name: Joi.string().trim().max(50),
      phone: commonValidation.phone,
      email: commonValidation.email
    }),
    channel: Joi.string().valid('web_widget', 'mobile_app', 'wechat', 'phone', 'email').default('web_widget'),
    language: Joi.string().valid('zh-CN', 'en-US').default('zh-CN')
  }),
  
  sendMessage: Joi.object({
    message: Joi.string().trim().max(2000).required().messages({
      'string.max': '消息内容不能超过2000个字符',
      'any.required': '消息内容是必需的'
    }),
    message_type: Joi.string().valid('text', 'image', 'file').default('text')
  }),
  
  escalate: Joi.object({
    reason: Joi.string().trim().max(500).required().messages({
      'string.max': '升级原因不能超过500个字符',
      'any.required': '升级原因是必需的'
    })
  })
};

/**
 * 验证数据的通用函数
 * @param {Object} schema - Joi验证模式
 * @param {Object} data - 要验证的数据
 * @param {Object} options - 验证选项
 * @returns {Object} 验证结果
 */
const validateData = (schema, data, options = {}) => {
  const defaultOptions = {
    abortEarly: false,
    allowUnknown: false,
    stripUnknown: true
  };
  
  const validationOptions = { ...defaultOptions, ...options };
  const { error, value } = schema.validate(data, validationOptions);
  
  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    return {
      isValid: false,
      errors,
      data: null
    };
  }
  
  return {
    isValid: true,
    errors: null,
    data: value
  };
};

/**
 * Express中间件：验证请求数据
 * @param {Object} schema - Joi验证模式
 * @param {string} source - 数据源 ('body', 'query', 'params')
 * @returns {Function} Express中间件函数
 */
const validateRequest = (schema, source = 'body') => {
  return (req, res, next) => {
    const data = req[source];
    const result = validateData(schema, data);
    
    if (!result.isValid) {
      return res.status(400).json({
        success: false,
        message: '数据验证失败',
        errors: result.errors
      });
    }
    
    // 将验证后的数据替换原始数据
    req[source] = result.data;
    next();
  };
};

module.exports = {
  commonValidation,
  userValidation,
  productValidation,
  inventoryValidation,
  returnValidation,
  chatValidation,
  validateData,
  validateRequest
};
