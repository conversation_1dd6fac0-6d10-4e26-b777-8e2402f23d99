# 部署指南

> **版本**: v3.0  
> **创建时间**: 2025-06-15  
> **最后更新**: 2025-06-15  
> **适用范围**: 第三阶段AI客服功能完整部署

## 📋 目录

- [概述](#概述)
- [环境要求](#环境要求)
- [生产环境配置](#生产环境配置)
- [数据库部署](#数据库部署)
- [AI服务配置](#ai服务配置)
- [应用部署](#应用部署)
- [监控和日志](#监控和日志)
- [安全配置](#安全配置)
- [故障排除](#故障排除)

## 概述

本指南介绍如何将HubGoodFood库存AI支持系统部署到生产环境，包括第三阶段新增的AI客服功能。

### 部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (Node.js)  │    │  数据库 (MongoDB) │
│   Port: 80/443  │────│   Port: 4000    │────│   Port: 27017   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │  AI服务 (DeepSeek) │
                       │  外部API调用      │
                       └─────────────────┘
```

### 新增功能部署要点
- 🤖 **AI服务集成**: DeepSeek API配置和降级机制
- 📜 **政策管理**: policy.json文件部署和自动导入
- 💬 **WebSocket服务**: 实时聊天功能的负载均衡
- 🔍 **RAG检索**: 缓存和性能优化配置

## 环境要求

### 服务器配置
- **CPU**: 最低2核，推荐4核
- **内存**: 最低4GB，推荐8GB
- **存储**: 最低20GB SSD
- **网络**: 稳定的互联网连接

### 软件要求
- **Node.js**: >= 18.0.0
- **MongoDB**: >= 5.0 (推荐使用MongoDB Atlas)
- **Nginx**: >= 1.18 (反向代理)
- **PM2**: >= 5.0 (进程管理)
- **Git**: 用于代码部署

### 云服务推荐
- **应用托管**: Render, Heroku, DigitalOcean
- **数据库**: MongoDB Atlas
- **文件存储**: AWS S3
- **CDN**: Cloudflare
- **监控**: New Relic, DataDog

## 生产环境配置

### 1. 环境变量配置

创建生产环境配置文件 `backend/.env.production`:

```env
# 应用配置
NODE_ENV=production
PORT=4000
API_BASE_URL=https://your-domain.com/api

# 数据库配置
MONGODB_URI=mongodb+srv://username:<EMAIL>/production_db

# JWT配置
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# AWS S3配置
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-production-bucket

# DeepSeek AI配置
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_BASE_URL=https://llm.chutes.ai/v1
DEEPSEEK_MODEL=deepseek-ai/DeepSeek-V3-0324
AI_MOCK_MODE=false

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/hubgoodfood/app.log

# 安全配置
CORS_ORIGIN=https://your-frontend-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# WebSocket配置
WS_CORS_ORIGIN=https://your-frontend-domain.com
WS_MAX_CONNECTIONS=1000

# 缓存配置
REDIS_URL=redis://localhost:6379
CACHE_TTL=300

# 监控配置
SENTRY_DSN=your_sentry_dsn
NEW_RELIC_LICENSE_KEY=your_newrelic_key
```

### 2. 前端构建配置

创建 `frontend/.env.production`:

```env
VITE_API_BASE_URL=https://your-domain.com/api
VITE_WS_URL=https://your-domain.com
VITE_APP_NAME=HubGoodFood库存AI支持系统
VITE_APP_VERSION=3.0.0
```

## 数据库部署

### 1. MongoDB Atlas配置

```javascript
// 生产数据库连接配置
const mongoConfig = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferMaxEntries: 0,
  bufferCommands: false,
  retryWrites: true,
  w: 'majority'
};
```

### 2. 数据库索引优化

```bash
# 连接到生产数据库
mongo "mongodb+srv://cluster.mongodb.net/production_db" --username your_username

# 创建必要索引
db.users.createIndex({ "username": 1 }, { unique: true })
db.users.createIndex({ "email": 1 }, { unique: true })
db.products.createIndex({ "barcode": 1 }, { unique: true })
db.products.createIndex({ "name": "text", "description": "text" })
db.policies.createIndex({ "policy_id": 1 }, { unique: true })
db.policies.createIndex({ "category": 1, "is_active": 1 })
db.policies.createIndex({ "name": "text", "current_content": "text" })
db.chatsessions.createIndex({ "session_id": 1 }, { unique: true })
db.chatsessions.createIndex({ "user_id": 1, "created_at": -1 })
```

### 3. 数据备份策略

```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/mongodb"
DB_NAME="production_db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mongodump --uri="$MONGODB_URI" --db=$DB_NAME --out=$BACKUP_DIR/$DATE

# 压缩备份文件
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz -C $BACKUP_DIR $DATE

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete

echo "备份完成: backup_$DATE.tar.gz"
```

## AI服务配置

### 1. DeepSeek API配置

```javascript
// backend/src/config/ai.js
const aiConfig = {
  deepseek: {
    apiKey: process.env.DEEPSEEK_API_KEY,
    baseURL: process.env.DEEPSEEK_BASE_URL,
    model: process.env.DEEPSEEK_MODEL,
    timeout: 30000,
    retries: 3,
    retryDelay: 1000
  },
  fallback: {
    enabled: true,
    mockMode: process.env.AI_MOCK_MODE === 'true'
  },
  cache: {
    enabled: true,
    ttl: 300, // 5分钟
    maxSize: 1000
  }
};

module.exports = aiConfig;
```

### 2. 政策文件部署

```bash
# 确保policy.json在正确位置
cp policy.json /var/www/hubgoodfood/policy.json

# 设置正确权限
chmod 644 /var/www/hubgoodfood/policy.json
chown www-data:www-data /var/www/hubgoodfood/policy.json

# 验证文件格式
node -e "console.log(JSON.parse(require('fs').readFileSync('/var/www/hubgoodfood/policy.json', 'utf8')))"
```

### 3. RAG检索优化

```javascript
// backend/src/config/rag.js
const ragConfig = {
  cache: {
    enabled: true,
    ttl: 600, // 10分钟
    maxSize: 500
  },
  search: {
    maxResults: 20,
    minRelevanceScore: 0.3,
    timeout: 5000
  },
  policies: {
    preloadOnStartup: true,
    refreshInterval: 3600000 // 1小时
  }
};

module.exports = ragConfig;
```

## 应用部署

### 1. 使用PM2部署

```bash
# 安装PM2
npm install -g pm2

# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'hubgoodfood-api',
    script: './src/server.js',
    cwd: '/var/www/hubgoodfood/backend',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 4000
    },
    env_file: '.env.production',
    log_file: '/var/log/hubgoodfood/combined.log',
    out_file: '/var/log/hubgoodfood/out.log',
    error_file: '/var/log/hubgoodfood/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

# 启动应用
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 2. Nginx反向代理配置

```nginx
# /etc/nginx/sites-available/hubgoodfood
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 前端静态文件
    location / {
        root /var/www/hubgoodfood/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:4000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://localhost:4000/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

### 3. Docker部署（可选）

```dockerfile
# Dockerfile.production
FROM node:18-alpine AS builder

# 构建前端
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production
COPY frontend/ ./
RUN npm run build

# 构建后端
FROM node:18-alpine AS production

WORKDIR /app
COPY backend/package*.json ./
RUN npm ci --only=production

COPY backend/ ./
COPY --from=builder /app/frontend/dist ./public
COPY policy.json ./

EXPOSE 4000

USER node

CMD ["npm", "start"]
```

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.production
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
    env_file:
      - backend/.env.production
    restart: unless-stopped
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
      - ./policy.json:/app/policy.json:ro

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl:ro
    depends_on:
      - app
    restart: unless-stopped

volumes:
  redis_data:
```

## 监控和日志

### 1. 应用监控

```javascript
// backend/src/middleware/monitoring.js
const monitoring = {
  // 性能监控
  trackPerformance: (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      console.log(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
      
      // 发送到监控服务
      if (process.env.NEW_RELIC_LICENSE_KEY) {
        // New Relic集成
      }
    });
    
    next();
  },

  // 错误监控
  trackErrors: (error, req, res, next) => {
    console.error('应用错误:', error);
    
    // 发送到错误追踪服务
    if (process.env.SENTRY_DSN) {
      // Sentry集成
    }
    
    next(error);
  }
};

module.exports = monitoring;
```

### 2. 健康检查

```javascript
// backend/src/routes/health.js
const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();

router.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    checks: {}
  };

  // 数据库连接检查
  try {
    await mongoose.connection.db.admin().ping();
    health.checks.database = 'ok';
  } catch (error) {
    health.checks.database = 'error';
    health.status = 'error';
  }

  // AI服务检查
  try {
    const aiService = require('../services/aiService');
    const healthCheck = await aiService.healthCheck();
    health.checks.ai_service = healthCheck.success ? 'ok' : 'error';
  } catch (error) {
    health.checks.ai_service = 'error';
  }

  res.status(health.status === 'ok' ? 200 : 503).json(health);
});

module.exports = router;
```

### 3. 日志配置

```javascript
// backend/src/utils/logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'hubgoodfood-api' },
  transports: [
    new winston.transports.File({ 
      filename: '/var/log/hubgoodfood/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: '/var/log/hubgoodfood/combined.log' 
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

module.exports = logger;
```

## 安全配置

### 1. 环境安全

```bash
# 设置文件权限
chmod 600 backend/.env.production
chown www-data:www-data backend/.env.production

# 防火墙配置
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 4000/tcp  # 只允许内部访问
ufw enable

# 自动安全更新
apt-get install unattended-upgrades
dpkg-reconfigure -plow unattended-upgrades
```

### 2. 应用安全

```javascript
// backend/src/middleware/security.js
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

const security = {
  // 安全头
  helmet: helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"]
      }
    }
  }),

  // 速率限制
  rateLimit: rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000,
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    message: '请求过于频繁，请稍后再试'
  }),

  // API密钥验证
  validateApiKey: (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    if (req.path.startsWith('/api/admin') && !apiKey) {
      return res.status(401).json({ error: '需要API密钥' });
    }
    next();
  }
};

module.exports = security;
```

## 故障排除

### 常见问题

1. **AI服务连接失败**
   ```bash
   # 检查API密钥
   curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" https://llm.chutes.ai/v1/models
   
   # 检查网络连接
   ping llm.chutes.ai
   ```

2. **WebSocket连接问题**
   ```bash
   # 检查端口是否开放
   netstat -tlnp | grep :4000
   
   # 检查Nginx配置
   nginx -t
   systemctl reload nginx
   ```

3. **数据库连接超时**
   ```bash
   # 检查MongoDB连接
   mongo "$MONGODB_URI" --eval "db.runCommand('ping')"
   
   # 检查网络延迟
   ping cluster.mongodb.net
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   pm2 monit
   
   # 重启应用
   pm2 restart all
   ```

### 监控脚本

```bash
#!/bin/bash
# monitor.sh - 应用监控脚本

# 检查应用状态
if ! pm2 list | grep -q "online"; then
    echo "应用离线，正在重启..."
    pm2 restart all
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "磁盘空间不足: ${DISK_USAGE}%"
fi

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
if [ $MEMORY_USAGE -gt 80 ]; then
    echo "内存使用过高: ${MEMORY_USAGE}%"
fi

# 检查API健康状态
if ! curl -f http://localhost:4000/health > /dev/null 2>&1; then
    echo "API健康检查失败"
fi
```

---

**📝 文档维护**
- 创建者: AI Assistant
- 最后更新: 2025-06-15
- 版本: v3.0
- 状态: 第三阶段部署指南
