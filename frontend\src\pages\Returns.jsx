import React, { useState, useEffect } from 'react'
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Row,
  Col,
  Statistic,
  Tag,
  DatePicker,
  Descriptions,
  Divider,
  Alert,
  Popconfirm,
  Badge
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  RobotOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons'
import { returnsService } from '../services/returns'
import dayjs from 'dayjs'

const { Title } = Typography
const { Option } = Select
const { Search } = Input
const { RangePicker } = DatePicker
const { TextArea } = Input

const Returns = () => {
  // 状态管理
  const [loading, setLoading] = useState(false)
  const [returns, setReturns] = useState([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [stats, setStats] = useState({})

  // 筛选条件
  const [searchText, setSearchText] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedReason, setSelectedReason] = useState('')
  const [dateRange, setDateRange] = useState([])

  // 模态框状态
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isReviewModalVisible, setIsReviewModalVisible] = useState(false)
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [selectedReturn, setSelectedReturn] = useState(null)
  const [aiAnalysis, setAiAnalysis] = useState(null)

  // 表单
  const [reviewForm] = Form.useForm()
  const [createForm] = Form.useForm()

  // 初始化数据
  useEffect(() => {
    loadReturns()
    loadStats()
  }, [currentPage, pageSize, searchText, selectedStatus, selectedReason, dateRange])

  // 加载退货列表
  const loadReturns = async () => {
    setLoading(true)
    try {
      const response = await returnsService.getReturnRequests({
        page: currentPage,
        limit: pageSize,
        search: searchText,
        status: selectedStatus,
        reason: selectedReason,
        startDate: dateRange[0]?.format('YYYY-MM-DD'),
        endDate: dateRange[1]?.format('YYYY-MM-DD')
      })

      if (response.success) {
        setReturns(response.data.returns || [])
        setTotal(response.data.total || 0)
      }
    } catch (error) {
      message.error('加载退货列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await returnsService.getReturnStatistics()
      if (response.success) {
        setStats(response.data || {})
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value)
    setCurrentPage(1)
  }

  // 处理状态筛选
  const handleStatusChange = (value) => {
    setSelectedStatus(value)
    setCurrentPage(1)
  }

  // 处理原因筛选
  const handleReasonChange = (value) => {
    setSelectedReason(value)
    setCurrentPage(1)
  }

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    setDateRange(dates || [])
    setCurrentPage(1)
  }

  // 查看详情
  const handleViewDetail = async (record) => {
    setSelectedReturn(record)
    setIsDetailModalVisible(true)

    // 加载AI分析结果
    try {
      const response = await returnsService.getAIAnalysis(record.id)
      if (response.success) {
        setAiAnalysis(response.data)
      }
    } catch (error) {
      console.error('加载AI分析失败:', error)
    }
  }

  // 打开审核模态框
  const handleOpenReviewModal = (record) => {
    setSelectedReturn(record)
    setIsReviewModalVisible(true)
    reviewForm.resetFields()
  }

  // 关闭模态框
  const handleCloseModals = () => {
    setIsDetailModalVisible(false)
    setIsReviewModalVisible(false)
    setIsCreateModalVisible(false)
    setSelectedReturn(null)
    setAiAnalysis(null)
    reviewForm.resetFields()
    createForm.resetFields()
  }

  // 审核退货申请
  const handleReviewReturn = async (values) => {
    try {
      await returnsService.reviewReturnRequest(selectedReturn.id, values)
      message.success('审核完成')
      handleCloseModals()
      loadReturns()
      loadStats()
    } catch (error) {
      message.error('审核失败')
    }
  }

  // 应用AI建议
  const handleApplyAISuggestions = async () => {
    try {
      await returnsService.applyAISuggestions(selectedReturn.id)
      message.success('AI建议已应用')
      handleCloseModals()
      loadReturns()
    } catch (error) {
      message.error('应用AI建议失败')
    }
  }

  // 创建退货申请
  const handleCreateReturn = async (values) => {
    try {
      await returnsService.createReturnRequest(values)
      message.success('退货申请创建成功')
      handleCloseModals()
      loadReturns()
      loadStats()
    } catch (error) {
      message.error('创建退货申请失败')
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '申请单号',
      dataIndex: 'request_number',
      key: 'request_number',
      width: 150,
      render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span>
    },
    {
      title: '客户信息',
      dataIndex: 'customer_info',
      key: 'customer_info',
      width: 150,
      render: (customer) => (
        <div>
          <div>{customer?.name || '未知客户'}</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            {customer?.phone || customer?.email}
          </div>
        </div>
      )
    },
    {
      title: '商品信息',
      dataIndex: 'items',
      key: 'items',
      ellipsis: true,
      render: (items) => (
        <div>
          {items?.slice(0, 2).map((item, index) => (
            <div key={index} style={{ fontSize: 12 }}>
              {item.product_name} x{item.quantity}
            </div>
          ))}
          {items?.length > 2 && (
            <div style={{ fontSize: 12, color: '#999' }}>
              等{items.length}件商品
            </div>
          )}
        </div>
      )
    },
    {
      title: '退货原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 120,
      render: (reason) => {
        const reasonMap = {
          quality_issue: { color: 'red', text: '质量问题' },
          wrong_item: { color: 'orange', text: '商品错误' },
          not_satisfied: { color: 'blue', text: '不满意' },
          damaged: { color: 'red', text: '商品损坏' },
          other: { color: 'default', text: '其他' }
        }
        const config = reasonMap[reason] || { color: 'default', text: reason }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '退货金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 100,
      render: (amount) => `¥${amount?.toFixed(2) || '0.00'}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusMap = {
          pending: { color: 'orange', text: '待审核' },
          approved: { color: 'green', text: '已通过' },
          rejected: { color: 'red', text: '已拒绝' },
          processing: { color: 'blue', text: '处理中' },
          completed: { color: 'green', text: '已完成' }
        }
        const config = statusMap[status] || { color: 'default', text: status }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '申请时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              icon={<CheckOutlined />}
              onClick={() => handleOpenReviewModal(record)}
            >
              审核
            </Button>
          )}
          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              icon={<RobotOutlined />}
              onClick={() => handleApplyAISuggestions(record)}
            >
              AI处理
            </Button>
          )}
        </Space>
      )
    }
  ]

  return (
    <div className="fade-in">
      <div className="page-header">
        <Title level={2}>退货管理</Title>
        <p>处理客户退货申请、审核退货请求和退款管理</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总申请数"
              value={stats.total || 0}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="待审核"
              value={stats.pending || 0}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已通过"
              value={stats.approved || 0}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="退货金额"
              value={stats.total_amount || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容卡片 */}
      <Card>
        {/* 工具栏 */}
        <div style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={6}>
              <Search
                placeholder="搜索申请单号、客户"
                allowClear
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={12} sm={6} md={4}>
              <Select
                placeholder="选择状态"
                allowClear
                value={selectedStatus}
                onChange={handleStatusChange}
                style={{ width: '100%' }}
              >
                <Option value="pending">待审核</Option>
                <Option value="approved">已通过</Option>
                <Option value="rejected">已拒绝</Option>
                <Option value="processing">处理中</Option>
                <Option value="completed">已完成</Option>
              </Select>
            </Col>
            <Col xs={12} sm={6} md={4}>
              <Select
                placeholder="退货原因"
                allowClear
                value={selectedReason}
                onChange={handleReasonChange}
                style={{ width: '100%' }}
              >
                <Option value="quality_issue">质量问题</Option>
                <Option value="wrong_item">商品错误</Option>
                <Option value="not_satisfied">不满意</Option>
                <Option value="damaged">商品损坏</Option>
                <Option value="other">其他</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
              />
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Space wrap>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setIsCreateModalVisible(true)}
                >
                  新增申请
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadReturns}
                >
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Divider />

        {/* 退货申请表格 */}
        <Table
          columns={columns}
          dataSource={returns}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            }
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 退货详情模态框 */}
      <Modal
        title="退货申请详情"
        open={isDetailModalVisible}
        onCancel={handleCloseModals}
        footer={[
          <Button key="close" onClick={handleCloseModals}>
            关闭
          </Button>,
          selectedReturn?.status === 'pending' && (
            <Button
              key="review"
              type="primary"
              onClick={() => {
                setIsDetailModalVisible(false)
                setIsReviewModalVisible(true)
              }}
            >
              审核
            </Button>
          )
        ]}
        width={800}
      >
        {selectedReturn && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="申请单号" span={2}>
                {selectedReturn.request_number}
              </Descriptions.Item>
              <Descriptions.Item label="客户姓名">
                {selectedReturn.customer_info?.name}
              </Descriptions.Item>
              <Descriptions.Item label="联系方式">
                {selectedReturn.customer_info?.phone || selectedReturn.customer_info?.email}
              </Descriptions.Item>
              <Descriptions.Item label="退货原因">
                {selectedReturn.reason}
              </Descriptions.Item>
              <Descriptions.Item label="申请状态">
                <Tag color={
                  selectedReturn.status === 'pending' ? 'orange' :
                  selectedReturn.status === 'approved' ? 'green' :
                  selectedReturn.status === 'rejected' ? 'red' : 'blue'
                }>
                  {selectedReturn.status === 'pending' ? '待审核' :
                   selectedReturn.status === 'approved' ? '已通过' :
                   selectedReturn.status === 'rejected' ? '已拒绝' : '处理中'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="退货金额" span={2}>
                ¥{selectedReturn.total_amount?.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="申请时间" span={2}>
                {dayjs(selectedReturn.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="退货说明" span={2}>
                {selectedReturn.description || '无'}
              </Descriptions.Item>
            </Descriptions>

            {/* 退货商品列表 */}
            <Divider>退货商品</Divider>
            <Table
              dataSource={selectedReturn.items || []}
              pagination={false}
              size="small"
              columns={[
                { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                { title: '数量', dataIndex: 'quantity', key: 'quantity', width: 80 },
                { title: '单价', dataIndex: 'unit_price', key: 'unit_price', width: 100, render: (price) => `¥${price?.toFixed(2)}` },
                { title: '小计', dataIndex: 'subtotal', key: 'subtotal', width: 100, render: (total) => `¥${total?.toFixed(2)}` }
              ]}
            />

            {/* AI分析结果 */}
            {aiAnalysis && (
              <div>
                <Divider>AI分析结果</Divider>
                <Alert
                  message="AI智能分析"
                  description={
                    <div>
                      <p><strong>分析结果:</strong> {aiAnalysis.analysis}</p>
                      <p><strong>处理建议:</strong> {aiAnalysis.suggestion}</p>
                      <p><strong>风险评估:</strong> {aiAnalysis.risk_level}</p>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 审核模态框 */}
      <Modal
        title="审核退货申请"
        open={isReviewModalVisible}
        onCancel={handleCloseModals}
        footer={null}
        width={600}
      >
        <Form
          form={reviewForm}
          layout="vertical"
          onFinish={handleReviewReturn}
        >
          <Form.Item
            name="status"
            label="审核结果"
            rules={[{ required: true, message: '请选择审核结果' }]}
          >
            <Select placeholder="请选择审核结果">
              <Option value="approved">通过</Option>
              <Option value="rejected">拒绝</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="review_notes"
            label="审核备注"
            rules={[{ required: true, message: '请输入审核备注' }]}
          >
            <TextArea rows={4} placeholder="请输入审核备注" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交审核
              </Button>
              <Button onClick={handleCloseModals}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 新增退货申请模态框 */}
      <Modal
        title="新增退货申请"
        open={isCreateModalVisible}
        onCancel={handleCloseModals}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateReturn}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['customer_info', 'name']}
                label="客户姓名"
                rules={[{ required: true, message: '请输入客户姓名' }]}
              >
                <Input placeholder="请输入客户姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['customer_info', 'phone']}
                label="联系电话"
                rules={[{ required: true, message: '请输入联系电话' }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="reason"
            label="退货原因"
            rules={[{ required: true, message: '请选择退货原因' }]}
          >
            <Select placeholder="请选择退货原因">
              <Option value="quality_issue">质量问题</Option>
              <Option value="wrong_item">商品错误</Option>
              <Option value="not_satisfied">不满意</Option>
              <Option value="damaged">商品损坏</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="退货说明"
            rules={[{ required: true, message: '请输入退货说明' }]}
          >
            <TextArea rows={4} placeholder="请详细描述退货原因" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交申请
              </Button>
              <Button onClick={handleCloseModals}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Returns
