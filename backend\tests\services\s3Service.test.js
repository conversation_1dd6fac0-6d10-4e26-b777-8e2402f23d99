// 设置测试环境变量
process.env.S3_BUCKET = 'test-bucket';
process.env.AWS_REGION = 'us-east-1';
process.env.AWS_ACCESS_KEY_ID = 'test-key';
process.env.AWS_SECRET_ACCESS_KEY = 'test-secret';

// Mock AWS SDK
jest.mock('aws-sdk', () => {
  const mockS3 = {
    upload: jest.fn(),
    getSignedUrlPromise: jest.fn(),
    deleteObject: jest.fn(),
    headObject: jest.fn(),
    deleteObjects: jest.fn(),
    headBucket: jest.fn()
  };

  return {
    S3: jest.fn(() => mockS3),
    __mockS3: mockS3
  };
});

const AWS = require('aws-sdk');
const mockS3 = AWS.__mockS3;

const s3Service = require('../../src/services/s3Service');

describe('S3Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // 设置环境变量
    process.env.S3_BUCKET = 'test-bucket';
    process.env.AWS_REGION = 'us-east-1';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('uploadFile', () => {
    it('应该成功上传文件', async () => {
      const mockResult = {
        Location: 'https://test-bucket.s3.amazonaws.com/uploads/test.jpg',
        Key: 'uploads/test.jpg',
        ETag: '"abc123"'
      };

      mockS3.upload.mockReturnValue({
        promise: jest.fn().mockResolvedValue(mockResult)
      });

      const fileBuffer = Buffer.from('test file content');
      const fileName = 'test.jpg';
      const contentType = 'image/jpeg';

      const result = await s3Service.uploadFile(fileBuffer, fileName, contentType);

      expect(result.success).toBe(true);
      expect(result.data.key).toBe(mockResult.Key);
      expect(result.data.location).toBe(mockResult.Location);
      expect(result.data.originalName).toBe(fileName);
      expect(result.data.contentType).toBe(contentType);
      expect(result.data.size).toBe(fileBuffer.length);

      expect(mockS3.upload).toHaveBeenCalledWith(
        expect.objectContaining({
          Bucket: 'test-bucket',
          Body: fileBuffer,
          ContentType: contentType,
          ACL: 'private'
        })
      );
    });

    it('应该处理上传失败的情况', async () => {
      const error = new Error('Upload failed');
      mockS3.upload.mockReturnValue({
        promise: jest.fn().mockRejectedValue(error)
      });

      const fileBuffer = Buffer.from('test file content');
      const fileName = 'test.jpg';
      const contentType = 'image/jpeg';

      await expect(
        s3Service.uploadFile(fileBuffer, fileName, contentType)
      ).rejects.toThrow('文件上传失败: Upload failed');
    });

    it('应该使用自定义文件夹', async () => {
      const mockResult = {
        Location: 'https://test-bucket.s3.amazonaws.com/custom/test.jpg',
        Key: 'custom/test.jpg',
        ETag: '"abc123"'
      };

      mockS3.upload.mockReturnValue({
        promise: jest.fn().mockResolvedValue(mockResult)
      });

      const fileBuffer = Buffer.from('test file content');
      const fileName = 'test.jpg';
      const contentType = 'image/jpeg';
      const folder = 'custom';

      await s3Service.uploadFile(fileBuffer, fileName, contentType, folder);

      expect(mockS3.upload).toHaveBeenCalledWith(
        expect.objectContaining({
          Key: expect.stringMatching(/^custom\/.*\.jpg$/)
        })
      );
    });
  });

  describe('getSignedUrl', () => {
    it('应该成功生成预签名URL', async () => {
      const mockUrl = 'https://test-bucket.s3.amazonaws.com/test.jpg?signature=abc123';
      mockS3.getSignedUrlPromise.mockResolvedValue(mockUrl);

      const key = 'uploads/test.jpg';
      const result = await s3Service.getSignedUrl(key);

      expect(result).toBe(mockUrl);
      expect(mockS3.getSignedUrlPromise).toHaveBeenCalledWith('getObject', {
        Bucket: 'test-bucket',
        Key: key,
        Expires: 3600
      });
    });

    it('应该使用自定义过期时间', async () => {
      const mockUrl = 'https://test-bucket.s3.amazonaws.com/test.jpg?signature=abc123';
      mockS3.getSignedUrlPromise.mockResolvedValue(mockUrl);

      const key = 'uploads/test.jpg';
      const expiresIn = 7200;
      
      await s3Service.getSignedUrl(key, expiresIn);

      expect(mockS3.getSignedUrlPromise).toHaveBeenCalledWith('getObject', {
        Bucket: 'test-bucket',
        Key: key,
        Expires: expiresIn
      });
    });

    it('应该处理生成URL失败的情况', async () => {
      const error = new Error('Access denied');
      mockS3.getSignedUrlPromise.mockRejectedValue(error);

      const key = 'uploads/test.jpg';

      await expect(
        s3Service.getSignedUrl(key)
      ).rejects.toThrow('生成预签名URL失败: Access denied');
    });
  });

  describe('deleteFile', () => {
    it('应该成功删除文件', async () => {
      mockS3.deleteObject.mockReturnValue({
        promise: jest.fn().mockResolvedValue({})
      });

      const key = 'uploads/test.jpg';
      const result = await s3Service.deleteFile(key);

      expect(result.success).toBe(true);
      expect(result.message).toBe('文件删除成功');
      expect(mockS3.deleteObject).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: key
      });
    });

    it('应该处理删除失败的情况', async () => {
      const error = new Error('File not found');
      mockS3.deleteObject.mockReturnValue({
        promise: jest.fn().mockRejectedValue(error)
      });

      const key = 'uploads/test.jpg';

      await expect(
        s3Service.deleteFile(key)
      ).rejects.toThrow('文件删除失败: File not found');
    });
  });

  describe('fileExists', () => {
    it('应该返回true当文件存在时', async () => {
      mockS3.headObject.mockReturnValue({
        promise: jest.fn().mockResolvedValue({})
      });

      const key = 'uploads/test.jpg';
      const result = await s3Service.fileExists(key);

      expect(result).toBe(true);
      expect(mockS3.headObject).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: key
      });
    });

    it('应该返回false当文件不存在时', async () => {
      const error = new Error('Not found');
      error.code = 'NotFound';
      mockS3.headObject.mockReturnValue({
        promise: jest.fn().mockRejectedValue(error)
      });

      const key = 'uploads/test.jpg';
      const result = await s3Service.fileExists(key);

      expect(result).toBe(false);
    });

    it('应该抛出错误当遇到其他错误时', async () => {
      const error = new Error('Access denied');
      error.code = 'AccessDenied';
      mockS3.headObject.mockReturnValue({
        promise: jest.fn().mockRejectedValue(error)
      });

      const key = 'uploads/test.jpg';

      await expect(
        s3Service.fileExists(key)
      ).rejects.toThrow('检查文件存在性失败: Access denied');
    });
  });

  describe('testConnection', () => {
    it('应该返回true当连接成功时', async () => {
      mockS3.headBucket.mockReturnValue({
        promise: jest.fn().mockResolvedValue({})
      });

      const result = await s3Service.testConnection();

      expect(result).toBe(true);
      expect(mockS3.headBucket).toHaveBeenCalledWith({
        Bucket: 'test-bucket'
      });
    });

    it('应该返回false当连接失败时', async () => {
      const error = new Error('Bucket not found');
      mockS3.headBucket.mockReturnValue({
        promise: jest.fn().mockRejectedValue(error)
      });

      const result = await s3Service.testConnection();

      expect(result).toBe(false);
    });
  });
});
