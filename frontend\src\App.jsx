import React, { useState, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout, message } from 'antd'

// 页面组件
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import Products from './pages/Products'
import Inventory from './pages/Inventory'
import Returns from './pages/Returns'
import Chat from './pages/Chat'

// 布局组件
import AppLayout from './components/Layout/AppLayout'
import ChatWidget from './components/Chat/ChatWidget'

// 服务
import { authService } from './services/auth'

// 样式
import './styles/App.css'

const { Content } = Layout

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState(null)

  // 检查用户认证状态
  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('token')
      if (token) {
        // 验证token有效性
        const userData = await authService.getCurrentUser()
        setUser(userData)
        setIsAuthenticated(true)
      }
    } catch (error) {
      console.error('认证检查失败:', error)
      localStorage.removeItem('token')
    } finally {
      setLoading(false)
    }
  }

  const handleLogin = async (credentials) => {
    try {
      const response = await authService.login(credentials)
      localStorage.setItem('token', response.token)
      setUser(response.user)
      setIsAuthenticated(true)
      message.success('登录成功')
      return true
    } catch (error) {
      message.error(error.message || '登录失败')
      return false
    }
  }

  const handleLogout = async () => {
    try {
      await authService.logout()
      localStorage.removeItem('token')
      setUser(null)
      setIsAuthenticated(false)
      message.success('已安全退出')
    } catch (error) {
      console.error('退出失败:', error)
      // 即使API调用失败，也要清除本地状态
      localStorage.removeItem('token')
      setUser(null)
      setIsAuthenticated(false)
    }
  }

  // 加载中状态
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner" />
        <p>加载中...</p>
      </div>
    )
  }

  // 未认证用户显示登录页面
  if (!isAuthenticated) {
    return (
      <div className="app">
        <Routes>
          <Route 
            path="/login" 
            element={<Login onLogin={handleLogin} />} 
          />
          <Route 
            path="*" 
            element={<Navigate to="/login" replace />} 
          />
        </Routes>
        
        {/* 公共聊天窗口 */}
        <ChatWidget />
      </div>
    )
  }

  // 已认证用户显示主应用
  return (
    <div className="app">
      <AppLayout user={user} onLogout={handleLogout}>
        <Content className="app-content">
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/products" element={<Products />} />
            <Route path="/inventory" element={<Inventory />} />
            <Route path="/returns" element={<Returns />} />
            <Route path="/chat" element={<Chat />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Content>
      </AppLayout>
      
      {/* 客服聊天窗口 */}
      <ChatWidget />
    </div>
  )
}

export default App
