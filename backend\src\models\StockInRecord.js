const mongoose = require('mongoose');

const stockInItemSchema = new mongoose.Schema({
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, '产品ID是必需的']
  },
  quantity: {
    type: Number,
    required: [true, '入库数量是必需的'],
    min: [1, '入库数量必须大于0']
  },
  unit_cost: {
    type: Number,
    required: [true, '单位成本是必需的'],
    min: [0, '单位成本不能为负数']
  },
  total_cost: {
    type: Number,
    required: [true, '总成本是必需的'],
    min: [0, '总成本不能为负数']
  },
  expiry_date: {
    type: Date
  },
  batch_number: {
    type: String,
    trim: true,
    maxlength: [50, '批次号不能超过50个字符']
  },
  location: {
    type: String,
    trim: true,
    maxlength: [50, '存储位置不能超过50个字符']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [200, '备注不能超过200个字符']
  }
}, { _id: false });

const stockInRecordSchema = new mongoose.Schema({
  record_number: {
    type: String,
    unique: true,
    index: true
  },
  supplier: {
    type: String,
    trim: true,
    maxlength: [100, '供应商名称不能超过100个字符']
  },
  supplier_invoice: {
    type: String,
    trim: true,
    maxlength: [50, '供应商发票号不能超过50个字符']
  },
  items: {
    type: [stockInItemSchema],
    required: [true, '入库商品列表是必需的'],
    validate: {
      validator: function(items) {
        return items && items.length > 0;
      },
      message: '至少需要一个入库商品'
    }
  },
  total_quantity: {
    type: Number,
    min: [0, '总数量不能为负数'],
    default: 0
  },
  total_amount: {
    type: Number,
    min: [0, '总金额不能为负数'],
    default: 0
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'cancelled'],
    default: 'pending',
    index: true
  },
  stock_in_date: {
    type: Date,
    default: Date.now,
    index: true
  },
  processed_date: {
    type: Date,
    index: true
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '创建人是必需的']
  },
  processed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  warehouse_location: {
    type: String,
    trim: true,
    maxlength: [100, '仓库位置不能超过100个字符']
  },
  delivery_note: {
    type: String,
    trim: true,
    maxlength: [50, '送货单号不能超过50个字符']
  },
  quality_check: {
    passed: {
      type: Boolean,
      default: true
    },
    notes: {
      type: String,
      trim: true,
      maxlength: [500, '质检备注不能超过500个字符']
    },
    checked_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    check_date: {
      type: Date
    }
  },
  attachments: [{
    filename: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    file_type: {
      type: String,
      enum: ['image', 'document', 'other'],
      default: 'other'
    },
    uploaded_at: {
      type: Date,
      default: Date.now
    }
  }],
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, '备注不能超过1000个字符']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 生成入库单号
stockInRecordSchema.statics.generateRecordNumber = function() {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const timestamp = now.getTime().toString().slice(-6);
  
  return `IN${year}${month}${day}${timestamp}`;
};

// 计算总数量和总金额
stockInRecordSchema.methods.calculateTotals = function() {
  this.total_quantity = this.items.reduce((sum, item) => sum + item.quantity, 0);
  this.total_amount = this.items.reduce((sum, item) => sum + item.total_cost, 0);
  return { total_quantity: this.total_quantity, total_amount: this.total_amount };
};

// 验证商品项目
stockInRecordSchema.methods.validateItems = function() {
  for (let item of this.items) {
    if (item.total_cost !== item.quantity * item.unit_cost) {
      throw new Error(`商品 ${item.product_id} 的总成本计算错误`);
    }
  }
  return true;
};

// 处理入库
stockInRecordSchema.methods.process = async function(userId) {
  if (this.status !== 'pending') {
    throw new Error('只能处理待处理状态的入库单');
  }
  
  this.status = 'processing';
  this.processed_by = userId;
  this.processed_date = new Date();
  
  return this;
};

// 完成入库
stockInRecordSchema.methods.complete = async function() {
  if (this.status !== 'processing') {
    throw new Error('只能完成处理中状态的入库单');
  }
  
  this.status = 'completed';
  return this;
};

// 取消入库
stockInRecordSchema.methods.cancel = async function(reason) {
  if (this.status === 'completed') {
    throw new Error('已完成的入库单不能取消');
  }
  
  this.status = 'cancelled';
  if (reason) {
    this.notes = (this.notes || '') + `\n取消原因: ${reason}`;
  }
  
  return this;
};

// 复合索引
stockInRecordSchema.index({ status: 1, stock_in_date: -1 });
stockInRecordSchema.index({ created_by: 1, createdAt: -1 });
stockInRecordSchema.index({ supplier: 1, stock_in_date: -1 });
stockInRecordSchema.index({ 'items.product_id': 1 });

// 中间件：保存前生成单号和计算总计
stockInRecordSchema.pre('save', function(next) {
  if (this.isNew && !this.record_number) {
    this.record_number = this.constructor.generateRecordNumber();
  }

  // 验证和计算总计
  try {
    if (this.items && this.items.length > 0) {
      this.validateItems();
      this.calculateTotals();
    }
  } catch (error) {
    return next(error);
  }

  next();
});

module.exports = mongoose.model('StockInRecord', stockInRecordSchema);
