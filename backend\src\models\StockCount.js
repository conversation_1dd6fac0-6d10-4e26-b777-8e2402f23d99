/**
 * 盘点任务模型
 * 管理库存盘点计划、任务分配和差异处理
 */

const mongoose = require('mongoose');

// 盘点项目子模式
const stockCountItemSchema = new mongoose.Schema({
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, '产品ID是必需的']
  },
  expected_quantity: {
    type: Number,
    required: [true, '预期数量是必需的'],
    min: [0, '预期数量不能为负数'],
    default: 0
  },
  actual_quantity: {
    type: Number,
    min: [0, '实际数量不能为负数'],
    default: null
  },
  difference: {
    type: Number,
    default: null
  },
  difference_percentage: {
    type: Number,
    default: null
  },
  status: {
    type: String,
    enum: ['pending', 'counted', 'verified', 'adjusted'],
    default: 'pending'
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, '备注不能超过500个字符']
  },
  counted_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  counted_at: {
    type: Date
  },
  verified_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verified_at: {
    type: Date
  }
}, { _id: false });

// 盘点任务主模式
const stockCountSchema = new mongoose.Schema({
  count_id: {
    type: String,
    unique: true,
    index: true
  },
  title: {
    type: String,
    required: [true, '盘点标题是必需的'],
    trim: true,
    maxlength: [200, '盘点标题不能超过200个字符']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, '盘点描述不能超过1000个字符']
  },
  count_type: {
    type: String,
    required: [true, '盘点类型是必需的'],
    enum: ['full', 'partial', 'cycle', 'spot'],
    index: true
  },
  status: {
    type: String,
    enum: ['planned', 'in_progress', 'completed', 'cancelled', 'reviewing'],
    default: 'planned',
    index: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  },
  scheduled_date: {
    type: Date,
    required: [true, '计划日期是必需的'],
    index: true
  },
  start_date: {
    type: Date,
    index: true
  },
  end_date: {
    type: Date,
    index: true
  },
  deadline: {
    type: Date,
    index: true
  },
  location: {
    type: String,
    trim: true,
    maxlength: [100, '盘点位置不能超过100个字符']
  },
  categories: [{
    type: String,
    trim: true
  }],
  items: {
    type: [stockCountItemSchema],
    default: []
  },
  assigned_to: [{
    user_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['counter', 'verifier', 'supervisor'],
      default: 'counter'
    },
    assigned_at: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ['assigned', 'accepted', 'in_progress', 'completed'],
      default: 'assigned'
    }
  }],
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '创建人是必需的']
  },
  approved_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approved_at: {
    type: Date
  },
  summary: {
    total_items: {
      type: Number,
      default: 0
    },
    counted_items: {
      type: Number,
      default: 0
    },
    verified_items: {
      type: Number,
      default: 0
    },
    discrepancy_items: {
      type: Number,
      default: 0
    },
    total_expected_value: {
      type: Number,
      default: 0
    },
    total_actual_value: {
      type: Number,
      default: 0
    },
    value_difference: {
      type: Number,
      default: 0
    },
    accuracy_percentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    }
  },
  settings: {
    allow_negative_count: {
      type: Boolean,
      default: false
    },
    require_verification: {
      type: Boolean,
      default: true
    },
    auto_adjust_inventory: {
      type: Boolean,
      default: false
    },
    tolerance_percentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 5
    }
  },
  adjustments: [{
    item_index: {
      type: Number,
      required: true
    },
    adjustment_type: {
      type: String,
      enum: ['increase', 'decrease', 'correction'],
      required: true
    },
    quantity_change: {
      type: Number,
      required: true
    },
    reason: {
      type: String,
      required: true,
      trim: true
    },
    adjusted_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    adjusted_at: {
      type: Date,
      default: Date.now
    },
    approved: {
      type: Boolean,
      default: false
    }
  }],
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, '标签不能超过30个字符']
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 生成盘点ID
stockCountSchema.statics.generateCountId = function(countType) {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const timestamp = now.getTime().toString().slice(-6);
  
  const typePrefix = {
    'full': 'FC',
    'partial': 'PC',
    'cycle': 'CC',
    'spot': 'SC'
  };
  
  const prefix = typePrefix[countType] || 'SC';
  return `${prefix}${year}${month}${day}${timestamp}`;
};

// 添加盘点项目
stockCountSchema.methods.addCountItem = function(productId, expectedQuantity, notes = '') {
  this.items.push({
    product_id: productId,
    expected_quantity: expectedQuantity,
    notes: notes,
    status: 'pending'
  });
  
  this.updateSummary();
  return this;
};

// 更新盘点项目
stockCountSchema.methods.updateCountItem = function(itemIndex, actualQuantity, countedBy, notes = '') {
  if (itemIndex >= 0 && itemIndex < this.items.length) {
    const item = this.items[itemIndex];
    item.actual_quantity = actualQuantity;
    item.difference = actualQuantity - item.expected_quantity;
    item.difference_percentage = item.expected_quantity > 0 
      ? ((item.difference / item.expected_quantity) * 100).toFixed(2)
      : 0;
    item.status = 'counted';
    item.counted_by = countedBy;
    item.counted_at = new Date();
    if (notes) item.notes = notes;
    
    this.updateSummary();
  }
  
  return this;
};

// 验证盘点项目
stockCountSchema.methods.verifyCountItem = function(itemIndex, verifiedBy, approved = true) {
  if (itemIndex >= 0 && itemIndex < this.items.length) {
    const item = this.items[itemIndex];
    if (item.status === 'counted') {
      item.status = approved ? 'verified' : 'pending';
      item.verified_by = verifiedBy;
      item.verified_at = new Date();
      
      this.updateSummary();
    }
  }
  
  return this;
};

// 更新摘要统计
stockCountSchema.methods.updateSummary = function() {
  const summary = {
    total_items: this.items.length,
    counted_items: this.items.filter(item => item.status === 'counted' || item.status === 'verified').length,
    verified_items: this.items.filter(item => item.status === 'verified').length,
    discrepancy_items: this.items.filter(item => item.difference !== null && Math.abs(item.difference) > 0).length,
    total_expected_value: 0,
    total_actual_value: 0,
    value_difference: 0,
    accuracy_percentage: 0
  };
  
  // 计算价值统计（需要产品价格信息，这里简化处理）
  this.items.forEach(item => {
    if (item.expected_quantity !== null) {
      summary.total_expected_value += item.expected_quantity; // 简化：假设单价为1
    }
    if (item.actual_quantity !== null) {
      summary.total_actual_value += item.actual_quantity;
    }
  });
  
  summary.value_difference = summary.total_actual_value - summary.total_expected_value;
  
  // 计算准确率
  if (summary.total_items > 0) {
    const accurateItems = this.items.filter(item => 
      item.difference !== null && Math.abs(item.difference) === 0
    ).length;
    summary.accuracy_percentage = ((accurateItems / summary.total_items) * 100).toFixed(2);
  }
  
  this.summary = summary;
  return this;
};

// 开始盘点
stockCountSchema.methods.startCount = function() {
  this.status = 'in_progress';
  this.start_date = new Date();
  return this;
};

// 完成盘点
stockCountSchema.methods.completeCount = function() {
  this.status = 'completed';
  this.end_date = new Date();
  this.updateSummary();
  return this;
};

// 取消盘点
stockCountSchema.methods.cancelCount = function() {
  this.status = 'cancelled';
  return this;
};

// 分配用户
stockCountSchema.methods.assignUser = function(userId, role = 'counter') {
  const existingAssignment = this.assigned_to.find(
    assignment => assignment.user_id.toString() === userId.toString()
  );
  
  if (!existingAssignment) {
    this.assigned_to.push({
      user_id: userId,
      role: role,
      assigned_at: new Date(),
      status: 'assigned'
    });
  }
  
  return this;
};

// 移除用户分配
stockCountSchema.methods.unassignUser = function(userId) {
  this.assigned_to = this.assigned_to.filter(
    assignment => assignment.user_id.toString() !== userId.toString()
  );
  
  return this;
};

// 检查是否过期
stockCountSchema.methods.isOverdue = function() {
  return this.deadline && new Date() > this.deadline && this.status !== 'completed';
};

// 获取进度百分比
stockCountSchema.methods.getProgress = function() {
  if (this.summary.total_items === 0) return 0;
  return ((this.summary.counted_items / this.summary.total_items) * 100).toFixed(2);
};

// 复合索引
stockCountSchema.index({ count_type: 1, status: 1 });
stockCountSchema.index({ created_by: 1, createdAt: -1 });
stockCountSchema.index({ 'assigned_to.user_id': 1 });
stockCountSchema.index({ location: 1 });
stockCountSchema.index({ categories: 1 });

// 中间件：保存前生成盘点ID和更新摘要
stockCountSchema.pre('save', function(next) {
  if (this.isNew && !this.count_id) {
    this.count_id = this.constructor.generateCountId(this.count_type);
  }
  
  // 更新摘要
  this.updateSummary();
  
  next();
});

module.exports = mongoose.model('StockCount', stockCountSchema);
