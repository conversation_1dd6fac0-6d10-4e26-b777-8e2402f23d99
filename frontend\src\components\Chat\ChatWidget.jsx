import React, { useState } from 'react'
import { But<PERSON>, Card, Typography } from 'antd'
import { MessageOutlined, CloseOutlined } from '@ant-design/icons'

const { Text } = Typography

const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false)

  const toggleChat = () => {
    setIsOpen(!isOpen)
  }

  return (
    <>
      {/* 聊天窗口 */}
      {isOpen && (
        <div style={{
          position: 'fixed',
          bottom: 100,
          right: 24,
          width: 350,
          height: 500,
          zIndex: 1000,
          boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
          borderRadius: 12,
          overflow: 'hidden'
        }}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <MessageOutlined style={{ color: '#2C7AFF' }} />
                  <Text strong>AI客服助手</Text>
                </div>
                <Button 
                  type="text" 
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={toggleChat}
                />
              </div>
            }
            bodyStyle={{ 
              padding: 0, 
              height: 420,
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {/* 聊天内容区域 */}
            <div style={{ 
              flex: 1, 
              padding: 16,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#f7f9fc'
            }}>
              <div style={{ textAlign: 'center' }}>
                <MessageOutlined style={{ fontSize: 48, color: '#2C7AFF', marginBottom: 16 }} />
                <Text type="secondary">AI客服功能开发中</Text>
                <br />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  即将为您提供智能客服服务
                </Text>
              </div>
            </div>

            {/* 输入区域 */}
            <div style={{ 
              padding: 16, 
              borderTop: '1px solid #f0f0f0',
              background: 'white'
            }}>
              <div style={{
                padding: 12,
                background: '#f7f9fc',
                borderRadius: 8,
                textAlign: 'center'
              }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  功能开发中，敬请期待
                </Text>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* 聊天按钮 */}
      <Button
        type="primary"
        shape="circle"
        size="large"
        icon={<MessageOutlined />}
        onClick={toggleChat}
        style={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          width: 56,
          height: 56,
          zIndex: 999,
          boxShadow: '0 4px 12px rgba(44, 122, 255, 0.3)',
          fontSize: 20
        }}
        className="btn-scale"
      />
    </>
  )
}

export default ChatWidget
