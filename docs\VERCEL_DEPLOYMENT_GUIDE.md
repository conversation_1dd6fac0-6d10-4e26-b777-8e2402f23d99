# 🚀 Vercel平台前端部署指南

## 📋 部署前准备

### 1. 确保后端已部署
在部署前端之前，确保后端服务已在Render上成功部署并获得URL。

### 2. 更新前端环境变量
创建 `frontend/.env.production` 文件：

```bash
# API配置（使用实际的后端URL）
VITE_API_BASE_URL=https://inventory-ai-backend-xxx.onrender.com/api
VITE_SOCKET_URL=https://inventory-ai-backend-xxx.onrender.com

# 应用配置
VITE_APP_TITLE=库存管理系统 | HubGoodFood
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=production

# 功能开关（生产环境）
VITE_ENABLE_AI_CHAT=true
VITE_ENABLE_BARCODE_SCANNER=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_PWA=true

# UI配置
VITE_PRIMARY_COLOR=#2C7AFF
VITE_ENABLE_DARK_MODE=true
VITE_DEFAULT_LOCALE=zh-CN

# 调试配置（生产环境关闭）
VITE_DEBUG=false
VITE_SHOW_DEV_TOOLS=false
VITE_ENABLE_PERFORMANCE_MONITORING=true

# 构建配置（生产环境优化）
VITE_BUILD_OUTPUT_DIR=dist
VITE_CODE_SPLITTING=true
VITE_MINIFY=true
VITE_SOURCE_MAP=false
VITE_HMR=false

# 安全配置
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true

# 性能配置
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_IMAGE_OPTIMIZATION=true
VITE_CACHE_STRATEGY=aggressive
```

## 🔧 Vercel部署步骤

### 方法1: 通过Vercel Dashboard

#### 步骤1: 导入项目
1. 登录 [Vercel Dashboard](https://vercel.com/dashboard)
2. 点击 "New Project"
3. 从GitHub导入 `Chat-AI-3.0` 仓库

#### 步骤2: 配置项目设置
```yaml
Framework Preset: Vite
Root Directory: frontend
Build Command: npm run build:prod
Output Directory: dist
Install Command: npm ci
```

#### 步骤3: 配置环境变量
在Vercel项目设置中添加以下环境变量：

```bash
VITE_API_BASE_URL=https://your-render-backend.onrender.com/api
VITE_SOCKET_URL=https://your-render-backend.onrender.com
VITE_APP_TITLE=库存管理系统 | HubGoodFood
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=production
VITE_ENABLE_AI_CHAT=true
VITE_ENABLE_BARCODE_SCANNER=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_PWA=true
VITE_DEBUG=false
VITE_SHOW_DEV_TOOLS=false
```

#### 步骤4: 部署
点击 "Deploy" 开始部署。

### 方法2: 通过Vercel CLI

#### 步骤1: 安装Vercel CLI
```bash
npm install -g vercel
```

#### 步骤2: 登录Vercel
```bash
vercel login
```

#### 步骤3: 配置项目
```bash
cd frontend
vercel
```

按照提示配置：
- Set up and deploy? Yes
- Which scope? (选择您的账户)
- Link to existing project? No
- Project name: inventory-ai-frontend
- In which directory is your code located? ./

#### 步骤4: 配置环境变量
```bash
# 添加生产环境变量
vercel env add VITE_API_BASE_URL production
vercel env add VITE_SOCKET_URL production
# ... 添加其他环境变量
```

#### 步骤5: 部署到生产环境
```bash
vercel --prod
```

## 📊 部署验证

### 1. 检查部署状态
访问Vercel分配的URL，确认应用正常加载。

### 2. 功能验证清单

#### 基础功能
- [ ] 页面正常加载
- [ ] 路由导航正常
- [ ] 登录功能正常
- [ ] API连接正常

#### 核心功能
- [ ] 产品管理页面
- [ ] 库存管理页面
- [ ] 退货管理页面
- [ ] AI客服聊天功能

#### 性能检查
- [ ] 页面加载速度 < 3秒
- [ ] 资源压缩正常
- [ ] 缓存策略生效

### 3. 自动化验证
使用我们创建的验证脚本：

```bash
# 设置前端URL
export FRONTEND_URL=https://your-vercel-domain.vercel.app
export BACKEND_URL=https://your-render-backend.onrender.com

# 运行验证
node scripts/verify-deployment.js
```

## 🔧 部署后配置

### 1. 更新后端CORS配置
在Render后端服务中更新环境变量：

```bash
FRONTEND_URL=https://your-vercel-domain.vercel.app
CORS_ORIGIN=https://your-vercel-domain.vercel.app
```

### 2. 配置自定义域名（可选）
在Vercel项目设置中可以添加自定义域名。

### 3. 启用分析和监控
- 在Vercel Dashboard中启用Analytics
- 配置性能监控
- 设置部署通知

## 🔍 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 检查构建日志
vercel logs

# 本地测试构建
npm run build:prod
```

#### 2. API连接失败
- 检查 `VITE_API_BASE_URL` 配置
- 验证后端服务是否正常
- 检查CORS配置

#### 3. 路由问题
- 确认 `vercel.json` 中的路由配置
- 检查React Router配置

#### 4. 环境变量问题
```bash
# 检查环境变量
vercel env ls

# 更新环境变量
vercel env rm VARIABLE_NAME
vercel env add VARIABLE_NAME production
```

### 性能优化

#### 1. 启用压缩
确认 `vercel.json` 中的压缩配置。

#### 2. 优化图片
使用Vercel的图片优化功能。

#### 3. 缓存策略
检查静态资源的缓存头设置。

## 📈 监控和维护

### 1. 性能监控
- 使用Vercel Analytics
- 监控Core Web Vitals
- 设置性能预算

### 2. 错误监控
- 配置错误追踪
- 设置告警通知

### 3. 自动部署
- 配置GitHub集成
- 设置自动部署规则

## 🔒 安全配置

### 1. 安全头
确认 `vercel.json` 中的安全头配置：
- X-Content-Type-Options
- X-Frame-Options
- X-XSS-Protection
- Referrer-Policy

### 2. HTTPS
Vercel自动提供HTTPS，确保所有请求都使用HTTPS。

### 3. 环境变量安全
- 不要在前端代码中暴露敏感信息
- 使用 `VITE_` 前缀的环境变量

## 📞 支持资源

- [Vercel官方文档](https://vercel.com/docs)
- [Vite部署指南](https://vitejs.dev/guide/static-deploy.html)
- [React部署最佳实践](https://create-react-app.dev/docs/deployment/)
