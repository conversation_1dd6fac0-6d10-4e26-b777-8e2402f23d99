const multer = require('multer');
const logger = require('../utils/logger');

/**
 * 文件上传中间件配置
 * 使用multer处理multipart/form-data请求
 */

// 支持的文件类型
const ALLOWED_FILE_TYPES = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,image/webp').split(',');

// 文件大小限制
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024; // 5MB

/**
 * 文件过滤器
 * @param {Object} req - 请求对象
 * @param {Object} file - 文件对象
 * @param {Function} cb - 回调函数
 */
const fileFilter = (req, file, cb) => {
  try {
    logger.info('文件上传过滤检查', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    });

    // 检查文件类型
    if (!ALLOWED_FILE_TYPES.includes(file.mimetype)) {
      const error = new Error(`不支持的文件类型: ${file.mimetype}。支持的类型: ${ALLOWED_FILE_TYPES.join(', ')}`);
      error.code = 'INVALID_FILE_TYPE';
      return cb(error, false);
    }

    // 检查文件名
    if (!file.originalname || file.originalname.trim() === '') {
      const error = new Error('文件名不能为空');
      error.code = 'INVALID_FILE_NAME';
      return cb(error, false);
    }

    // 检查文件扩展名
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
    
    if (!allowedExtensions.includes(fileExtension)) {
      const error = new Error(`不支持的文件扩展名: ${fileExtension}。支持的扩展名: ${allowedExtensions.join(', ')}`);
      error.code = 'INVALID_FILE_EXTENSION';
      return cb(error, false);
    }

    cb(null, true);

  } catch (error) {
    logger.error('文件过滤器错误', {
      error: error.message,
      file: file?.originalname
    });
    
    cb(error, false);
  }
};

/**
 * 内存存储配置
 * 文件存储在内存中，便于后续处理和上传到S3
 */
const storage = multer.memoryStorage();

/**
 * 基础multer配置
 */
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 10, // 最多10个文件
    fields: 20, // 最多20个字段
    fieldNameSize: 100, // 字段名最大长度
    fieldSize: 1024 * 1024 // 字段值最大1MB
  }
});

/**
 * 错误处理中间件
 * @param {Error} error - 错误对象
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
const handleUploadError = (error, req, res, next) => {
  logger.error('文件上传错误', {
    error: error.message,
    code: error.code,
    field: error.field,
    url: req.originalUrl,
    method: req.method
  });

  // 根据错误类型返回相应的错误信息
  let statusCode = 400;
  let message = '文件上传失败';

  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      message = `文件大小超过限制 (${MAX_FILE_SIZE / 1024 / 1024}MB)`;
      break;
      
    case 'LIMIT_FILE_COUNT':
      message = '文件数量超过限制 (最多10个文件)';
      break;
      
    case 'LIMIT_UNEXPECTED_FILE':
      message = `意外的文件字段: ${error.field}`;
      break;
      
    case 'INVALID_FILE_TYPE':
    case 'INVALID_FILE_NAME':
    case 'INVALID_FILE_EXTENSION':
      message = error.message;
      break;
      
    case 'LIMIT_FIELD_COUNT':
      message = '表单字段数量超过限制';
      break;
      
    case 'LIMIT_FIELD_KEY':
      message = '表单字段名长度超过限制';
      break;
      
    case 'LIMIT_FIELD_VALUE':
      message = '表单字段值长度超过限制';
      break;
      
    default:
      message = error.message || '文件上传失败';
      statusCode = 500;
      break;
  }

  res.status(statusCode).json({
    success: false,
    message,
    code: error.code
  });
};

/**
 * 单文件上传中间件
 * @param {string} fieldName - 表单字段名，默认为'file'
 */
const singleFileUpload = (fieldName = 'file') => {
  return [
    upload.single(fieldName),
    handleUploadError
  ];
};

/**
 * 多文件上传中间件
 * @param {string} fieldName - 表单字段名，默认为'files'
 * @param {number} maxCount - 最大文件数量，默认为10
 */
const multipleFileUpload = (fieldName = 'files', maxCount = 10) => {
  return [
    upload.array(fieldName, maxCount),
    handleUploadError
  ];
};

/**
 * 多字段文件上传中间件
 * @param {Array} fields - 字段配置数组
 */
const fieldsFileUpload = (fields) => {
  return [
    upload.fields(fields),
    handleUploadError
  ];
};

/**
 * 任意文件上传中间件
 */
const anyFileUpload = () => {
  return [
    upload.any(),
    handleUploadError
  ];
};

/**
 * 验证上传的文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件
 */
const validateUploadedFiles = (req, res, next) => {
  try {
    // 检查是否有文件上传
    const hasFiles = req.file || (req.files && req.files.length > 0);
    
    if (!hasFiles) {
      return res.status(400).json({
        success: false,
        message: '未检测到上传的文件'
      });
    }

    // 记录上传的文件信息
    if (req.file) {
      logger.info('单文件上传验证通过', {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      });
    }

    if (req.files && req.files.length > 0) {
      logger.info('多文件上传验证通过', {
        fileCount: req.files.length,
        files: req.files.map(file => ({
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size
        }))
      });
    }

    next();

  } catch (error) {
    logger.error('文件验证失败', {
      error: error.message
    });

    res.status(500).json({
      success: false,
      message: '文件验证失败'
    });
  }
};

module.exports = {
  singleFileUpload,
  multipleFileUpload,
  fieldsFileUpload,
  anyFileUpload,
  validateUploadedFiles,
  handleUploadError,
  upload
};
