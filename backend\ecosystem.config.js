module.exports = {
  apps: [
    {
      name: 'inventory-ai-backend',
      script: './src/server.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 4000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 4000
      },
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 进程管理
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 10,
      
      // 监控配置
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      
      // 健康检查
      health_check_url: 'http://localhost:4000/health',
      health_check_grace_period: 3000,
      
      // 优雅关闭
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // 环境变量
      source_map_support: true,
      instance_var: 'INSTANCE_ID',
      
      // 自动重启配置
      autorestart: true,
      cron_restart: '0 2 * * *', // 每天凌晨2点重启
      
      // 集群配置
      merge_logs: true,
      combine_logs: true
    }
  ],
  
  // 部署配置
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: 'https://github.com/HubGoodFood/Chat-AI-3.0.git',
      path: '/var/www/inventory-ai',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    },
    staging: {
      user: 'deploy',
      host: ['your-staging-server.com'],
      ref: 'origin/develop',
      repo: 'https://github.com/HubGoodFood/Chat-AI-3.0.git',
      path: '/var/www/inventory-ai-staging',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env staging'
    }
  }
};
