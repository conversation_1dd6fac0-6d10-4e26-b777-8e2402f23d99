@echo off
REM Windows 批处理脚本 - 项目快速设置
REM 适用于 Windows Command Prompt

echo.
echo ========================================
echo   Chat AI 3.0 项目快速设置 (Windows)
echo ========================================
echo.

REM 检查 Node.js
echo 检查 Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+ 
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查 npm
echo 检查 npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

echo ✅ Node.js 和 npm 已安装

REM 检查环境变量文件
echo.
echo 检查环境配置...
if not exist "backend\.env" (
    echo ⚠️  环境变量文件不存在，正在创建...
    copy "backend\.env.sample" "backend\.env"
    echo ✅ 已创建环境变量文件，请编辑 backend\.env 填入配置
    echo.
    echo 按任意键继续...
    pause >nul
)

REM 安装依赖
echo.
echo 安装项目依赖...
echo 正在安装根目录依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 根目录依赖安装失败
    pause
    exit /b 1
)

echo 正在安装后端依赖...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)

echo 正在安装前端依赖...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)

cd ..

REM 初始化数据库
echo.
echo 初始化数据库...
cd backend
call npm run seed
if %errorlevel% neq 0 (
    echo ⚠️  数据库初始化失败，请检查 MongoDB 连接
)

cd ..

echo.
echo ========================================
echo   🎉 设置完成！
echo ========================================
echo.
echo 快速启动命令:
echo   npm run dev          # 同时启动前后端
echo   npm run dev:backend  # 仅启动后端
echo   npm run dev:frontend # 仅启动前端
echo   npm run check-env    # 检查环境
echo.
echo 访问地址:
echo   前端: http://localhost:5173
echo   后端: http://localhost:4000
echo   API文档: http://localhost:4000/api-docs
echo.
pause
