services:
  - type: web
    name: inventory-ai-backend
    env: node
    plan: starter
    region: singapore
    buildCommand: |
      cd backend
      rm -rf node_modules package-lock.json
      npm install --only=production --no-optional
    startCommand: |
      cd backend
      npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 4000
      - key: MONGO_URI
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: AWS_ACCESS_KEY_ID
        sync: false
      - key: AWS_SECRET_ACCESS_KEY
        sync: false
      - key: AWS_REGION
        value: us-east-1
      - key: S3_BUCKET
        sync: false
      - key: DEEPSEEK_API_KEY
        sync: false
      - key: DEEPSEEK_BASE_URL
        value: https://llm.chutes.ai/v1
      - key: DEEPSEEK_MODEL
        value: deepseek-ai/DeepSeek-V3-0324
      - key: AI_MOCK_MODE
        value: false
      - key: FRONTEND_URL
        value: https://inventory-ai-frontend.onrender.com
      - key: CORS_ORIGIN
        value: https://inventory-ai-frontend.onrender.com
      - key: RATE_LIMIT_WINDOW_MS
        value: 900000
      - key: RATE_LIMIT_MAX_REQUESTS
        value: 200
      - key: LOG_LEVEL
        value: warn
      - key: ENABLE_HTTPS_REDIRECT
        value: true
      - key: TRUST_PROXY
        value: true
      - key: SESSION_SECURE
        value: true
      - key: SMTP_HOST
        value: smtp.gmail.com
      - key: SMTP_PORT
        value: 587
      - key: SMTP_USER
        sync: false
      - key: SMTP_PASS
        sync: false
      - key: MAX_FILE_SIZE
        value: 10485760
      - key: ALLOWED_FILE_TYPES
        value: image/jpeg,image/png,image/gif,image/webp,application/pdf
      - key: REDIS_URL
        sync: false
      - key: REFUND_POLICY_PATH
        value: ../config/refund_policy.yaml
      - key: HEALTH_CHECK_ENABLED
        value: true
      - key: METRICS_ENABLED
        value: true
      - key: DB_MAX_POOL_SIZE
        value: 10
      - key: DB_MIN_POOL_SIZE
        value: 2
      - key: DB_MAX_IDLE_TIME
        value: 30000
      - key: API_VERSION
        value: v1
      - key: API_PREFIX
        value: /api
      - key: SHOW_STACK_TRACE
        value: false
      - key: DETAILED_ERRORS
        value: false
      - key: ENABLE_COMPRESSION
        value: true
      - key: ENABLE_ETAG
        value: true
      - key: CACHE_MAX_AGE
        value: 3600
