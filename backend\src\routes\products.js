const express = require('express');
const productController = require('../controllers/productController');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const router = express.Router();

// 所有产品路由都需要认证
router.use(authenticateToken);

// @desc    获取产品列表
// @route   GET /api/products
// @access  Private
router.get('/', requirePermission('products.read'), productController.getProducts);

// @desc    获取产品类别列表
// @route   GET /api/products/categories
// @access  Private
router.get('/categories', requirePermission('products.read'), productController.getCategories);

// @desc    根据条码获取产品
// @route   GET /api/products/barcode/:barcode
// @access  Private
router.get('/barcode/:barcode', requirePermission('products.read'), productController.getProductByBarcode);

// @desc    根据ID获取产品详情
// @route   GET /api/products/:id
// @access  Private
router.get('/:id', requirePermission('products.read'), productController.getProductById);

// @desc    新增产品
// @route   POST /api/products
// @access  Private
router.post('/', requirePermission('products.write'), productController.createProduct);

// @desc    更新产品
// @route   PUT /api/products/:id
// @access  Private
router.put('/:id', requirePermission('products.write'), productController.updateProduct);

// @desc    删除产品
// @route   DELETE /api/products/:id
// @access  Private
router.delete('/:id', requirePermission('products.delete'), productController.deleteProduct);

// @desc    生成产品条码标签
// @route   POST /api/products/:id/barcode-label
// @access  Private
router.post('/:id/barcode-label', requirePermission('products.read'), productController.generateBarcodeLabel);

// @desc    验证条码
// @route   POST /api/products/validate-barcode
// @access  Private
router.post('/validate-barcode', requirePermission('products.read'), productController.validateBarcode);

// @desc    批量生成条码
// @route   POST /api/products/generate-barcodes
// @access  Private
router.post('/generate-barcodes', requirePermission('products.write'), productController.generateBatchBarcodes);

// @desc    获取支持的条码格式
// @route   GET /api/products/barcode-formats
// @access  Private
router.get('/barcode-formats', requirePermission('products.read'), productController.getBarcodeFormats);

module.exports = router;
