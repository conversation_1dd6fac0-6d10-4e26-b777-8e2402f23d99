const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, '用户名是必需的'],
    unique: true,
    trim: true,
    minlength: [3, '用户名至少需要3个字符'],
    maxlength: [30, '用户名不能超过30个字符']
  },
  email: {
    type: String,
    required: [true, '邮箱是必需的'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
  },
  password: {
    type: String,
    required: [true, '密码是必需的'],
    minlength: [6, '密码至少需要6个字符']
  },
  name: {
    type: String,
    required: [true, '姓名是必需的'],
    trim: true,
    maxlength: [50, '姓名不能超过50个字符']
  },
  role: {
    type: String,
    enum: ['admin', 'manager', 'staff'],
    default: 'staff',
    required: true
  },
  avatar: {
    type: String,
    default: null
  },
  phone: {
    type: String,
    trim: true,
    match: [/^1[3-9]\d{9}$/, '请输入有效的手机号码']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date,
    default: null
  },
  permissions: [{
    type: String,
    enum: [
      'products.read', 'products.write', 'products.delete',
      'inventory.read', 'inventory.write', 'inventory.delete',
      'returns.read', 'returns.write', 'returns.approve',
      'chat.read', 'chat.manage',
      'users.read', 'users.write', 'users.delete',
      'reports.read', 'reports.generate'
    ]
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      return ret;
    }
  }
});

// 密码加密中间件
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 密码验证方法
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// 更新最后登录时间
userSchema.methods.updateLastLogin = function() {
  this.lastLogin = new Date();
  return this.save();
};

// 检查用户权限
userSchema.methods.hasPermission = function(permission) {
  return this.permissions.includes(permission) || this.role === 'admin';
};

// 根据角色设置默认权限
userSchema.methods.setDefaultPermissions = function() {
  const rolePermissions = {
    admin: [
      'products.read', 'products.write', 'products.delete',
      'inventory.read', 'inventory.write', 'inventory.delete',
      'returns.read', 'returns.write', 'returns.approve',
      'chat.read', 'chat.manage',
      'users.read', 'users.write', 'users.delete',
      'reports.read', 'reports.generate'
    ],
    manager: [
      'products.read', 'products.write',
      'inventory.read', 'inventory.write',
      'returns.read', 'returns.write', 'returns.approve',
      'chat.read', 'chat.manage',
      'reports.read', 'reports.generate'
    ],
    staff: [
      'products.read',
      'inventory.read', 'inventory.write',
      'returns.read', 'returns.write',
      'chat.read'
    ]
  };
  
  this.permissions = rolePermissions[this.role] || [];
  return this;
};

// 索引定义（统一在此处定义，避免重复）
userSchema.index({ username: 1 }, { unique: true }); // 用户名唯一索引
userSchema.index({ email: 1 }, { unique: true }); // 邮箱唯一索引
userSchema.index({ role: 1 }); // 角色索引
userSchema.index({ isActive: 1 }); // 状态索引
userSchema.index({ role: 1, isActive: 1 }); // 角色和状态复合索引

module.exports = mongoose.model('User', userSchema);
