#!/usr/bin/env node

/**
 * 跨平台环境检查脚本
 * 检查开发环境是否满足项目要求
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkCommand(command, name, minVersion = null) {
  try {
    const version = execSync(`${command} --version`, { encoding: 'utf8' }).trim();
    log(`✅ ${name}: ${version}`, 'green');
    
    if (minVersion && version) {
      const currentVersion = version.match(/\d+\.\d+\.\d+/)?.[0];
      if (currentVersion && compareVersions(currentVersion, minVersion) < 0) {
        log(`⚠️  警告: ${name} 版本过低，建议升级到 ${minVersion} 或更高版本`, 'yellow');
      }
    }
    return true;
  } catch (error) {
    log(`❌ ${name}: 未安装或不在 PATH 中`, 'red');
    return false;
  }
}

function compareVersions(version1, version2) {
  const v1 = version1.split('.').map(Number);
  const v2 = version2.split('.').map(Number);
  
  for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
    const num1 = v1[i] || 0;
    const num2 = v2[i] || 0;
    
    if (num1 > num2) return 1;
    if (num1 < num2) return -1;
  }
  return 0;
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description}: 存在`, 'green');
    return true;
  } else {
    log(`❌ ${description}: 不存在`, 'red');
    return false;
  }
}

function checkDirectory(dirPath, description) {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    log(`✅ ${description}: 存在`, 'green');
    return true;
  } else {
    log(`❌ ${description}: 不存在`, 'red');
    return false;
  }
}

function main() {
  log('🔍 开始环境检查...', 'blue');
  log(`📱 操作系统: ${os.type()} ${os.release()}`, 'blue');
  log(`💻 架构: ${os.arch()}`, 'blue');
  log(`🏠 用户目录: ${os.homedir()}`, 'blue');
  console.log();

  let allChecksPass = true;

  // 检查必需的命令行工具
  log('🛠️  检查开发工具...', 'bold');
  allChecksPass &= checkCommand('node', 'Node.js', '18.0.0');
  allChecksPass &= checkCommand('npm', 'npm', '8.0.0');
  allChecksPass &= checkCommand('git', 'Git');
  
  // 检查可选工具
  checkCommand('yarn', 'Yarn (可选)');
  checkCommand('docker', 'Docker (可选)');
  checkCommand('docker-compose', 'Docker Compose (可选)');
  console.log();

  // 检查项目文件
  log('📁 检查项目文件...', 'bold');
  allChecksPass &= checkFile('package.json', '根目录 package.json');
  allChecksPass &= checkFile('backend/package.json', '后端 package.json');
  allChecksPass &= checkFile('frontend/package.json', '前端 package.json');
  allChecksPass &= checkFile('backend/.env.sample', '环境变量模板');
  console.log();

  // 检查项目目录
  log('📂 检查项目目录...', 'bold');
  allChecksPass &= checkDirectory('backend', '后端目录');
  allChecksPass &= checkDirectory('frontend', '前端目录');
  allChecksPass &= checkDirectory('config', '配置目录');
  allChecksPass &= checkDirectory('docs', '文档目录');
  allChecksPass &= checkDirectory('scripts', '脚本目录');
  console.log();

  // 检查环境变量文件
  log('🔧 检查环境配置...', 'bold');
  if (checkFile('backend/.env', '环境变量文件')) {
    log('✅ 环境变量文件已配置', 'green');
  } else {
    log('⚠️  环境变量文件未配置，请复制 .env.sample 并填入配置', 'yellow');
    log(`   ${os.type() === 'Windows_NT' ? 'copy' : 'cp'} backend\\.env.sample backend\\.env`, 'yellow');
  }
  console.log();

  // 检查依赖安装
  log('📦 检查依赖安装...', 'bold');
  const backendNodeModules = checkDirectory('backend/node_modules', '后端依赖');
  const frontendNodeModules = checkDirectory('frontend/node_modules', '前端依赖');
  
  if (!backendNodeModules || !frontendNodeModules) {
    log('⚠️  依赖未完全安装，请运行: npm run install:all', 'yellow');
  }
  console.log();

  // 总结
  if (allChecksPass) {
    log('🎉 环境检查通过！可以开始开发了。', 'green');
    log('💡 快速启动命令:', 'blue');
    log('   npm run dev     # 同时启动前后端', 'blue');
    log('   npm run setup   # 安装依赖并初始化数据', 'blue');
  } else {
    log('❌ 环境检查未通过，请解决上述问题后重试。', 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkCommand, checkFile, checkDirectory };
