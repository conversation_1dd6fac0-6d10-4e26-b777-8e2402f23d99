const mongoose = require('mongoose');

const operationLogSchema = new mongoose.Schema({
  operation_id: {
    type: String,
    unique: true,
    required: [true, '操作ID是必需的'],
    index: true
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '用户ID是必需的'],
    index: true
  },
  operation_type: {
    type: String,
    required: [true, '操作类型是必需的'],
    enum: [
      // 认证相关
      'auth.login', 'auth.logout', 'auth.register', 'auth.password_change',
      // 产品相关
      'product.create', 'product.update', 'product.delete', 'product.view',
      // 库存相关
      'inventory.stock_in', 'inventory.stock_out', 'inventory.adjust', 'inventory.count',
      // 退货相关
      'return.create', 'return.approve', 'return.reject', 'return.process',
      // 聊天相关
      'chat.start', 'chat.message', 'chat.escalate', 'chat.resolve',
      // 报表相关
      'report.generate', 'report.view', 'report.export',
      // 系统相关
      'system.backup', 'system.restore', 'system.config_change',
      // 文件相关
      'file.upload', 'file.delete', 'file.download'
    ],
    index: true
  },
  resource_type: {
    type: String,
    enum: ['user', 'product', 'inventory', 'return_request', 'chat_session', 'report', 'file', 'system'],
    index: true
  },
  resource_id: {
    type: String,
    index: true
  },
  action: {
    type: String,
    required: [true, '操作动作是必需的'],
    enum: ['create', 'read', 'update', 'delete', 'approve', 'reject', 'process', 'export', 'upload', 'download'],
    index: true
  },
  status: {
    type: String,
    required: [true, '操作状态是必需的'],
    enum: ['success', 'failure', 'pending', 'cancelled'],
    default: 'success',
    index: true
  },
  ip_address: {
    type: String,
    required: [true, 'IP地址是必需的'],
    trim: true,
    index: true
  },
  user_agent: {
    type: String,
    trim: true
  },
  request_method: {
    type: String,
    enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    index: true
  },
  request_url: {
    type: String,
    trim: true,
    maxlength: [500, '请求URL不能超过500个字符']
  },
  request_params: {
    type: mongoose.Schema.Types.Mixed
  },
  response_status: {
    type: Number,
    min: 100,
    max: 599
  },
  response_time_ms: {
    type: Number,
    min: 0
  },
  before_data: {
    type: mongoose.Schema.Types.Mixed
  },
  after_data: {
    type: mongoose.Schema.Types.Mixed
  },
  changes: [{
    field: {
      type: String,
      required: true
    },
    old_value: mongoose.Schema.Types.Mixed,
    new_value: mongoose.Schema.Types.Mixed,
    change_type: {
      type: String,
      enum: ['create', 'update', 'delete'],
      required: true
    }
  }],
  error_details: {
    error_code: String,
    error_message: String,
    stack_trace: String
  },
  session_id: {
    type: String,
    index: true
  },
  correlation_id: {
    type: String,
    index: true
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, '标签不能超过30个字符']
  }],
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'low',
    index: true
  },
  category: {
    type: String,
    enum: ['security', 'business', 'system', 'audit', 'performance'],
    default: 'audit',
    index: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, '描述不能超过1000个字符']
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 生成操作ID
operationLogSchema.statics.generateOperationId = function() {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 8);
  return `op_${timestamp}_${random}`;
};

// 记录操作日志的静态方法
operationLogSchema.statics.logOperation = async function(logData) {
  const operationId = this.generateOperationId();
  
  const log = new this({
    operation_id: operationId,
    ...logData
  });
  
  try {
    await log.save();
    return log;
  } catch (error) {
    console.error('保存操作日志失败:', error);
    // 不抛出错误，避免影响主业务流程
    return null;
  }
};

// 记录成功操作
operationLogSchema.statics.logSuccess = async function(userId, operationType, resourceType, resourceId, additionalData = {}) {
  return this.logOperation({
    user_id: userId,
    operation_type: operationType,
    resource_type: resourceType,
    resource_id: resourceId,
    action: additionalData.action || 'read',
    status: 'success',
    ip_address: additionalData.ip_address || '127.0.0.1',
    user_agent: additionalData.user_agent,
    request_method: additionalData.request_method,
    request_url: additionalData.request_url,
    response_status: additionalData.response_status || 200,
    response_time_ms: additionalData.response_time_ms,
    after_data: additionalData.after_data,
    session_id: additionalData.session_id,
    description: additionalData.description
  });
};

// 记录失败操作
operationLogSchema.statics.logFailure = async function(userId, operationType, resourceType, error, additionalData = {}) {
  return this.logOperation({
    user_id: userId,
    operation_type: operationType,
    resource_type: resourceType,
    resource_id: additionalData.resource_id,
    action: additionalData.action || 'read',
    status: 'failure',
    ip_address: additionalData.ip_address || '127.0.0.1',
    user_agent: additionalData.user_agent,
    request_method: additionalData.request_method,
    request_url: additionalData.request_url,
    response_status: additionalData.response_status || 500,
    response_time_ms: additionalData.response_time_ms,
    error_details: {
      error_code: error.code || 'UNKNOWN_ERROR',
      error_message: error.message,
      stack_trace: error.stack
    },
    session_id: additionalData.session_id,
    severity: additionalData.severity || 'medium',
    description: additionalData.description || `操作失败: ${error.message}`
  });
};

// 记录数据变更
operationLogSchema.statics.logDataChange = async function(userId, operationType, resourceType, resourceId, beforeData, afterData, additionalData = {}) {
  const changes = this.calculateChanges(beforeData, afterData);
  
  return this.logOperation({
    user_id: userId,
    operation_type: operationType,
    resource_type: resourceType,
    resource_id: resourceId,
    action: additionalData.action || 'update',
    status: 'success',
    ip_address: additionalData.ip_address || '127.0.0.1',
    user_agent: additionalData.user_agent,
    request_method: additionalData.request_method,
    request_url: additionalData.request_url,
    response_status: additionalData.response_status || 200,
    before_data: beforeData,
    after_data: afterData,
    changes: changes,
    session_id: additionalData.session_id,
    description: additionalData.description || `${resourceType}数据已更新`
  });
};

// 计算数据变更
operationLogSchema.statics.calculateChanges = function(beforeData, afterData) {
  const changes = [];
  
  if (!beforeData && afterData) {
    // 新建记录
    Object.keys(afterData).forEach(key => {
      if (key !== '_id' && key !== '__v' && key !== 'createdAt' && key !== 'updatedAt') {
        changes.push({
          field: key,
          old_value: null,
          new_value: afterData[key],
          change_type: 'create'
        });
      }
    });
  } else if (beforeData && afterData) {
    // 更新记录
    Object.keys(afterData).forEach(key => {
      if (key !== '_id' && key !== '__v' && key !== 'createdAt' && key !== 'updatedAt') {
        const oldValue = beforeData[key];
        const newValue = afterData[key];
        
        if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
          changes.push({
            field: key,
            old_value: oldValue,
            new_value: newValue,
            change_type: 'update'
          });
        }
      }
    });
  }
  
  return changes;
};

// 获取用户操作历史
operationLogSchema.statics.getUserOperationHistory = function(userId, options = {}) {
  const query = { user_id: userId };
  
  if (options.operation_type) {
    query.operation_type = options.operation_type;
  }
  
  if (options.resource_type) {
    query.resource_type = options.resource_type;
  }
  
  if (options.status) {
    query.status = options.status;
  }
  
  if (options.start_date || options.end_date) {
    query.createdAt = {};
    if (options.start_date) {
      query.createdAt.$gte = new Date(options.start_date);
    }
    if (options.end_date) {
      query.createdAt.$lte = new Date(options.end_date);
    }
  }
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(options.limit || 100)
    .populate('user_id', 'username name');
};

// 复合索引
operationLogSchema.index({ user_id: 1, createdAt: -1 });
operationLogSchema.index({ operation_type: 1, createdAt: -1 });
operationLogSchema.index({ resource_type: 1, resource_id: 1 });
operationLogSchema.index({ status: 1, severity: 1 });
operationLogSchema.index({ ip_address: 1, createdAt: -1 });
operationLogSchema.index({ createdAt: -1 });

// 中间件：保存前生成操作ID
operationLogSchema.pre('save', function(next) {
  if (this.isNew && !this.operation_id) {
    this.operation_id = this.constructor.generateOperationId();
  }
  next();
});

module.exports = mongoose.model('OperationLog', operationLogSchema);
