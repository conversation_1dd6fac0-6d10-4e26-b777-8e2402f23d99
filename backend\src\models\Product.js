const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, '产品名称是必需的'],
    trim: true,
    maxlength: [100, '产品名称不能超过100个字符']
  },
  category: {
    type: String,
    required: [true, '产品类别是必需的'],
    enum: ['produce', 'poultry', 'dry_goods', 'frozen', 'dairy', 'other']
  },
  size: {
    type: String,
    trim: true,
    maxlength: [50, '规格不能超过50个字符']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, '描述不能超过500个字符']
  },
  barcode: {
    type: String,
    trim: true
  },
  photo_url: {
    type: String,
    trim: true
  },
  expiry_date: {
    type: Date
  },
  unit_price: {
    type: Number,
    min: [0, '单价不能为负数'],
    default: 0
  },
  supplier: {
    type: String,
    trim: true,
    maxlength: [100, '供应商名称不能超过100个字符']
  },
  safety_stock: {
    type: Number,
    min: [0, '安全库存不能为负数'],
    default: 0
  },
  storage_location: {
    type: String,
    trim: true,
    maxlength: [50, '存储位置不能超过50个字符']
  },
  is_active: {
    type: Boolean,
    default: true
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 生成条码的方法
productSchema.methods.generateBarcode = function() {
  if (!this.barcode) {
    // 生成格式：P + 时间戳后6位 + 随机3位数
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.barcode = `P${timestamp}${random}`;
  }
  return this.barcode;
};

// 检查是否需要补货
productSchema.methods.needsRestock = function(currentStock) {
  return currentStock <= this.safety_stock;
};

// 检查是否临期
productSchema.methods.isNearExpiry = function(daysThreshold = 30) {
  if (!this.expiry_date) return false;
  const now = new Date();
  const expiryDate = new Date(this.expiry_date);
  const diffTime = expiryDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= daysThreshold && diffDays > 0;
};

// 索引定义（统一在此处定义，避免重复）
productSchema.index({ name: 1 }); // 产品名称索引
productSchema.index({ category: 1 }); // 类别索引
productSchema.index({ barcode: 1 }, { unique: true, sparse: true }); // 条码唯一索引
productSchema.index({ expiry_date: 1 }); // 过期日期索引
productSchema.index({ is_active: 1 }); // 状态索引
productSchema.index({ name: 'text', description: 'text' }); // 全文搜索
productSchema.index({ category: 1, is_active: 1 }); // 复合索引
productSchema.index({ created_by: 1, createdAt: -1 }); // 创建者和时间复合索引

// 中间件：保存前生成条码
productSchema.pre('save', function(next) {
  if (this.isNew && !this.barcode) {
    this.generateBarcode();
  }
  next();
});

module.exports = mongoose.model('Product', productSchema);
