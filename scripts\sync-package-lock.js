#!/usr/bin/env node

/**
 * 同步package-lock.json文件脚本
 * 解决Render部署时的依赖同步问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查并修复前端依赖
function fixFrontendDependencies() {
  log('🔧 修复前端依赖同步问题...', 'blue');
  
  const frontendDir = path.join(process.cwd(), 'frontend');
  const packageJsonPath = path.join(frontendDir, 'package.json');
  const packageLockPath = path.join(frontendDir, 'package-lock.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    log('❌ frontend/package.json 不存在', 'red');
    return false;
  }
  
  try {
    // 读取package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    log('✅ 读取 package.json 成功', 'green');
    
    // 检查是否有package-lock.json
    if (fs.existsSync(packageLockPath)) {
      log('⚠️  删除旧的 package-lock.json', 'yellow');
      fs.unlinkSync(packageLockPath);
    }
    
    // 检查是否有node_modules
    const nodeModulesPath = path.join(frontendDir, 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
      log('⚠️  删除旧的 node_modules', 'yellow');
      fs.rmSync(nodeModulesPath, { recursive: true, force: true });
    }
    
    // 创建新的package-lock.json
    log('📦 重新生成 package-lock.json...', 'blue');
    
    // 切换到前端目录并运行npm install
    process.chdir(frontendDir);
    
    try {
      execSync('npm install', { 
        stdio: 'inherit',
        timeout: 300000 // 5分钟超时
      });
      log('✅ 前端依赖安装成功', 'green');
      
      // 验证package-lock.json是否生成
      if (fs.existsSync(packageLockPath)) {
        log('✅ package-lock.json 生成成功', 'green');
        return true;
      } else {
        log('❌ package-lock.json 生成失败', 'red');
        return false;
      }
      
    } catch (error) {
      log(`❌ npm install 失败: ${error.message}`, 'red');
      return false;
    }
    
  } catch (error) {
    log(`❌ 处理前端依赖失败: ${error.message}`, 'red');
    return false;
  } finally {
    // 切换回原目录
    process.chdir(path.join(frontendDir, '..'));
  }
}

// 检查并修复后端依赖
function fixBackendDependencies() {
  log('🔧 检查后端依赖...', 'blue');
  
  const backendDir = path.join(process.cwd(), 'backend');
  const packageJsonPath = path.join(backendDir, 'package.json');
  const packageLockPath = path.join(backendDir, 'package-lock.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    log('❌ backend/package.json 不存在', 'red');
    return false;
  }
  
  try {
    // 检查package-lock.json是否存在且有效
    if (fs.existsSync(packageLockPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const packageLock = JSON.parse(fs.readFileSync(packageLockPath, 'utf8'));
      
      // 简单检查版本是否匹配
      if (packageJson.name === packageLock.name && packageJson.version === packageLock.version) {
        log('✅ 后端依赖同步正常', 'green');
        return true;
      }
    }
    
    log('⚠️  后端依赖需要同步', 'yellow');
    
    // 删除旧文件
    if (fs.existsSync(packageLockPath)) {
      fs.unlinkSync(packageLockPath);
    }
    
    const nodeModulesPath = path.join(backendDir, 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
      fs.rmSync(nodeModulesPath, { recursive: true, force: true });
    }
    
    // 重新安装
    process.chdir(backendDir);
    
    try {
      execSync('npm install', { 
        stdio: 'inherit',
        timeout: 300000
      });
      log('✅ 后端依赖安装成功', 'green');
      return true;
      
    } catch (error) {
      log(`❌ 后端npm install失败: ${error.message}`, 'red');
      return false;
    }
    
  } catch (error) {
    log(`❌ 处理后端依赖失败: ${error.message}`, 'red');
    return false;
  } finally {
    process.chdir(path.join(backendDir, '..'));
  }
}

// 验证构建
function verifyBuild() {
  log('🔍 验证构建...', 'blue');
  
  try {
    // 验证前端构建
    log('📦 测试前端构建...', 'blue');
    process.chdir('frontend');
    
    try {
      execSync('npm run build:prod', { 
        stdio: 'inherit',
        timeout: 300000
      });
      
      // 检查dist目录
      if (fs.existsSync('dist')) {
        log('✅ 前端构建成功', 'green');
      } else {
        log('❌ 前端构建失败 - dist目录不存在', 'red');
        return false;
      }
      
    } catch (error) {
      log(`❌ 前端构建失败: ${error.message}`, 'red');
      return false;
    }
    
    process.chdir('..');
    
    // 验证后端
    log('🔍 检查后端配置...', 'blue');
    process.chdir('backend');
    
    try {
      // 检查后端启动脚本
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      if (packageJson.scripts && packageJson.scripts.start) {
        log('✅ 后端启动脚本存在', 'green');
      } else {
        log('❌ 后端缺少启动脚本', 'red');
        return false;
      }
      
    } catch (error) {
      log(`❌ 检查后端配置失败: ${error.message}`, 'red');
      return false;
    }
    
    process.chdir('..');
    return true;
    
  } catch (error) {
    log(`❌ 验证构建失败: ${error.message}`, 'red');
    return false;
  }
}

// 生成修复报告
function generateReport(frontendSuccess, backendSuccess, buildSuccess) {
  const timestamp = new Date().toISOString();
  const report = `# 📦 依赖同步修复报告

## 📊 修复结果
- **修复时间**: ${timestamp}
- **前端依赖**: ${frontendSuccess ? '✅ 成功' : '❌ 失败'}
- **后端依赖**: ${backendSuccess ? '✅ 成功' : '❌ 失败'}
- **构建验证**: ${buildSuccess ? '✅ 成功' : '❌ 失败'}

## 🔧 执行的操作
1. 删除旧的 package-lock.json 文件
2. 删除 node_modules 目录
3. 重新运行 npm install
4. 生成新的 package-lock.json
5. 验证构建过程

## 📋 下一步操作
${frontendSuccess && backendSuccess && buildSuccess ? `
✅ **修复成功！可以继续部署**

\`\`\`bash
# 提交修复的文件
git add .
git commit -m "Fix: Sync package-lock.json for Render deployment"
git push origin main
\`\`\`

然后在Render Dashboard中重新部署服务。
` : `
❌ **修复失败，需要手动处理**

请检查错误日志并手动修复依赖问题：

\`\`\`bash
# 手动修复前端
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run build:prod

# 手动修复后端
cd ../backend
rm -rf node_modules package-lock.json
npm install
\`\`\`
`}

## 📞 如果仍有问题
1. 检查Node.js版本兼容性
2. 清理npm缓存: \`npm cache clean --force\`
3. 检查网络连接
4. 联系技术支持

---
**报告生成时间**: ${timestamp}
`;

  fs.writeFileSync(`dependency-sync-report-${Date.now()}.md`, report);
  log('📄 修复报告已生成', 'blue');
}

// 主函数
function main() {
  log('🚀 开始修复依赖同步问题...', 'blue');
  log('=' * 50, 'blue');
  
  const frontendSuccess = fixFrontendDependencies();
  const backendSuccess = fixBackendDependencies();
  const buildSuccess = frontendSuccess && backendSuccess ? verifyBuild() : false;
  
  generateReport(frontendSuccess, backendSuccess, buildSuccess);
  
  if (frontendSuccess && backendSuccess && buildSuccess) {
    log('🎉 依赖同步修复完成！', 'green');
    log('📋 下一步: 提交代码并重新部署', 'blue');
  } else {
    log('❌ 修复过程中遇到问题，请查看报告', 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  fixFrontendDependencies,
  fixBackendDependencies,
  verifyBuild
};
