/**
 * Dashboard组件单元测试
 * 测试仪表盘数据展示、统计卡片渲染、基础布局等核心功能
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Dashboard from '../pages/Dashboard';

// 模拟Ant Design的message组件
jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    message: {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
      info: jest.fn(),
    },
  };
});

// 测试组件包装器
const renderDashboard = () => {
  return render(
    <ConfigProvider locale={zhCN}>
      <Dashboard />
    </ConfigProvider>
  );
};

describe('Dashboard组件测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('组件渲染测试', () => {
    test('应该正确渲染页面标题和描述', async () => {
      renderDashboard();

      expect(screen.getByText('仪表盘')).toBeInTheDocument();
      expect(screen.getByText('欢迎回来！这里是您的库存管理概览')).toBeInTheDocument();
    });

    test('应该正确渲染统计卡片标题', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('总商品数')).toBeInTheDocument();
        expect(screen.getByText('库存预警')).toBeInTheDocument();
        expect(screen.getByText('今日入库')).toBeInTheDocument();
        expect(screen.getByText('AI客服会话')).toBeInTheDocument();
      });
    });

    test('应该正确渲染统计数据值', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('156')).toBeInTheDocument(); // 总商品数
        expect(screen.getByText('8')).toBeInTheDocument(); // 库存预警
        expect(screen.getByText('23')).toBeInTheDocument(); // 今日入库
        expect(screen.getByText('45')).toBeInTheDocument(); // AI客服会话
      });
    });

    test('应该正确渲染变化百分比', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('+12% 较上周')).toBeInTheDocument();
        expect(screen.getByText('-3% 较上周')).toBeInTheDocument();
        expect(screen.getByText('+18% 较上周')).toBeInTheDocument();
        expect(screen.getByText('+25% 较上周')).toBeInTheDocument();
      });
    });

    test('应该正确渲染图表和动态区域', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('库存趋势')).toBeInTheDocument();
        expect(screen.getByText('最新动态')).toBeInTheDocument();
        expect(screen.getByText('图表组件将在后续开发中添加')).toBeInTheDocument();
      });
    });

    test('应该正确渲染最新动态内容', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('新增商品：日本贝贝南瓜')).toBeInTheDocument();
        expect(screen.getByText('库存预警：走地鸡库存不足')).toBeInTheDocument();
        expect(screen.getByText('退货申请：客户张三申请退货')).toBeInTheDocument();
      });
    });

    test('应该正确渲染时间戳', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('2分钟前')).toBeInTheDocument();
        expect(screen.getByText('15分钟前')).toBeInTheDocument();
        expect(screen.getByText('1小时前')).toBeInTheDocument();
      });
    });
  });

  describe('布局和样式测试', () => {
    test('应该正确应用CSS类名', async () => {
      const { container } = renderDashboard();

      // 检查主容器类名
      const mainContainer = container.querySelector('.fade-in');
      expect(mainContainer).toBeInTheDocument();

      // 检查页面头部类名
      const pageHeader = container.querySelector('.page-header');
      expect(pageHeader).toBeInTheDocument();

      // 检查统计卡片类名
      const statsCards = container.querySelectorAll('.stats-card');
      expect(statsCards).toHaveLength(4);

      // 检查内容卡片类名
      const contentCards = container.querySelectorAll('.content-card');
      expect(contentCards).toHaveLength(2);
    });

    test('应该正确设置统计卡片的颜色类名', async () => {
      const { container } = renderDashboard();

      await waitFor(() => {
        // 检查不同颜色的图标容器
        const primaryIcon = container.querySelector('.stats-card-icon.primary');
        const warningIcon = container.querySelector('.stats-card-icon.warning');
        const successIcon = container.querySelector('.stats-card-icon.success');
        const infoIcon = container.querySelector('.stats-card-icon.info');

        expect(primaryIcon).toBeInTheDocument();
        expect(warningIcon).toBeInTheDocument();
        expect(successIcon).toBeInTheDocument();
        expect(infoIcon).toBeInTheDocument();
      });
    });

    test('应该正确设置变化趋势的样式类名', async () => {
      const { container } = renderDashboard();

      await waitFor(() => {
        // 检查正向和负向变化的样式类名
        const positiveChanges = container.querySelectorAll('.stats-card-change.positive');
        const negativeChanges = container.querySelectorAll('.stats-card-change.negative');

        expect(positiveChanges).toHaveLength(3); // +12%, +18%, +25%
        expect(negativeChanges).toHaveLength(1); // -3%
      });
    });
  });

  describe('数据展示测试', () => {
    test('应该正确显示所有统计指标', async () => {
      renderDashboard();

      // 验证所有统计数据都正确显示
      const expectedStats = [
        { title: '总商品数', value: '156', change: '+12%' },
        { title: '库存预警', value: '8', change: '-3%' },
        { title: '今日入库', value: '23', change: '+18%' },
        { title: 'AI客服会话', value: '45', change: '+25%' }
      ];

      for (const stat of expectedStats) {
        await waitFor(() => {
          expect(screen.getByText(stat.title)).toBeInTheDocument();
          expect(screen.getByText(stat.value)).toBeInTheDocument();
          expect(screen.getByText(`${stat.change} 较上周`)).toBeInTheDocument();
        });
      }
    });

    test('应该正确显示最新动态列表', async () => {
      renderDashboard();

      const expectedActivities = [
        { content: '新增商品：日本贝贝南瓜', time: '2分钟前' },
        { content: '库存预警：走地鸡库存不足', time: '15分钟前' },
        { content: '退货申请：客户张三申请退货', time: '1小时前' }
      ];

      for (const activity of expectedActivities) {
        await waitFor(() => {
          expect(screen.getByText(activity.content)).toBeInTheDocument();
          expect(screen.getByText(activity.time)).toBeInTheDocument();
        });
      }
    });
  });

  describe('组件稳定性测试', () => {
    test('应该能够多次渲染而不出错', async () => {
      // 第一次渲染
      const { unmount } = renderDashboard();
      expect(screen.getByText('仪表盘')).toBeInTheDocument();
      
      // 卸载组件
      unmount();
      
      // 第二次渲染
      renderDashboard();
      expect(screen.getByText('仪表盘')).toBeInTheDocument();
      
      // 验证数据仍然正确显示
      await waitFor(() => {
        expect(screen.getByText('总商品数')).toBeInTheDocument();
        expect(screen.getByText('156')).toBeInTheDocument();
      });
    });

    test('应该正确处理组件的生命周期', async () => {
      const { rerender } = renderDashboard();
      
      // 验证初始渲染
      expect(screen.getByText('仪表盘')).toBeInTheDocument();
      
      // 重新渲染
      rerender(
        <ConfigProvider locale={zhCN}>
          <Dashboard />
        </ConfigProvider>
      );
      
      // 验证重新渲染后数据仍然正确
      await waitFor(() => {
        expect(screen.getByText('仪表盘')).toBeInTheDocument();
        expect(screen.getByText('总商品数')).toBeInTheDocument();
      });
    });
  });

  describe('边界情况测试', () => {
    test('应该正确处理空的props', async () => {
      // Dashboard组件不接收props，但测试组件在没有props时的表现
      renderDashboard();
      
      // 验证组件仍然正常渲染
      expect(screen.getByText('仪表盘')).toBeInTheDocument();
      await waitFor(() => {
        expect(screen.getByText('总商品数')).toBeInTheDocument();
      });
    });

    test('应该正确处理组件的默认状态', async () => {
      renderDashboard();
      
      // 验证所有默认数据都正确显示
      await waitFor(() => {
        // 统计数据
        expect(screen.getByText('156')).toBeInTheDocument();
        expect(screen.getByText('8')).toBeInTheDocument();
        expect(screen.getByText('23')).toBeInTheDocument();
        expect(screen.getByText('45')).toBeInTheDocument();
        
        // 动态内容
        expect(screen.getByText('新增商品：日本贝贝南瓜')).toBeInTheDocument();
        expect(screen.getByText('库存预警：走地鸡库存不足')).toBeInTheDocument();
        expect(screen.getByText('退货申请：客户张三申请退货')).toBeInTheDocument();
      });
    });
  });
});
