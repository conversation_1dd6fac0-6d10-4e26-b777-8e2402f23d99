/**
 * 报表管理路由
 * 处理报表相关的API请求
 */

const express = require('express');
const router = express.Router();
const reportController = require('../controllers/reportController');
const { authenticateToken, requirePermission } = require('../middleware/auth');

// @desc    生成报表
// @route   POST /api/reports/generate
// @access  Private (需要报表生成权限)
router.post('/generate', 
  authenticateToken, 
  requirePermission('reports.create'), 
  reportController.generateReport
);

// @desc    获取报表列表
// @route   GET /api/reports
// @access  Private (需要报表查看权限)
router.get('/', 
  authenticateToken, 
  requirePermission('reports.read'), 
  reportController.getReports
);

// @desc    获取报表详情
// @route   GET /api/reports/:id
// @access  Private (需要报表查看权限)
router.get('/:id', 
  authenticateToken, 
  requirePermission('reports.read'), 
  reportController.getReportById
);

// @desc    删除报表
// @route   DELETE /api/reports/:id
// @access  Private (需要报表删除权限)
router.delete('/:id', 
  authenticateToken, 
  requirePermission('reports.delete'), 
  reportController.deleteReport
);

// @desc    获取报表模板
// @route   GET /api/reports/templates
// @access  Private (需要报表查看权限)
router.get('/templates/list', 
  authenticateToken, 
  requirePermission('reports.read'), 
  reportController.getReportTemplates
);

module.exports = router;
