const { Product, Inventory, OperationLog } = require('../models');
const barcodeService = require('../services/barcodeService');
const logger = require('../utils/logger');

/**
 * 产品控制器
 * 处理产品的CRUD操作、条码管理等
 */
class ProductController {
  /**
   * 创建新产品
   */
  async createProduct(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        name,
        category,
        size,
        description,
        barcode,
        photo_url,
        expiry_date,
        unit_price,
        supplier,
        safety_stock,
        storage_location
      } = req.body;
      
      const userId = req.user.userId;
      
      // 验证必需字段
      if (!name || !category) {
        return res.status(400).json({
          success: false,
          message: '产品名称和类别是必需的'
        });
      }
      
      // 检查条码是否已存在
      if (barcode) {
        const existingProduct = await Product.findOne({ barcode });
        if (existingProduct) {
          return res.status(409).json({
            success: false,
            message: '条码已存在'
          });
        }
      }
      
      // 创建产品
      const product = new Product({
        name,
        category,
        size,
        description,
        barcode,
        photo_url,
        expiry_date: expiry_date ? new Date(expiry_date) : null,
        unit_price: unit_price || 0,
        supplier,
        safety_stock: safety_stock || 0,
        storage_location,
        created_by: userId,
        updated_by: userId
      });
      
      await product.save();
      
      // 创建对应的库存记录
      const inventory = new Inventory({
        product_id: product._id,
        current_stock: 0,
        reserved_stock: 0,
        available_stock: 0,
        location: storage_location,
        reorder_point: safety_stock || 0,
        cost_per_unit: unit_price || 0,
        updated_by: userId
      });
      
      await inventory.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'product.create',
        'product',
        product._id.toString(),
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          after_data: {
            name: product.name,
            category: product.category,
            barcode: product.barcode
          },
          description: `创建新产品: ${product.name}`
        }
      );
      
      logger.info(`新产品创建: ${product.name} (${product.barcode}) by ${req.user.username}`);
      
      res.status(201).json({
        success: true,
        message: '产品创建成功',
        data: { product }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'product.create',
        'product',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('创建产品错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取产品列表
   */
  async getProducts(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        page = 1,
        limit = 20,
        keyword,
        category,
        is_active = true,
        sort_by = 'createdAt',
        sort_order = 'desc'
      } = req.query;
      
      // 构建查询条件
      const query = {};
      
      if (is_active !== undefined) {
        query.is_active = is_active === 'true';
      }
      
      if (category) {
        query.category = category;
      }
      
      if (keyword) {
        query.$or = [
          { name: { $regex: keyword, $options: 'i' } },
          { description: { $regex: keyword, $options: 'i' } },
          { barcode: { $regex: keyword, $options: 'i' } },
          { supplier: { $regex: keyword, $options: 'i' } }
        ];
      }
      
      // 构建排序
      const sortOptions = {};
      sortOptions[sort_by] = sort_order === 'desc' ? -1 : 1;
      
      // 分页查询
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      const [products, total] = await Promise.all([
        Product.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('created_by', 'username name')
          .populate('updated_by', 'username name'),
        Product.countDocuments(query)
      ]);
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        req.user.userId,
        'product.view',
        'product',
        'list',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `查询产品列表: ${products.length}条记录`
        }
      );
      
      res.json({
        success: true,
        data: {
          products,
          pagination: {
            current_page: parseInt(page),
            per_page: parseInt(limit),
            total,
            total_pages: Math.ceil(total / parseInt(limit))
          }
        }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'product.view',
        'product',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime
        }
      );
      
      logger.error('获取产品列表错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 根据ID获取产品详情
   */
  async getProductById(req, res) {
    try {
      const { id } = req.params;
      
      const product = await Product.findById(id)
        .populate('created_by', 'username name')
        .populate('updated_by', 'username name');
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: '产品不存在'
        });
      }
      
      // 获取库存信息
      const inventory = await Inventory.findOne({ product_id: id });
      
      res.json({
        success: true,
        data: {
          product,
          inventory
        }
      });
      
    } catch (error) {
      logger.error('获取产品详情错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 根据条码获取产品
   */
  async getProductByBarcode(req, res) {
    try {
      const { barcode } = req.params;
      
      const product = await Product.findOne({ barcode, is_active: true })
        .populate('created_by', 'username name');
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: '产品不存在'
        });
      }
      
      // 获取库存信息
      const inventory = await Inventory.findOne({ product_id: product._id });
      
      res.json({
        success: true,
        data: {
          product,
          inventory
        }
      });
      
    } catch (error) {
      logger.error('根据条码获取产品错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 更新产品
   */
  async updateProduct(req, res) {
    const startTime = Date.now();
    
    try {
      const { id } = req.params;
      const userId = req.user.userId;
      
      const product = await Product.findById(id);
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: '产品不存在'
        });
      }
      
      // 保存更新前的数据
      const beforeData = product.toJSON();
      
      // 检查条码是否被其他产品使用
      if (req.body.barcode && req.body.barcode !== product.barcode) {
        const existingProduct = await Product.findOne({ 
          barcode: req.body.barcode,
          _id: { $ne: id }
        });
        
        if (existingProduct) {
          return res.status(409).json({
            success: false,
            message: '条码已被其他产品使用'
          });
        }
      }
      
      // 更新产品信息
      const allowedFields = [
        'name', 'category', 'size', 'description', 'barcode',
        'photo_url', 'expiry_date', 'unit_price', 'supplier',
        'safety_stock', 'storage_location', 'is_active'
      ];
      
      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          if (field === 'expiry_date' && req.body[field]) {
            product[field] = new Date(req.body[field]);
          } else {
            product[field] = req.body[field];
          }
        }
      });
      
      product.updated_by = userId;
      
      await product.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logDataChange(
        userId,
        'product.update',
        'product',
        id,
        beforeData,
        product.toJSON(),
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `更新产品: ${product.name}`
        }
      );
      
      logger.info(`产品更新: ${product.name} (${product.barcode}) by ${req.user.username}`);
      
      res.json({
        success: true,
        message: '产品更新成功',
        data: { product }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'product.update',
        'product',
        error,
        {
          resource_id: req.params.id,
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('更新产品错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 删除产品（软删除）
   */
  async deleteProduct(req, res) {
    const startTime = Date.now();
    
    try {
      const { id } = req.params;
      const userId = req.user.userId;
      
      const product = await Product.findById(id);
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: '产品不存在'
        });
      }
      
      // 检查是否有库存
      const inventory = await Inventory.findOne({ product_id: id });
      if (inventory && inventory.current_stock > 0) {
        return res.status(400).json({
          success: false,
          message: '产品仍有库存，无法删除'
        });
      }
      
      // 软删除
      product.is_active = false;
      product.updated_by = userId;
      await product.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'product.delete',
        'product',
        id,
        {
          action: 'delete',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `删除产品: ${product.name}`
        }
      );
      
      logger.info(`产品删除: ${product.name} (${product.barcode}) by ${req.user.username}`);
      
      res.json({
        success: true,
        message: '产品删除成功'
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'product.delete',
        'product',
        error,
        {
          resource_id: req.params.id,
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('删除产品错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取产品类别列表
   */
  async getCategories(req, res) {
    try {
      const categories = await Product.distinct('category', { is_active: true });
      
      res.json({
        success: true,
        data: { categories }
      });
      
    } catch (error) {
      logger.error('获取产品类别错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 生成产品条码标签
   */
  async generateBarcodeLabel(req, res) {
    try {
      const { id } = req.params;
      const { format = 'EAN13' } = req.query;

      const product = await Product.findById(id);

      if (!product) {
        return res.status(404).json({
          success: false,
          message: '产品不存在'
        });
      }

      // 如果没有条码，生成一个
      if (!product.barcode) {
        const newBarcode = barcodeService.generateBarcode(format);
        product.barcode = newBarcode;
        await product.save();
      }

      res.json({
        success: true,
        data: {
          barcode: product.barcode,
          product_name: product.name,
          category: product.category,
          size: product.size,
          format: format
        }
      });

    } catch (error) {
      logger.error('生成条码标签错误:', error);

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 验证条码
   */
  async validateBarcode(req, res) {
    try {
      const { barcode } = req.body;
      const { format } = req.query;

      if (!barcode) {
        return res.status(400).json({
          success: false,
          message: '条码不能为空'
        });
      }

      // 验证条码格式
      const validation = barcodeService.validateBarcode(barcode, format);

      // 检查条码是否已被使用
      let isUsed = false;
      let existingProduct = null;

      if (validation.isValid) {
        existingProduct = await Product.findOne({ barcode });
        isUsed = !!existingProduct;
      }

      res.json({
        success: true,
        data: {
          barcode,
          validation,
          isUsed,
          existingProduct: isUsed ? {
            id: existingProduct._id,
            name: existingProduct.name,
            category: existingProduct.category
          } : null
        }
      });

    } catch (error) {
      logger.error('验证条码错误:', error);

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 批量生成条码
   */
  async generateBatchBarcodes(req, res) {
    try {
      const { count = 10, format = 'EAN13', prefix = '' } = req.body;
      const userId = req.user.userId;

      if (count <= 0 || count > 100) {
        return res.status(400).json({
          success: false,
          message: '生成数量必须在1-100之间'
        });
      }

      // 生成条码
      const barcodes = barcodeService.generateBatchBarcodes(count, format, prefix);

      // 检查哪些条码已被使用
      const existingBarcodes = await Product.find({
        barcode: { $in: barcodes }
      }).select('barcode name');

      const usedBarcodes = new Set(existingBarcodes.map(p => p.barcode));

      const result = barcodes.map(barcode => ({
        barcode,
        isUsed: usedBarcodes.has(barcode),
        format
      }));

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'barcode.generate_batch',
        'barcode',
        'batch',
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          after_data: {
            count: barcodes.length,
            format,
            prefix,
            usedCount: result.filter(r => r.isUsed).length
          },
          description: `批量生成条码: ${barcodes.length}个`
        }
      );

      res.json({
        success: true,
        data: {
          barcodes: result,
          summary: {
            total: barcodes.length,
            available: result.filter(r => !r.isUsed).length,
            used: result.filter(r => r.isUsed).length
          }
        }
      });

    } catch (error) {
      logger.error('批量生成条码错误:', error);

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取支持的条码格式
   */
  async getBarcodeFormats(req, res) {
    try {
      const formats = barcodeService.getSupportedFormats();

      res.json({
        success: true,
        data: formats
      });

    } catch (error) {
      logger.error('获取条码格式错误:', error);

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = new ProductController();
