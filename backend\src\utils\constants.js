/**
 * 系统常量定义
 */

// 产品类别
const PRODUCT_CATEGORIES = {
  PRODUCE: 'produce',
  POULTRY: 'poultry', 
  DRY_GOODS: 'dry_goods',
  FROZEN: 'frozen',
  DAIRY: 'dairy',
  OTHER: 'other'
};

// 用户角色
const USER_ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  STAFF: 'staff'
};

// 用户权限
const PERMISSIONS = {
  // 产品权限
  PRODUCTS_READ: 'products.read',
  PRODUCTS_WRITE: 'products.write',
  PRODUCTS_DELETE: 'products.delete',
  
  // 库存权限
  INVENTORY_READ: 'inventory.read',
  INVENTORY_WRITE: 'inventory.write',
  INVENTORY_DELETE: 'inventory.delete',
  
  // 退货权限
  RETURNS_READ: 'returns.read',
  RETURNS_WRITE: 'returns.write',
  RETURNS_APPROVE: 'returns.approve',
  
  // 聊天权限
  CHAT_READ: 'chat.read',
  CHAT_MANAGE: 'chat.manage',
  
  // 用户权限
  USERS_READ: 'users.read',
  USERS_WRITE: 'users.write',
  USERS_DELETE: 'users.delete',
  
  // 报表权限
  REPORTS_READ: 'reports.read',
  REPORTS_GENERATE: 'reports.generate'
};

// 入库单状态
const STOCK_IN_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// 退货申请状态
const RETURN_STATUS = {
  SUBMITTED: 'submitted',
  UNDER_REVIEW: 'under_review',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// 退货原因
const RETURN_REASONS = {
  QUALITY_ISSUE: 'quality_issue',
  WRONG_ITEM: 'wrong_item',
  DAMAGED: 'damaged',
  EXPIRED: 'expired',
  CUSTOMER_CHANGE: 'customer_change',
  OTHER: 'other'
};

// 退货优先级
const RETURN_PRIORITY = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
};

// 退款方式
const REFUND_METHODS = {
  ORIGINAL_PAYMENT: 'original_payment',
  STORE_CREDIT: 'store_credit',
  VENMO: 'venmo',
  CASH: 'cash',
  EXCHANGE: 'exchange'
};

// 聊天会话状态
const CHAT_STATUS = {
  ACTIVE: 'active',
  WAITING: 'waiting',
  ESCALATED: 'escalated',
  RESOLVED: 'resolved',
  CLOSED: 'closed'
};

// 聊天消息类型
const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  SYSTEM: 'system',
  ACTION: 'action'
};

// 聊天意图
const CHAT_INTENTS = {
  GREETING: 'greeting',
  INVENTORY_QUERY: 'inventory_query',
  RETURN_REQUEST: 'return_request',
  COMPLAINT: 'complaint',
  GENERAL_QUESTION: 'general_question',
  OTHER: 'other'
};

// 操作日志类型
const OPERATION_TYPES = {
  // 认证相关
  AUTH_LOGIN: 'auth.login',
  AUTH_LOGOUT: 'auth.logout',
  AUTH_REGISTER: 'auth.register',
  AUTH_PASSWORD_CHANGE: 'auth.password_change',
  
  // 产品相关
  PRODUCT_CREATE: 'product.create',
  PRODUCT_UPDATE: 'product.update',
  PRODUCT_DELETE: 'product.delete',
  PRODUCT_VIEW: 'product.view',
  
  // 库存相关
  INVENTORY_STOCK_IN: 'inventory.stock_in',
  INVENTORY_STOCK_OUT: 'inventory.stock_out',
  INVENTORY_ADJUST: 'inventory.adjust',
  INVENTORY_COUNT: 'inventory.count',
  
  // 退货相关
  RETURN_CREATE: 'return.create',
  RETURN_APPROVE: 'return.approve',
  RETURN_REJECT: 'return.reject',
  RETURN_PROCESS: 'return.process',
  
  // 聊天相关
  CHAT_START: 'chat.start',
  CHAT_MESSAGE: 'chat.message',
  CHAT_ESCALATE: 'chat.escalate',
  CHAT_RESOLVE: 'chat.resolve',
  
  // 报表相关
  REPORT_GENERATE: 'report.generate',
  REPORT_VIEW: 'report.view',
  REPORT_EXPORT: 'report.export',
  
  // 系统相关
  SYSTEM_BACKUP: 'system.backup',
  SYSTEM_RESTORE: 'system.restore',
  SYSTEM_CONFIG_CHANGE: 'system.config_change',
  
  // 文件相关
  FILE_UPLOAD: 'file.upload',
  FILE_DELETE: 'file.delete',
  FILE_DOWNLOAD: 'file.download'
};

// 报表类型
const REPORT_TYPES = {
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  ANNUAL: 'annual',
  CUSTOM: 'custom',
  DAILY: 'daily'
};

// 报表状态
const REPORT_STATUS = {
  GENERATING: 'generating',
  COMPLETED: 'completed',
  FAILED: 'failed',
  ARCHIVED: 'archived'
};

// 文件类型
const FILE_TYPES = {
  IMAGE: 'image',
  DOCUMENT: 'document',
  OTHER: 'other'
};

// 支付方式
const PAYMENT_METHODS = {
  CASH: 'cash',
  WECHAT: 'wechat',
  ALIPAY: 'alipay',
  VENMO: 'venmo',
  CREDIT_CARD: 'credit_card',
  OTHER: 'other'
};

// 商品状态
const PRODUCT_CONDITIONS = {
  UNOPENED: 'unopened',
  OPENED: 'opened',
  DAMAGED: 'damaged',
  EXPIRED: 'expired'
};

// 预警严重程度
const ALERT_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// 预警类型
const ALERT_TYPES = {
  LOW_STOCK: 'low_stock',
  OUT_OF_STOCK: 'out_of_stock',
  EXPIRY: 'expiry',
  SYSTEM: 'system'
};

// 默认分页设置
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100
};

// 时间常量（毫秒）
const TIME_CONSTANTS = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  MONTH: 30 * 24 * 60 * 60 * 1000
};

// 系统配置
const SYSTEM_CONFIG = {
  // 退货时限（小时）
  RETURN_TIME_LIMIT: 24,
  
  // 临期预警天数
  EXPIRY_WARNING_DAYS: 30,
  
  // 最大文件上传大小（字节）
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  
  // 允许的图片格式
  ALLOWED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  
  // 允许的文档格式
  ALLOWED_DOCUMENT_FORMATS: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
  
  // JWT过期时间
  JWT_EXPIRES_IN: '24h',
  
  // 密码最小长度
  MIN_PASSWORD_LENGTH: 6,
  
  // 最大登录尝试次数
  MAX_LOGIN_ATTEMPTS: 5,
  
  // 账户锁定时间（分钟）
  ACCOUNT_LOCKOUT_TIME: 30
};

module.exports = {
  PRODUCT_CATEGORIES,
  USER_ROLES,
  PERMISSIONS,
  STOCK_IN_STATUS,
  RETURN_STATUS,
  RETURN_REASONS,
  RETURN_PRIORITY,
  REFUND_METHODS,
  CHAT_STATUS,
  MESSAGE_TYPES,
  CHAT_INTENTS,
  OPERATION_TYPES,
  REPORT_TYPES,
  REPORT_STATUS,
  FILE_TYPES,
  PAYMENT_METHODS,
  PRODUCT_CONDITIONS,
  ALERT_SEVERITY,
  ALERT_TYPES,
  PAGINATION,
  TIME_CONSTANTS,
  SYSTEM_CONFIG
};
