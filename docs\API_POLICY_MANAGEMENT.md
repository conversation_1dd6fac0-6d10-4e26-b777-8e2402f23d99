# 政策管理API文档

> **版本**: v1.0  
> **创建时间**: 2025-06-15  
> **最后更新**: 2025-06-15  
> **状态**: 已实现并测试通过

## 📋 目录

- [概述](#概述)
- [认证说明](#认证说明)
- [API端点](#api端点)
- [数据模型](#数据模型)
- [使用示例](#使用示例)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)

## 概述

政策管理API提供了完整的政策生命周期管理功能，包括政策的创建、查询、更新、删除、搜索和版本管理。该API支持从policy.json文件批量导入政策，并提供智能搜索和统计功能。

### 核心功能
- ✅ 政策CRUD操作
- ✅ 政策搜索和过滤
- ✅ 版本管理和历史记录
- ✅ 批量导入功能
- ✅ 使用统计和分析
- ✅ 权限控制和审计日志

### 技术特性
- **RESTful设计**: 遵循REST API设计规范
- **权限控制**: 基于角色的访问控制
- **版本管理**: 完整的政策版本控制
- **缓存优化**: 智能缓存提高响应速度
- **搜索优化**: 支持全文搜索和相关性排序

## 认证说明

### 权限级别
- **公开访问**: 政策查询和搜索
- **用户权限**: 查看详细政策信息
- **管理员权限**: 政策的增删改、导入、统计

### 认证方式
```http
Authorization: Bearer <JWT_TOKEN>
```

## API端点

### 1. 获取所有政策

```http
GET /api/policy
```

#### 查询参数
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| category | string | 否 | null | 政策类别过滤 |
| active_only | boolean | 否 | true | 只返回活跃政策 |
| include_versions | boolean | 否 | false | 是否包含版本历史 |
| limit | number | 否 | 50 | 返回数量限制 |
| page | number | 否 | 1 | 页码 |

#### 响应示例
```json
{
  "success": true,
  "data": {
    "policies": [
      {
        "policy_id": "policy_001",
        "name": "配送政策",
        "category": "delivery",
        "current_version": "2.0.0",
        "description": "配送服务相关政策",
        "is_active": true,
        "effective_date": "2025-01-01T00:00:00.000Z",
        "priority": 8,
        "usage_count": 156,
        "tags": ["配送", "运费", "时间"],
        "created_at": "2025-06-15T10:00:00.000Z",
        "updated_at": "2025-06-15T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 8,
      "pages": 1
    }
  },
  "response_time_ms": 45
}
```

### 2. 搜索政策

```http
GET /api/policy/search
```

#### 查询参数
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| keyword | string | 是 | - | 搜索关键词 |
| category | string | 否 | null | 政策类别过滤 |
| active_only | boolean | 否 | true | 只搜索活跃政策 |
| limit | number | 否 | 20 | 返回数量限制 |

#### 响应示例
```json
{
  "success": true,
  "data": {
    "keyword": "配送",
    "results": [
      {
        "policy_id": "policy_001",
        "name": "配送政策",
        "category": "delivery",
        "relevanceScore": 0.95,
        "matchedFields": ["name", "tags", "content"],
        "current_content": ["配送时间：每周三截单，周五送货", "..."]
      }
    ],
    "count": 1
  },
  "response_time_ms": 32
}
```

### 3. 获取单个政策详情

```http
GET /api/policy/:policy_id
```

#### 路径参数
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| policy_id | string | 是 | 政策唯一标识符 |

#### 查询参数
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| include_versions | boolean | 否 | false | 是否包含版本历史 |

#### 响应示例
```json
{
  "success": true,
  "data": {
    "policy_id": "policy_001",
    "name": "配送政策",
    "category": "delivery",
    "description": "配送服务相关政策",
    "current_version": "2.0.0",
    "current_content": [
      "配送时间：每周三截单，周五送货",
      "起送标准：三只鸡或同等金额起可送到家",
      "配送范围：以波士顿为中心，Quincy、Waltham、Newton以内",
      "配送费用：上述区域内运费$5/次",
      "免费配送：10只鸡或同等金额以上免费送到家"
    ],
    "tags": ["配送", "运费", "时间"],
    "keywords": ["配送", "运费", "波士顿", "免费"],
    "priority": 8,
    "is_active": true,
    "is_public": true,
    "effective_date": "2025-01-01T00:00:00.000Z",
    "usage_count": 156,
    "feedback_score": 4.5,
    "created_at": "2025-06-15T10:00:00.000Z",
    "updated_at": "2025-06-15T10:00:00.000Z",
    "versions": [
      {
        "version": "2.0.0",
        "content": [...],
        "created_by": "admin_user_id",
        "created_at": "2025-06-15T10:00:00.000Z",
        "change_summary": "更新配送范围和费用标准",
        "is_active": true
      }
    ]
  },
  "response_time_ms": 28
}
```

### 4. 创建新政策

```http
POST /api/policy
```

**权限要求**: 管理员

#### 请求体
```json
{
  "name": "新政策名称",
  "category": "delivery",
  "description": "政策描述",
  "current_content": [
    "政策条款1",
    "政策条款2"
  ],
  "tags": ["标签1", "标签2"],
  "keywords": ["关键词1", "关键词2"],
  "priority": 5,
  "effective_date": "2025-06-15T00:00:00.000Z",
  "expiry_date": null
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "policy_id": "policy_new_001",
    "name": "新政策名称",
    "category": "delivery",
    "current_version": "1.0.0",
    "created_at": "2025-06-15T10:00:00.000Z"
  },
  "message": "政策创建成功",
  "response_time_ms": 156
}
```

### 5. 更新政策

```http
PUT /api/policy/:policy_id
```

**权限要求**: 管理员

#### 请求体
```json
{
  "name": "更新后的政策名称",
  "description": "更新后的描述",
  "current_content": [
    "更新后的政策条款1",
    "更新后的政策条款2"
  ],
  "change_summary": "更新说明"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "policy_id": "policy_001",
    "name": "更新后的政策名称",
    "current_version": "2.1.0",
    "updated_at": "2025-06-15T10:30:00.000Z"
  },
  "message": "政策更新成功",
  "response_time_ms": 89
}
```

### 6. 删除政策

```http
DELETE /api/policy/:policy_id
```

**权限要求**: 管理员

#### 响应示例
```json
{
  "success": true,
  "message": "政策删除成功",
  "response_time_ms": 45
}
```

### 7. 导入政策文件

```http
POST /api/policy/import
```

**权限要求**: 管理员

#### 功能说明
从项目根目录的`policy.json`文件批量导入政策数据。

#### 响应示例
```json
{
  "success": true,
  "data": {
    "imported": 8,
    "updated": 0,
    "errors": []
  },
  "message": "政策导入完成",
  "response_time_ms": 1250
}
```

### 8. 获取政策统计

```http
GET /api/policy/statistics
```

**权限要求**: 管理员

#### 响应示例
```json
{
  "success": true,
  "data": {
    "total": 8,
    "active": 8,
    "needingReview": 2,
    "byCategory": [
      {
        "_id": "delivery",
        "count": 1,
        "active_count": 1,
        "total_usage": 156
      },
      {
        "_id": "after_sale",
        "count": 1,
        "active_count": 1,
        "total_usage": 89
      }
    ]
  },
  "response_time_ms": 67
}
```

## 数据模型

### Policy模型结构

```javascript
{
  policy_id: String,           // 政策唯一标识符
  name: String,               // 政策名称
  category: String,           // 政策类别
  description: String,        // 政策描述
  current_version: String,    // 当前版本号
  current_content: Mixed,     // 当前政策内容
  versions: [                 // 版本历史
    {
      version: String,
      content: Mixed,
      created_by: ObjectId,
      created_at: Date,
      change_summary: String,
      is_active: Boolean
    }
  ],
  tags: [String],            // 标签
  keywords: [String],        // 关键词
  priority: Number,          // 优先级 (1-10)
  is_active: Boolean,        // 是否活跃
  is_public: Boolean,        // 是否公开
  effective_date: Date,      // 生效日期
  expiry_date: Date,         // 过期日期
  created_by: ObjectId,      // 创建者
  updated_by: ObjectId,      // 更新者
  last_reviewed_at: Date,    // 最后审核时间
  review_frequency_days: Number, // 审核频率
  usage_count: Number,       // 使用次数
  feedback_score: Number,    // 反馈评分
  metadata: {                // 元数据
    source: String,
    import_file: String,
    external_id: String,
    sync_status: String
  }
}
```

### 政策类别枚举

| 类别 | 说明 | 示例 |
|------|------|------|
| mission | 使命理念 | 平台价值观、服务理念 |
| group_rules | 群规管理 | 社区行为规范 |
| product_quality | 产品质量 | 质量保证政策 |
| delivery | 配送服务 | 配送时间、费用、范围 |
| payment | 付款方式 | 付款流程、方式 |
| pickup | 取货点 | 取货地点、时间 |
| after_sale | 售后服务 | 退货、换货政策 |
| community | 社区文化 | 社区互助精神 |
| other | 其他 | 其他类型政策 |

## 使用示例

### 获取配送相关政策

```javascript
// 搜索配送政策
const response = await fetch('/api/policy/search?keyword=配送&category=delivery');
const data = await response.json();

if (data.success) {
  console.log('找到配送政策:', data.data.results.length);
  data.data.results.forEach(policy => {
    console.log(`- ${policy.name}: ${policy.relevanceScore}`);
  });
}
```

### 创建新政策

```javascript
const newPolicy = {
  name: '客户服务政策',
  category: 'other',
  description: '客户服务相关规定',
  current_content: [
    '客服工作时间：周一至周五 9:00-18:00',
    '响应时间：工作时间内2小时内回复',
    '服务态度：友好、专业、耐心'
  ],
  tags: ['客服', '服务时间', '响应'],
  priority: 6
};

const response = await fetch('/api/policy', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(newPolicy)
});

const result = await response.json();
console.log('政策创建结果:', result);
```

### 批量导入政策

```javascript
// 导入policy.json文件中的所有政策
const response = await fetch('/api/policy/import', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${adminToken}`
  }
});

const result = await response.json();
if (result.success) {
  console.log(`成功导入 ${result.data.imported} 个政策`);
  if (result.data.errors.length > 0) {
    console.log('导入错误:', result.data.errors);
  }
}
```

## 错误处理

### 常见错误码

| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 政策不存在 |
| 409 | Conflict | 政策名称冲突 |
| 500 | Internal Server Error | 服务器内部错误 |

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "error_code": "ERROR_CODE",
  "timestamp": "2025-06-15T10:00:00.000Z"
}
```

## 最佳实践

### 1. 政策命名规范
- 使用清晰、描述性的名称
- 避免使用特殊字符
- 保持名称简洁（建议50字符以内）

### 2. 版本管理
- 重大变更时更新主版本号
- 小幅修改时更新次版本号
- 提供详细的变更摘要

### 3. 标签和关键词
- 使用相关的标签便于分类
- 添加关键词提高搜索准确性
- 保持标签和关键词的一致性

### 4. 缓存策略
- 政策查询会被缓存5分钟
- 政策更新后缓存会自动清除
- 高频访问的政策会被优先缓存

### 5. 性能优化
- 使用分页避免一次性加载大量数据
- 合理使用搜索过滤条件
- 避免频繁的政策更新操作

---

**📝 文档维护**
- 创建者: AI Assistant
- 最后更新: 2025-06-15
- 版本: v1.0
- 状态: 已完成并测试通过
