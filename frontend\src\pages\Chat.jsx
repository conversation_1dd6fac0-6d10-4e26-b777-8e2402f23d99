import React, { useState, useEffect } from 'react'
import {
  Typo<PERSON>,
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Row,
  Col,
  Statistic,
  Tabs,
  Tag,
  DatePicker,
  List,
  Avatar,
  Divider,
  Progress,
  Alert,
  Badge
} from 'antd'
import {
  MessageOutlined,
  RobotOutlined,
  UserOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  FileTextOutlined,
  ReloadOutlined,
  SearchOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { chatService } from '../services/chat'
import dayjs from 'dayjs'

const { Title } = Typography
const { Option } = Select
const { Search } = Input
const { TabPane } = Tabs
const { RangePicker } = DatePicker
const { TextArea } = Input

const Chat = () => {
  // 状态管理
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('sessions')

  // 聊天会话数据
  const [sessions, setSessions] = useState([])
  const [sessionsTotal, setSessionsTotal] = useState(0)
  const [sessionsPage, setSessionsPage] = useState(1)
  const [sessionsPageSize, setSessionsPageSize] = useState(10)

  // 政策数据
  const [policies, setPolicies] = useState([])
  const [policiesTotal, setPoliciesTotal] = useState(0)
  const [policiesPage, setPoliciesPage] = useState(1)
  const [policiesPageSize, setPoliciesPageSize] = useState(10)

  // 统计数据
  const [chatStats, setChatStats] = useState({})
  const [policyStats, setPolicyStats] = useState({})

  // 筛选条件
  const [searchText, setSearchText] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [dateRange, setDateRange] = useState([])

  // 模态框状态
  const [isSessionDetailVisible, setIsSessionDetailVisible] = useState(false)
  const [isPolicyModalVisible, setIsPolicyModalVisible] = useState(false)
  const [selectedSession, setSelectedSession] = useState(null)
  const [selectedPolicy, setSelectedPolicy] = useState(null)
  const [chatHistory, setChatHistory] = useState([])

  // 表单
  const [policyForm] = Form.useForm()

  // 初始化数据
  useEffect(() => {
    if (activeTab === 'sessions') {
      loadSessions()
      loadChatStats()
    } else if (activeTab === 'policies') {
      loadPolicies()
      loadPolicyStats()
    }
  }, [activeTab, sessionsPage, sessionsPageSize, policiesPage, policiesPageSize, searchText, selectedStatus, selectedCategory, dateRange])

  // 加载聊天会话
  const loadSessions = async () => {
    setLoading(true)
    try {
      const response = await chatService.getChatSessions({
        page: sessionsPage,
        limit: sessionsPageSize,
        status: selectedStatus,
        startDate: dateRange[0]?.format('YYYY-MM-DD'),
        endDate: dateRange[1]?.format('YYYY-MM-DD')
      })

      if (response.success) {
        setSessions(response.data.sessions || [])
        setSessionsTotal(response.data.total || 0)
      }
    } catch (error) {
      message.error('加载聊天会话失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载聊天统计
  const loadChatStats = async () => {
    try {
      const response = await chatService.getChatStatistics()
      if (response.success) {
        setChatStats(response.data || {})
      }
    } catch (error) {
      console.error('加载聊天统计失败:', error)
    }
  }

  // 加载政策列表
  const loadPolicies = async () => {
    setLoading(true)
    try {
      const response = await chatService.getPolicies({
        page: policiesPage,
        limit: policiesPageSize,
        search: searchText,
        category: selectedCategory
      })

      if (response.success) {
        setPolicies(response.data.policies || [])
        setPoliciesTotal(response.data.total || 0)
      }
    } catch (error) {
      message.error('加载政策列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载政策统计
  const loadPolicyStats = async () => {
    try {
      const response = await chatService.getPolicyStatistics()
      if (response.success) {
        setPolicyStats(response.data || {})
      }
    } catch (error) {
      console.error('加载政策统计失败:', error)
    }
  }

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value)
    if (activeTab === 'policies') {
      setPoliciesPage(1)
    }
  }

  // 处理状态筛选
  const handleStatusChange = (value) => {
    setSelectedStatus(value)
    setSessionsPage(1)
  }

  // 处理分类筛选
  const handleCategoryChange = (value) => {
    setSelectedCategory(value)
    setPoliciesPage(1)
  }

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    setDateRange(dates || [])
    setSessionsPage(1)
  }

  // 查看会话详情
  const handleViewSessionDetail = async (session) => {
    setSelectedSession(session)
    setIsSessionDetailVisible(true)

    // 加载聊天历史
    try {
      const response = await chatService.getChatHistory(session.id)
      if (response.success) {
        setChatHistory(response.data.messages || [])
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error)
    }
  }

  // 打开政策模态框
  const handleOpenPolicyModal = (policy = null) => {
    setSelectedPolicy(policy)
    setIsPolicyModalVisible(true)
    if (policy) {
      policyForm.setFieldsValue(policy)
    } else {
      policyForm.resetFields()
    }
  }

  // 关闭模态框
  const handleCloseModals = () => {
    setIsSessionDetailVisible(false)
    setIsPolicyModalVisible(false)
    setSelectedSession(null)
    setSelectedPolicy(null)
    setChatHistory([])
    policyForm.resetFields()
  }

  // 保存政策
  const handleSavePolicy = async (values) => {
    try {
      if (selectedPolicy) {
        await chatService.updatePolicy(selectedPolicy.id, values)
        message.success('政策更新成功')
      } else {
        await chatService.createPolicy(values)
        message.success('政策创建成功')
      }
      handleCloseModals()
      loadPolicies()
      loadPolicyStats()
    } catch (error) {
      message.error(selectedPolicy ? '更新政策失败' : '创建政策失败')
    }
  }

  // 删除政策
  const handleDeletePolicy = async (id) => {
    try {
      await chatService.deletePolicy(id)
      message.success('政策删除成功')
      loadPolicies()
      loadPolicyStats()
    } catch (error) {
      message.error('删除政策失败')
    }
  }

  // 聊天会话表格列定义
  const sessionColumns = [
    {
      title: '会话ID',
      dataIndex: 'session_id',
      key: 'session_id',
      width: 150,
      render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span>
    },
    {
      title: '客户信息',
      dataIndex: 'customer_info',
      key: 'customer_info',
      width: 150,
      render: (customer) => (
        <div>
          <div>{customer?.name || '匿名用户'}</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            {customer?.ip_address}
          </div>
        </div>
      )
    },
    {
      title: '消息数量',
      dataIndex: 'message_count',
      key: 'message_count',
      width: 100,
      render: (count) => <Badge count={count} showZero />
    },
    {
      title: '会话状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusMap = {
          active: { color: 'green', text: '活跃' },
          ended: { color: 'default', text: '已结束' },
          escalated: { color: 'orange', text: '转人工' }
        }
        const config = statusMap[status] || { color: 'default', text: status }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      width: 100,
      render: (channel) => {
        const channelMap = {
          web_widget: '网页客服',
          mobile_app: '移动应用',
          wechat: '微信',
          api: 'API接口'
        }
        return channelMap[channel] || channel
      }
    },
    {
      title: '开始时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewSessionDetail(record)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ]

  // 政策表格列定义
  const policyColumns = [
    {
      title: '政策标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category) => <Tag color="blue">{category}</Tag>
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 200,
      render: (tags) => (
        <div>
          {tags?.slice(0, 3).map(tag => (
            <Tag key={tag} size="small">{tag}</Tag>
          ))}
          {tags?.length > 3 && <Tag size="small">+{tags.length - 3}</Tag>}
        </div>
      )
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
      width: 100,
      render: (count) => <Badge count={count} showZero />
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => handleOpenPolicyModal(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            onClick={() => handleDeletePolicy(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div className="fade-in">
      <div className="page-header">
        <Title level={2}>AI客服管理</Title>
        <p>管理AI客服对话、查看聊天记录和客服统计</p>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 聊天会话管理 */}
        <TabPane tab="聊天会话" key="sessions">
          {/* 统计卡片 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="总会话数"
                  value={chatStats.total_sessions || 0}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="活跃会话"
                  value={chatStats.active_sessions || 0}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="平均响应时间"
                  value={chatStats.avg_response_time || 0}
                  suffix="ms"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="满意度"
                  value={chatStats.satisfaction_rate || 0}
                  suffix="%"
                  precision={1}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
          </Row>

          <Card>
            <div style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} sm={12} md={8}>
                  <RangePicker
                    value={dateRange}
                    onChange={handleDateRangeChange}
                    style={{ width: '100%' }}
                    placeholder={['开始日期', '结束日期']}
                  />
                </Col>
                <Col xs={12} sm={6} md={4}>
                  <Select
                    placeholder="选择状态"
                    allowClear
                    value={selectedStatus}
                    onChange={handleStatusChange}
                    style={{ width: '100%' }}
                  >
                    <Option value="active">活跃</Option>
                    <Option value="ended">已结束</Option>
                    <Option value="escalated">转人工</Option>
                  </Select>
                </Col>
                <Col xs={12} sm={6} md={4}>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={loadSessions}
                  >
                    刷新
                  </Button>
                </Col>
              </Row>
            </div>

            <Divider />

            <Table
              columns={sessionColumns}
              dataSource={sessions}
              rowKey="id"
              loading={loading}
              pagination={{
                current: sessionsPage,
                pageSize: sessionsPageSize,
                total: sessionsTotal,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, size) => {
                  setSessionsPage(page)
                  setSessionsPageSize(size)
                }
              }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </TabPane>

        {/* 政策管理 */}
        <TabPane tab="政策管理" key="policies">
          {/* 统计卡片 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="总政策数"
                  value={policyStats.total || 0}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="启用政策"
                  value={policyStats.active || 0}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="政策分类"
                  value={policyStats.categories || 0}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="总使用次数"
                  value={policyStats.total_usage || 0}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
          </Row>

          <Card>
            <div style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} sm={12} md={8}>
                  <Search
                    placeholder="搜索政策标题、内容"
                    allowClear
                    onSearch={handleSearch}
                    style={{ width: '100%' }}
                  />
                </Col>
                <Col xs={12} sm={6} md={4}>
                  <Select
                    placeholder="选择分类"
                    allowClear
                    value={selectedCategory}
                    onChange={handleCategoryChange}
                    style={{ width: '100%' }}
                  >
                    <Option value="使命">使命</Option>
                    <Option value="群规">群规</Option>
                    <Option value="质量">质量</Option>
                    <Option value="配送">配送</Option>
                    <Option value="付款">付款</Option>
                    <Option value="取货">取货</Option>
                    <Option value="售后">售后</Option>
                    <Option value="社区">社区</Option>
                  </Select>
                </Col>
                <Col xs={12} sm={12} md={12}>
                  <Space wrap>
                    <Button
                      type="primary"
                      onClick={() => handleOpenPolicyModal()}
                    >
                      新增政策
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={loadPolicies}
                    >
                      刷新
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            <Divider />

            <Table
              columns={policyColumns}
              dataSource={policies}
              rowKey="id"
              loading={loading}
              pagination={{
                current: policiesPage,
                pageSize: policiesPageSize,
                total: policiesTotal,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, size) => {
                  setPoliciesPage(page)
                  setPoliciesPageSize(size)
                }
              }}
              scroll={{ x: 1200 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 会话详情模态框 */}
      <Modal
        title="聊天会话详情"
        open={isSessionDetailVisible}
        onCancel={handleCloseModals}
        footer={[
          <Button key="close" onClick={handleCloseModals}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedSession && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <div><strong>会话ID:</strong> {selectedSession.session_id}</div>
                  <div><strong>客户:</strong> {selectedSession.customer_info?.name || '匿名用户'}</div>
                </Col>
                <Col span={12}>
                  <div><strong>状态:</strong>
                    <Tag color={selectedSession.status === 'active' ? 'green' : 'default'} style={{ marginLeft: 8 }}>
                      {selectedSession.status === 'active' ? '活跃' : '已结束'}
                    </Tag>
                  </div>
                  <div><strong>开始时间:</strong> {dayjs(selectedSession.created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
                </Col>
              </Row>
            </Card>

            <div style={{ height: 400, overflowY: 'auto', border: '1px solid #f0f0f0', borderRadius: 4, padding: 16 }}>
              <List
                dataSource={chatHistory}
                renderItem={(message) => (
                  <List.Item style={{ border: 'none', padding: '8px 0' }}>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          icon={message.sender === 'user' ? <UserOutlined /> : <RobotOutlined />}
                          style={{
                            backgroundColor: message.sender === 'user' ? '#1890ff' : '#52c41a'
                          }}
                        />
                      }
                      title={
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span>{message.sender === 'user' ? '用户' : 'AI助手'}</span>
                          <span style={{ fontSize: 12, color: '#999' }}>
                            {dayjs(message.timestamp).format('HH:mm:ss')}
                          </span>
                        </div>
                      }
                      description={
                        <div style={{
                          background: message.sender === 'user' ? '#e6f7ff' : '#f6ffed',
                          padding: 8,
                          borderRadius: 4,
                          marginTop: 4
                        }}>
                          {message.content}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          </div>
        )}
      </Modal>

      {/* 政策编辑模态框 */}
      <Modal
        title={selectedPolicy ? '编辑政策' : '新增政策'}
        open={isPolicyModalVisible}
        onCancel={handleCloseModals}
        footer={null}
        width={600}
      >
        <Form
          form={policyForm}
          layout="vertical"
          onFinish={handleSavePolicy}
        >
          <Form.Item
            name="title"
            label="政策标题"
            rules={[{ required: true, message: '请输入政策标题' }]}
          >
            <Input placeholder="请输入政策标题" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="政策分类"
                rules={[{ required: true, message: '请选择政策分类' }]}
              >
                <Select placeholder="请选择政策分类">
                  <Option value="使命">使命</Option>
                  <Option value="群规">群规</Option>
                  <Option value="质量">质量</Option>
                  <Option value="配送">配送</Option>
                  <Option value="付款">付款</Option>
                  <Option value="取货">取货</Option>
                  <Option value="售后">售后</Option>
                  <Option value="社区">社区</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                initialValue="active"
              >
                <Select>
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="content"
            label="政策内容"
            rules={[{ required: true, message: '请输入政策内容' }]}
          >
            <TextArea rows={6} placeholder="请输入政策内容" />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="请输入标签，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {selectedPolicy ? '更新' : '创建'}
              </Button>
              <Button onClick={handleCloseModals}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Chat
