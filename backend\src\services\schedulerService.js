/**
 * 定时任务服务
 * 负责管理和执行定时任务，如自动生成报表
 */

const cron = require('node-cron');
const reportService = require('./reportService');
const { Report, User } = require('../models');
const logger = require('../utils/logger');

class SchedulerService {
  constructor() {
    this.tasks = new Map();
    this.isInitialized = false;
  }

  /**
   * 初始化定时任务服务
   */
  async initialize() {
    if (this.isInitialized) {
      logger.warn('定时任务服务已经初始化');
      return;
    }

    try {
      logger.info('初始化定时任务服务');

      // 启动默认的定时任务
      await this.startDefaultTasks();

      // 恢复已保存的定时任务
      await this.restoreScheduledTasks();

      this.isInitialized = true;
      logger.info('定时任务服务初始化完成');

    } catch (error) {
      logger.error('定时任务服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 启动默认定时任务
   */
  async startDefaultTasks() {
    try {
      // 每周一早上8点生成周报
      this.scheduleTask('weekly_report', '0 8 * * 1', async () => {
        await this.generateWeeklyReport();
      });

      // 每月1号早上8点生成月报
      this.scheduleTask('monthly_report', '0 8 1 * *', async () => {
        await this.generateMonthlyReport();
      });

      // 每天凌晨2点清理过期报表
      this.scheduleTask('cleanup_reports', '0 2 * * *', async () => {
        await this.cleanupExpiredReports();
      });

      logger.info('默认定时任务启动完成');

    } catch (error) {
      logger.error('启动默认定时任务失败:', error);
      throw error;
    }
  }

  /**
   * 恢复已保存的定时任务
   */
  async restoreScheduledTasks() {
    try {
      // 查找所有已设置定时的报表
      const scheduledReports = await Report.find({
        'schedule.is_scheduled': true,
        'schedule.next_run': { $gte: new Date() }
      });

      for (const report of scheduledReports) {
        await this.scheduleReportGeneration(report);
      }

      logger.info(`恢复了 ${scheduledReports.length} 个定时报表任务`);

    } catch (error) {
      logger.error('恢复定时任务失败:', error);
      throw error;
    }
  }

  /**
   * 调度任务
   * @param {string} taskId - 任务ID
   * @param {string} cronExpression - Cron表达式
   * @param {Function} taskFunction - 任务函数
   */
  scheduleTask(taskId, cronExpression, taskFunction) {
    try {
      // 如果任务已存在，先停止
      if (this.tasks.has(taskId)) {
        this.stopTask(taskId);
      }

      // 创建新任务
      const task = cron.schedule(cronExpression, async () => {
        try {
          logger.info(`执行定时任务: ${taskId}`);
          await taskFunction();
          logger.info(`定时任务执行完成: ${taskId}`);
        } catch (error) {
          logger.error(`定时任务执行失败: ${taskId}`, error);
        }
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      });

      this.tasks.set(taskId, {
        task,
        cronExpression,
        createdAt: new Date()
      });

      task.start();

      logger.info(`定时任务已调度: ${taskId} (${cronExpression})`);

    } catch (error) {
      logger.error(`调度任务失败: ${taskId}`, error);
      throw error;
    }
  }

  /**
   * 停止任务
   * @param {string} taskId - 任务ID
   */
  stopTask(taskId) {
    try {
      const taskInfo = this.tasks.get(taskId);
      if (taskInfo) {
        taskInfo.task.stop();
        this.tasks.delete(taskId);
        logger.info(`定时任务已停止: ${taskId}`);
      }
    } catch (error) {
      logger.error(`停止任务失败: ${taskId}`, error);
    }
  }

  /**
   * 调度报表生成
   * @param {Object} report - 报表对象
   */
  async scheduleReportGeneration(report) {
    try {
      if (!report.schedule.is_scheduled || !report.schedule.next_run) {
        return;
      }

      const taskId = `report_${report._id}`;
      const nextRun = new Date(report.schedule.next_run);
      const now = new Date();

      if (nextRun <= now) {
        // 如果下次运行时间已过，立即执行
        await this.executeReportGeneration(report);
        return;
      }

      // 计算延迟时间
      const delay = nextRun.getTime() - now.getTime();

      // 使用setTimeout而不是cron，因为这是一次性任务
      const timeoutId = setTimeout(async () => {
        await this.executeReportGeneration(report);
      }, delay);

      this.tasks.set(taskId, {
        timeoutId,
        nextRun,
        reportId: report._id
      });

      logger.info(`报表定时任务已调度: ${report.report_id} (${nextRun.toISOString()})`);

    } catch (error) {
      logger.error('调度报表生成失败:', error);
    }
  }

  /**
   * 执行报表生成
   * @param {Object} report - 报表对象
   */
  async executeReportGeneration(report) {
    try {
      logger.info(`开始执行定时报表生成: ${report.report_id}`);

      // 计算报表时间范围
      const { startDate, endDate } = this.calculateReportPeriod(report.report_type);

      // 生成报表
      const result = await reportService.generateWeeklyReport({
        startDate,
        endDate,
        generatedBy: report.generated_by,
        filters: report.filters || {}
      });

      if (result.success) {
        logger.info(`定时报表生成成功: ${result.data.report.report_id}`);

        // 更新下次运行时间
        report.calculateNextRun();
        await report.save();

        // 重新调度下次任务
        await this.scheduleReportGeneration(report);
      } else {
        logger.error(`定时报表生成失败: ${report.report_id}`, result.error);
      }

    } catch (error) {
      logger.error('执行报表生成失败:', error);
    }
  }

  /**
   * 生成周报
   */
  async generateWeeklyReport() {
    try {
      logger.info('开始生成自动周报');

      // 获取系统管理员用户
      const adminUser = await User.findOne({ role: 'admin' });
      if (!adminUser) {
        logger.error('未找到管理员用户，无法生成自动报表');
        return;
      }

      // 计算上周的时间范围
      const { startDate, endDate } = this.calculateReportPeriod('weekly');

      const result = await reportService.generateWeeklyReport({
        startDate,
        endDate,
        generatedBy: adminUser._id,
        filters: {}
      });

      if (result.success) {
        logger.info(`自动周报生成成功: ${result.data.report.report_id}`);
      } else {
        logger.error('自动周报生成失败:', result.error);
      }

    } catch (error) {
      logger.error('生成自动周报失败:', error);
    }
  }

  /**
   * 生成月报
   */
  async generateMonthlyReport() {
    try {
      logger.info('开始生成自动月报');

      // 获取系统管理员用户
      const adminUser = await User.findOne({ role: 'admin' });
      if (!adminUser) {
        logger.error('未找到管理员用户，无法生成自动报表');
        return;
      }

      // 计算上月的时间范围
      const { startDate, endDate } = this.calculateReportPeriod('monthly');

      const result = await reportService.generateMonthlyReport({
        startDate,
        endDate,
        generatedBy: adminUser._id,
        filters: {}
      });

      if (result.success) {
        logger.info(`自动月报生成成功: ${result.data.report.report_id}`);
      } else {
        logger.error('自动月报生成失败:', result.error);
      }

    } catch (error) {
      logger.error('生成自动月报失败:', error);
    }
  }

  /**
   * 清理过期报表
   */
  async cleanupExpiredReports() {
    try {
      logger.info('开始清理过期报表');

      // 删除30天前的报表（保留状态为archived的除外）
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const result = await Report.deleteMany({
        generated_at: { $lt: thirtyDaysAgo },
        status: { $ne: 'archived' }
      });

      logger.info(`清理了 ${result.deletedCount} 个过期报表`);

    } catch (error) {
      logger.error('清理过期报表失败:', error);
    }
  }

  /**
   * 计算报表时间范围
   * @param {string} reportType - 报表类型
   * @returns {Object} 时间范围
   */
  calculateReportPeriod(reportType) {
    const now = new Date();
    let startDate, endDate;

    switch (reportType) {
      case 'weekly':
        // 上周一到周日
        const lastWeekStart = new Date(now);
        lastWeekStart.setDate(now.getDate() - now.getDay() - 6);
        lastWeekStart.setHours(0, 0, 0, 0);

        const lastWeekEnd = new Date(lastWeekStart);
        lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
        lastWeekEnd.setHours(23, 59, 59, 999);

        startDate = lastWeekStart;
        endDate = lastWeekEnd;
        break;

      case 'monthly':
        // 上个月1号到最后一天
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        lastMonthEnd.setHours(23, 59, 59, 999);

        startDate = lastMonthStart;
        endDate = lastMonthEnd;
        break;

      default:
        throw new Error(`不支持的报表类型: ${reportType}`);
    }

    return { startDate, endDate };
  }

  /**
   * 获取任务状态
   */
  getTaskStatus() {
    const status = {
      isInitialized: this.isInitialized,
      totalTasks: this.tasks.size,
      tasks: []
    };

    this.tasks.forEach((taskInfo, taskId) => {
      status.tasks.push({
        id: taskId,
        cronExpression: taskInfo.cronExpression,
        createdAt: taskInfo.createdAt,
        nextRun: taskInfo.nextRun,
        reportId: taskInfo.reportId
      });
    });

    return status;
  }

  /**
   * 停止所有任务
   */
  stopAllTasks() {
    try {
      this.tasks.forEach((taskInfo, taskId) => {
        if (taskInfo.task) {
          taskInfo.task.stop();
        }
        if (taskInfo.timeoutId) {
          clearTimeout(taskInfo.timeoutId);
        }
      });

      this.tasks.clear();
      this.isInitialized = false;

      logger.info('所有定时任务已停止');

    } catch (error) {
      logger.error('停止所有任务失败:', error);
    }
  }
}

module.exports = new SchedulerService();
