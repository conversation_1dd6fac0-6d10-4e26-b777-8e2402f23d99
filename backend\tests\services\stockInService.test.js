// Mock数据库模型
const mockStockInRecordInstance = {
  _id: '507f1f77bcf86cd799439012',
  record_number: 'IN202406150001',
  supplier: '测试供应商',
  total_quantity: 10,
  total_amount: 155,
  status: 'pending',
  created_at: new Date(),
  save: jest.fn().mockResolvedValue()
};

const mockStockInRecordConstructor = jest.fn().mockImplementation(() => mockStockInRecordInstance);
mockStockInRecordConstructor.findById = jest.fn();
mockStockInRecordConstructor.findByIdAndUpdate = jest.fn();
mockStockInRecordConstructor.aggregate = jest.fn();

jest.mock('../../src/models', () => ({
  Product: {
    findById: jest.fn(),
    findOne: jest.fn()
  },
  Inventory: {
    findOne: jest.fn()
  },
  StockInRecord: mockStockInRecordConstructor,
  OperationLog: {
    logSuccess: jest.fn()
  }
}));

const stockInService = require('../../src/services/stockInService');
const { Product, Inventory, StockInRecord, OperationLog } = require('../../src/models');

describe('StockInService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateStockInItem', () => {
    it('应该验证有效的入库商品信息', async () => {
      const mockProduct = {
        _id: '507f1f77bcf86cd799439011',
        name: '测试商品',
        barcode: '1234567890123',
        is_active: true
      };

      Product.findById.mockResolvedValue(mockProduct);

      // 设置一个未来的日期
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      const item = {
        product_id: '507f1f77bcf86cd799439011',
        quantity: 10,
        unit_cost: 15.5,
        expiry_date: futureDate.toISOString(),
        batch_number: 'BATCH001',
        location: 'A-01'
      };

      const result = await stockInService.validateStockInItem(item);

      expect(result).toMatchObject({
        product_id: mockProduct._id,
        product_name: mockProduct.name,
        quantity: 10,
        unit_cost: 15.5,
        batch_number: 'BATCH001',
        location: 'A-01'
      });
      expect(result.expiry_date).toBeInstanceOf(Date);
    });

    it('应该通过条码查找商品', async () => {
      const mockProduct = {
        _id: '507f1f77bcf86cd799439011',
        name: '测试商品',
        barcode: '1234567890123',
        is_active: true
      };

      Product.findOne.mockResolvedValue(mockProduct);

      const item = {
        barcode: '1234567890123',
        quantity: 5,
        unit_cost: 20.0
      };

      const result = await stockInService.validateStockInItem(item);

      expect(result.product_id).toBe(mockProduct._id);
      expect(Product.findOne).toHaveBeenCalledWith({
        barcode: '1234567890123',
        is_active: true
      });
    });

    it('应该拒绝无效的数量', async () => {
      const item = {
        product_id: '507f1f77bcf86cd799439011',
        quantity: 0,
        unit_cost: 15.5
      };

      await expect(stockInService.validateStockInItem(item))
        .rejects.toThrow('商品数量必须大于0');
    });

    it('应该拒绝负数单价', async () => {
      const item = {
        product_id: '507f1f77bcf86cd799439011',
        quantity: 10,
        unit_cost: -5
      };

      await expect(stockInService.validateStockInItem(item))
        .rejects.toThrow('商品单价不能为负数');
    });

    it('应该拒绝过期日期早于当前日期', async () => {
      const mockProduct = {
        _id: '507f1f77bcf86cd799439011',
        name: '测试商品',
        is_active: true
      };

      Product.findById.mockResolvedValue(mockProduct);

      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      const item = {
        product_id: '507f1f77bcf86cd799439011',
        quantity: 10,
        unit_cost: 15.5,
        expiry_date: pastDate.toISOString()
      };

      await expect(stockInService.validateStockInItem(item))
        .rejects.toThrow('过期日期不能早于当前日期');
    });

    it('应该拒绝不存在的商品', async () => {
      Product.findById.mockResolvedValue(null);

      const item = {
        product_id: '507f1f77bcf86cd799439011',
        quantity: 10,
        unit_cost: 15.5
      };

      await expect(stockInService.validateStockInItem(item))
        .rejects.toThrow('商品不存在或已停用');
    });

    it('应该要求提供商品ID或条码', async () => {
      const item = {
        quantity: 10,
        unit_cost: 15.5
      };

      await expect(stockInService.validateStockInItem(item))
        .rejects.toThrow('必须提供商品ID或条码');
    });
  });

  describe('createSingleStockIn', () => {
    it('应该创建单个入库记录', async () => {
      const mockProduct = {
        _id: '507f1f77bcf86cd799439011',
        name: '测试商品',
        is_active: true
      };

      Product.findById.mockResolvedValue(mockProduct);

      const stockInData = {
        supplier: '测试供应商',
        supplier_invoice: 'INV001',
        items: [
          {
            product_id: '507f1f77bcf86cd799439011',
            quantity: 10,
            unit_cost: 15.5
          }
        ],
        warehouse_location: '仓库A',
        notes: '测试入库'
      };

      const result = await stockInService.createSingleStockIn(stockInData, 'user123');

      expect(result.success).toBe(true);
      expect(result.data.stockInRecord).toMatchObject({
        id: mockStockInRecordInstance._id,
        record_number: mockStockInRecordInstance.record_number,
        supplier: mockStockInRecordInstance.supplier,
        total_quantity: 10,
        total_amount: 155,
        status: 'pending'
      });
    });

    it('应该拒绝空的商品列表', async () => {
      const stockInData = {
        supplier: '测试供应商',
        items: []
      };

      const result = await stockInService.createSingleStockIn(stockInData, 'user123');

      expect(result.success).toBe(false);
      expect(result.error).toBe('供应商和商品信息是必需的');
    });

    it('应该拒绝缺少供应商信息', async () => {
      const stockInData = {
        items: [
          {
            product_id: '507f1f77bcf86cd799439011',
            quantity: 10,
            unit_cost: 15.5
          }
        ]
      };

      const result = await stockInService.createSingleStockIn(stockInData, 'user123');

      expect(result.success).toBe(false);
      expect(result.error).toBe('供应商和商品信息是必需的');
    });
  });

  describe('createBatchStockIn', () => {
    it('应该创建批量入库记录', async () => {
      const mockProduct = {
        _id: '507f1f77bcf86cd799439011',
        name: '测试商品',
        is_active: true
      };

      Product.findById.mockResolvedValue(mockProduct);

      const stockInData = [
        {
          supplier: '供应商1',
          items: [
            {
              product_id: '507f1f77bcf86cd799439011',
              quantity: 10,
              unit_cost: 15.5
            }
          ]
        },
        {
          supplier: '供应商2',
          items: [
            {
              product_id: '507f1f77bcf86cd799439011',
              quantity: 5,
              unit_cost: 20.0
            }
          ]
        }
      ];

      const result = await stockInService.createBatchStockIn(stockInData, 'user123');

      expect(result.success).toBe(true);
      expect(result.data.summary.total).toBe(2);
      expect(result.data.summary.success).toBe(2);
      expect(result.data.summary.failed).toBe(0);
    });

    it('应该处理部分失败的情况', async () => {
      // 第一个商品存在，第二个不存在
      Product.findById
        .mockResolvedValueOnce({
          _id: '507f1f77bcf86cd799439011',
          name: '测试商品1',
          is_active: true
        })
        .mockResolvedValueOnce(null);



      const stockInData = [
        {
          supplier: '供应商1',
          items: [
            {
              product_id: '507f1f77bcf86cd799439011',
              quantity: 10,
              unit_cost: 15.5
            }
          ]
        },
        {
          supplier: '供应商2',
          items: [
            {
              product_id: '507f1f77bcf86cd799439999', // 不存在的商品
              quantity: 5,
              unit_cost: 20.0
            }
          ]
        }
      ];

      const result = await stockInService.createBatchStockIn(stockInData, 'user123');

      expect(result.success).toBe(false);
      expect(result.data.summary.total).toBe(2);
      expect(result.data.summary.success).toBe(1);
      expect(result.data.summary.failed).toBe(1);
      expect(result.data.errors).toHaveLength(1);
    });

    it('应该拒绝空的入库数据', async () => {
      const result = await stockInService.createBatchStockIn([], 'user123');

      expect(result.success).toBe(false);
      expect(result.error).toBe('入库数据不能为空');
    });

    it('应该拒绝超过最大批量大小的数据', async () => {
      const largeStockInData = new Array(101).fill({
        supplier: '测试供应商',
        items: [
          {
            product_id: '507f1f77bcf86cd799439011',
            quantity: 10,
            unit_cost: 15.5
          }
        ]
      });

      const result = await stockInService.createBatchStockIn(largeStockInData, 'user123');

      expect(result.success).toBe(false);
      expect(result.error).toBe('批量入库数量不能超过 100 条');
    });
  });

  describe('getStockInStatistics', () => {
    it('应该返回入库统计信息', async () => {
      const mockStatistics = [
        {
          totalRecords: 10,
          totalQuantity: 100,
          totalAmount: 1500,
          avgAmount: 150
        }
      ];

      const mockStatusStats = [
        { _id: 'pending', count: 3 },
        { _id: 'completed', count: 6 },
        { _id: 'cancelled', count: 1 }
      ];

      StockInRecord.aggregate
        .mockResolvedValueOnce(mockStatistics)
        .mockResolvedValueOnce(mockStatusStats);

      const result = await stockInService.getStockInStatistics();

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        totalRecords: 10,
        totalQuantity: 100,
        totalAmount: 1500,
        avgAmount: 150,
        statusCounts: {
          pending: 3,
          completed: 6,
          cancelled: 1
        }
      });
    });

    it('应该处理空统计结果', async () => {
      StockInRecord.aggregate
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([]);

      const result = await stockInService.getStockInStatistics();

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        totalRecords: 0,
        totalQuantity: 0,
        totalAmount: 0,
        avgAmount: 0,
        statusCounts: {}
      });
    });

    it('应该处理数据库错误', async () => {
      StockInRecord.aggregate.mockRejectedValue(new Error('Database error'));

      const result = await stockInService.getStockInStatistics();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database error');
    });
  });
});
