const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  message_id: {
    type: String,
    required: true,
    unique: true
  },
  sender: {
    type: String,
    enum: ['user', 'ai', 'agent'],
    required: [true, '发送者类型是必需的']
  },
  content: {
    type: String,
    required: [true, '消息内容是必需的'],
    trim: true,
    maxlength: [2000, '消息内容不能超过2000个字符']
  },
  message_type: {
    type: String,
    enum: ['text', 'image', 'file', 'system', 'action'],
    default: 'text'
  },
  metadata: {
    intent: {
      type: String,
      enum: ['greeting', 'inventory_query', 'return_request', 'complaint', 'general_question', 'other']
    },
    confidence: {
      type: Number,
      min: 0,
      max: 1
    },
    entities: [{
      type: {
        type: String,
        enum: ['product', 'quantity', 'date', 'phone', 'email', 'location']
      },
      value: String,
      confidence: {
        type: Number,
        min: 0,
        max: 1
      }
    }],
    action_taken: {
      type: String,
      enum: ['inventory_search', 'return_created', 'escalated', 'resolved']
    },
    response_time_ms: {
      type: Number,
      min: 0
    }
  },
  attachments: [{
    filename: String,
    url: String,
    file_type: {
      type: String,
      enum: ['image', 'document', 'other']
    },
    file_size: Number
  }],
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  edited: {
    type: Boolean,
    default: false
  },
  edited_at: {
    type: Date
  }
}, { _id: false });

const chatSessionSchema = new mongoose.Schema({
  session_id: {
    type: String,
    unique: true,
    index: true
  },
  customer_info: {
    name: {
      type: String,
      trim: true,
      maxlength: [50, '客户姓名不能超过50个字符']
    },
    phone: {
      type: String,
      trim: true,
      match: [/^1[3-9]\d{9}$/, '请输入有效的手机号码'],
      index: true
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
    },
    ip_address: {
      type: String,
      trim: true
    },
    user_agent: {
      type: String,
      trim: true
    }
  },
  messages: {
    type: [messageSchema],
    default: []
  },
  status: {
    type: String,
    enum: ['active', 'waiting', 'escalated', 'resolved', 'closed'],
    default: 'active',
    index: true
  },
  channel: {
    type: String,
    enum: ['web_widget', 'mobile_app', 'wechat', 'phone', 'email'],
    default: 'web_widget'
  },
  language: {
    type: String,
    enum: ['zh-CN', 'en-US'],
    default: 'zh-CN'
  },
  context: {
    current_topic: {
      type: String,
      enum: ['inventory', 'return', 'complaint', 'general', 'other']
    },
    products_mentioned: [{
      product_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product'
      },
      product_name: String,
      mentioned_at: {
        type: Date,
        default: Date.now
      }
    }],
    return_request_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ReturnRequest'
    },
    escalation_reason: {
      type: String,
      trim: true,
      maxlength: [500, '升级原因不能超过500个字符']
    }
  },
  ai_config: {
    model_version: {
      type: String,
      default: 'deepseek-v3'
    },
    temperature: {
      type: Number,
      min: 0,
      max: 2,
      default: 0.7
    },
    max_tokens: {
      type: Number,
      min: 1,
      max: 4000,
      default: 1000
    },
    system_prompt: {
      type: String,
      default: '你是一个专业的客服AI助手，专门处理库存查询和退货申请。'
    }
  },
  metrics: {
    total_messages: {
      type: Number,
      default: 0
    },
    user_messages: {
      type: Number,
      default: 0
    },
    ai_messages: {
      type: Number,
      default: 0
    },
    avg_response_time: {
      type: Number,
      default: 0
    },
    satisfaction_score: {
      type: Number,
      min: 1,
      max: 5
    },
    resolution_time: {
      type: Number // 毫秒
    }
  },
  assigned_agent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  escalated_at: {
    type: Date,
    index: true
  },
  resolved_at: {
    type: Date,
    index: true
  },
  closed_at: {
    type: Date,
    index: true
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, '标签不能超过30个字符']
  }],
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, '备注不能超过1000个字符']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 生成会话ID
chatSessionSchema.statics.generateSessionId = function() {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `chat_${timestamp}_${random}`;
};

// 添加消息
chatSessionSchema.methods.addMessage = function(sender, content, messageType = 'text', metadata = {}) {
  const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
  
  const message = {
    message_id: messageId,
    sender: sender,
    content: content,
    message_type: messageType,
    metadata: metadata,
    timestamp: new Date()
  };
  
  this.messages.push(message);
  this.updateMetrics();
  
  return message;
};

// 更新统计指标
chatSessionSchema.methods.updateMetrics = function() {
  this.metrics.total_messages = this.messages.length;
  this.metrics.user_messages = this.messages.filter(m => m.sender === 'user').length;
  this.metrics.ai_messages = this.messages.filter(m => m.sender === 'ai').length;
  
  // 计算平均响应时间
  const responseTimes = this.messages
    .filter(m => m.metadata && m.metadata.response_time_ms)
    .map(m => m.metadata.response_time_ms);
  
  if (responseTimes.length > 0) {
    this.metrics.avg_response_time = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
  }
  
  return this.metrics;
};

// 升级到人工客服
chatSessionSchema.methods.escalateToAgent = function(reason, agentId) {
  this.status = 'escalated';
  this.context.escalation_reason = reason;
  this.assigned_agent = agentId;
  this.escalated_at = new Date();
  
  // 添加系统消息
  this.addMessage('system', '会话已转接至人工客服', 'system', {
    action_taken: 'escalated'
  });
  
  return this;
};

// 解决会话
chatSessionSchema.methods.resolve = function(resolutionNotes) {
  this.status = 'resolved';
  this.resolved_at = new Date();
  
  if (resolutionNotes) {
    this.notes = (this.notes || '') + `\n解决方案: ${resolutionNotes}`;
  }
  
  // 计算解决时间
  this.metrics.resolution_time = this.resolved_at - this.createdAt;
  
  // 添加系统消息
  this.addMessage('system', '问题已解决', 'system', {
    action_taken: 'resolved'
  });
  
  return this;
};

// 关闭会话
chatSessionSchema.methods.close = function() {
  this.status = 'closed';
  this.closed_at = new Date();
  
  return this;
};

// 设置满意度评分
chatSessionSchema.methods.setSatisfactionScore = function(score) {
  if (score < 1 || score > 5) {
    throw new Error('满意度评分必须在1-5之间');
  }
  
  this.metrics.satisfaction_score = score;
  return this;
};

// 获取最近的消息
chatSessionSchema.methods.getRecentMessages = function(limit = 10) {
  return this.messages.slice(-limit);
};

// 搜索消息
chatSessionSchema.methods.searchMessages = function(keyword) {
  return this.messages.filter(message => 
    message.content.toLowerCase().includes(keyword.toLowerCase())
  );
};

// 复合索引
chatSessionSchema.index({ status: 1, createdAt: -1 });
chatSessionSchema.index({ assigned_agent: 1, status: 1 });
chatSessionSchema.index({ escalated_at: -1 });
chatSessionSchema.index({ resolved_at: -1 });
chatSessionSchema.index({ 'messages.timestamp': -1 });

// 中间件：保存前生成会话ID
chatSessionSchema.pre('save', function(next) {
  if (this.isNew && !this.session_id) {
    this.session_id = this.constructor.generateSessionId();
  }

  // 更新统计指标
  if (this.messages && this.messages.length > 0) {
    this.updateMetrics();
  }

  next();
});

module.exports = mongoose.model('ChatSession', chatSessionSchema);
