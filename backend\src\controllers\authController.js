const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { User, OperationLog } = require('../models');
const logger = require('../utils/logger');

/**
 * 认证控制器
 * 处理用户登录、注册、密码重置等认证相关操作
 */
class AuthController {
  /**
   * 用户登录
   */
  async login(req, res) {
    const startTime = Date.now();
    
    try {
      const { username, password } = req.body;
      
      // 验证输入
      if (!username || !password) {
        await OperationLog.logFailure(
          null,
          'auth.login',
          'user',
          new Error('用户名和密码是必需的'),
          {
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 400
          }
        );
        
        return res.status(400).json({
          success: false,
          message: '用户名和密码是必需的'
        });
      }
      
      // 查找用户
      const user = await User.findOne({ 
        $or: [
          { username: username },
          { email: username }
        ]
      });
      
      if (!user) {
        await OperationLog.logFailure(
          null,
          'auth.login',
          'user',
          new Error('用户不存在'),
          {
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 401,
            description: `登录失败: 用户名 ${username} 不存在`
          }
        );
        
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }
      
      // 检查用户是否激活
      if (!user.isActive) {
        await OperationLog.logFailure(
          user._id,
          'auth.login',
          'user',
          new Error('用户账户已被禁用'),
          {
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 401,
            severity: 'medium'
          }
        );
        
        return res.status(401).json({
          success: false,
          message: '账户已被禁用，请联系管理员'
        });
      }
      
      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      
      if (!isPasswordValid) {
        await OperationLog.logFailure(
          user._id,
          'auth.login',
          'user',
          new Error('密码错误'),
          {
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 401,
            severity: 'medium',
            description: `登录失败: 用户 ${username} 密码错误`
          }
        );
        
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }
      
      // 生成JWT token
      const token = jwt.sign(
        { 
          userId: user._id,
          username: user.username,
          role: user.role
        },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );
      
      // 更新最后登录时间
      user.lastLogin = new Date();
      await user.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录成功登录
      await OperationLog.logSuccess(
        user._id,
        'auth.login',
        'user',
        user._id.toString(),
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `用户 ${username} 登录成功`
        }
      );
      
      logger.info(`用户登录成功: ${username} (${req.ip})`);
      
      res.json({
        success: true,
        message: '登录成功',
        data: {
          token,
          user: {
            id: user._id,
            username: user.username,
            name: user.name,
            email: user.email,
            role: user.role,
            permissions: user.permissions,
            avatar: user.avatar,
            lastLogin: user.lastLogin
          }
        }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        null,
        'auth.login',
        'user',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'high'
        }
      );
      
      logger.error('登录错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 用户注册
   */
  async register(req, res) {
    const startTime = Date.now();
    
    try {
      const { username, email, password, name, role = 'staff' } = req.body;
      
      // 验证输入
      if (!username || !email || !password || !name) {
        return res.status(400).json({
          success: false,
          message: '所有字段都是必需的'
        });
      }
      
      // 检查用户是否已存在
      const existingUser = await User.findOne({
        $or: [
          { username: username },
          { email: email }
        ]
      });
      
      if (existingUser) {
        await OperationLog.logFailure(
          null,
          'auth.register',
          'user',
          new Error('用户已存在'),
          {
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 409,
            description: `注册失败: 用户名 ${username} 或邮箱 ${email} 已存在`
          }
        );
        
        return res.status(409).json({
          success: false,
          message: '用户名或邮箱已存在'
        });
      }
      
      // 创建新用户
      const user = new User({
        username,
        email,
        password,
        name,
        role
      });
      
      // 设置默认权限
      user.setDefaultPermissions();
      
      await user.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录成功注册
      await OperationLog.logSuccess(
        user._id,
        'auth.register',
        'user',
        user._id.toString(),
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          after_data: {
            username: user.username,
            email: user.email,
            role: user.role
          },
          description: `新用户注册: ${username}`
        }
      );
      
      logger.info(`新用户注册: ${username} (${email})`);
      
      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          user: {
            id: user._id,
            username: user.username,
            name: user.name,
            email: user.email,
            role: user.role,
            permissions: user.permissions
          }
        }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        null,
        'auth.register',
        'user',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'high'
        }
      );
      
      logger.error('注册错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取当前用户信息
   */
  async getCurrentUser(req, res) {
    try {
      const user = await User.findById(req.user.userId).select('-password');
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }
      
      res.json({
        success: true,
        data: { user }
      });
      
    } catch (error) {
      logger.error('获取用户信息错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 修改密码
   */
  async changePassword(req, res) {
    const startTime = Date.now();
    
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user.userId;
      
      // 验证输入
      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          message: '当前密码和新密码都是必需的'
        });
      }
      
      // 查找用户
      const user = await User.findById(userId);
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }
      
      // 验证当前密码
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      
      if (!isCurrentPasswordValid) {
        await OperationLog.logFailure(
          userId,
          'auth.password_change',
          'user',
          new Error('当前密码错误'),
          {
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 401,
            severity: 'medium'
          }
        );
        
        return res.status(401).json({
          success: false,
          message: '当前密码错误'
        });
      }
      
      // 更新密码
      user.password = newPassword;
      await user.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录密码修改
      await OperationLog.logSuccess(
        userId,
        'auth.password_change',
        'user',
        userId,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: '用户密码已修改'
        }
      );
      
      logger.info(`用户密码修改: ${user.username} (${req.ip})`);
      
      res.json({
        success: true,
        message: '密码修改成功'
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'auth.password_change',
        'user',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'high'
        }
      );
      
      logger.error('修改密码错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 用户登出
   */
  async logout(req, res) {
    try {
      const userId = req.user.userId;
      
      // 记录登出操作
      await OperationLog.logSuccess(
        userId,
        'auth.logout',
        'user',
        userId,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          description: '用户登出'
        }
      );
      
      logger.info(`用户登出: ${req.user.username} (${req.ip})`);
      
      res.json({
        success: true,
        message: '登出成功'
      });
      
    } catch (error) {
      logger.error('登出错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = new AuthController();
