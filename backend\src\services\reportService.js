/**
 * 报表服务
 * 负责报表数据统计、生成和处理
 */

const { Report, Product, Inventory, StockInRecord, ReturnRequest, ChatSession, OperationLog } = require('../models');
const logger = require('../utils/logger');

class ReportService {
  /**
   * 生成周报
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成结果
   */
  async generateWeeklyReport(options = {}) {
    try {
      const { 
        startDate, 
        endDate, 
        generatedBy,
        filters = {}
      } = options;

      logger.info('开始生成周报', { startDate, endDate, generatedBy });

      const reportStartTime = Date.now();

      // 创建报表记录
      const report = new Report({
        report_type: 'weekly',
        title: `库存管理周报 (${this.formatDateRange(startDate, endDate)})`,
        description: '系统自动生成的库存管理周报，包含库存状态、出入库统计、预警信息等',
        period: {
          start_date: startDate,
          end_date: endDate
        },
        filters,
        generated_by: generatedBy,
        status: 'generating'
      });

      await report.save();

      // 并行收集各模块数据
      const [
        summaryData,
        inventoryData,
        stockMovementData,
        returnsData,
        alertsData,
        performanceData
      ] = await Promise.all([
        this.collectSummaryData(startDate, endDate, filters),
        this.collectInventoryData(startDate, endDate, filters),
        this.collectStockMovementData(startDate, endDate, filters),
        this.collectReturnsData(startDate, endDate, filters),
        this.collectAlertsData(startDate, endDate, filters),
        this.collectPerformanceData(startDate, endDate, filters)
      ]);

      // 添加数据章节
      report.addDataSection('summary', '总览摘要', summaryData.data, summaryData.charts);
      report.addDataSection('inventory', '库存状态', inventoryData.data, inventoryData.charts);
      report.addDataSection('stock_movement', '出入库统计', stockMovementData.data, stockMovementData.charts);
      report.addDataSection('returns', '退货分析', returnsData.data, returnsData.charts);
      report.addDataSection('alerts', '预警信息', alertsData.data, alertsData.charts);
      report.addDataSection('performance', '系统性能', performanceData.data, performanceData.charts);

      // 更新摘要数据
      report.updateSummary(summaryData.summary);

      // 标记完成
      const generationTime = Date.now() - reportStartTime;
      report.markCompleted(generationTime);

      await report.save();

      logger.info('周报生成完成', { 
        reportId: report.report_id, 
        generationTime: `${generationTime}ms` 
      });

      return {
        success: true,
        data: {
          report,
          generationTime
        }
      };

    } catch (error) {
      logger.error('生成周报失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 收集总览数据
   */
  async collectSummaryData(startDate, endDate, filters) {
    try {
      const dateFilter = {
        createdAt: { $gte: startDate, $lte: endDate }
      };

      // 基础统计
      const [
        totalProducts,
        totalInventoryValue,
        stockInCount,
        returnRequestsCount,
        chatSessionsCount
      ] = await Promise.all([
        Product.countDocuments({ is_active: true }),
        this.calculateTotalInventoryValue(),
        StockInRecord.countDocuments({ 
          created_at: { $gte: startDate, $lte: endDate },
          status: 'completed'
        }),
        ReturnRequest.countDocuments(dateFilter),
        ChatSession.countDocuments(dateFilter)
      ]);

      // 库存预警统计
      const alertStats = await this.getAlertStatistics();

      const summaryData = {
        period: {
          start: startDate,
          end: endDate,
          days: Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
        },
        overview: {
          total_products: totalProducts,
          total_inventory_value: totalInventoryValue,
          stock_in_count: stockInCount,
          return_requests: returnRequestsCount,
          chat_sessions: chatSessionsCount,
          low_stock_alerts: alertStats.lowStock,
          expiry_alerts: alertStats.expiring
        },
        trends: await this.calculateTrends(startDate, endDate)
      };

      const charts = [
        {
          chart_type: 'metric',
          title: '核心指标',
          data: {
            metrics: [
              { label: '产品总数', value: totalProducts, unit: '个' },
              { label: '库存总值', value: totalInventoryValue, unit: '元' },
              { label: '入库次数', value: stockInCount, unit: '次' },
              { label: '退货申请', value: returnRequestsCount, unit: '个' }
            ]
          }
        }
      ];

      return {
        data: summaryData,
        charts,
        summary: {
          total_products: totalProducts,
          total_stock_value: totalInventoryValue,
          stock_in_count: stockInCount,
          return_requests: returnRequestsCount,
          chat_sessions: chatSessionsCount,
          low_stock_alerts: alertStats.lowStock,
          expiry_alerts: alertStats.expiring
        }
      };

    } catch (error) {
      logger.error('收集总览数据失败:', error);
      throw error;
    }
  }

  /**
   * 收集库存数据
   */
  async collectInventoryData(startDate, endDate, filters) {
    try {
      // 获取库存统计
      const inventoryStats = await Inventory.aggregate([
        { $match: { is_active: true } },
        {
          $lookup: {
            from: 'products',
            localField: 'product_id',
            foreignField: '_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $group: {
            _id: '$product.category',
            total_products: { $sum: 1 },
            total_stock: { $sum: '$current_stock' },
            total_value: { $sum: '$total_value' },
            low_stock_count: {
              $sum: {
                $cond: [
                  { $lte: ['$current_stock', '$reorder_point'] },
                  1,
                  0
                ]
              }
            }
          }
        },
        { $sort: { total_value: -1 } }
      ]);

      // 库存分布数据
      const stockDistribution = await this.getStockDistribution();

      const inventoryData = {
        by_category: inventoryStats,
        distribution: stockDistribution,
        summary: {
          total_categories: inventoryStats.length,
          total_stock_value: inventoryStats.reduce((sum, cat) => sum + cat.total_value, 0),
          low_stock_products: inventoryStats.reduce((sum, cat) => sum + cat.low_stock_count, 0)
        }
      };

      const charts = [
        {
          chart_type: 'pie',
          title: '库存价值分布',
          data: {
            labels: inventoryStats.map(item => item._id),
            values: inventoryStats.map(item => item.total_value)
          }
        },
        {
          chart_type: 'bar',
          title: '各类别库存数量',
          data: {
            labels: inventoryStats.map(item => item._id),
            values: inventoryStats.map(item => item.total_stock)
          }
        }
      ];

      return {
        data: inventoryData,
        charts
      };

    } catch (error) {
      logger.error('收集库存数据失败:', error);
      throw error;
    }
  }

  /**
   * 收集出入库数据
   */
  async collectStockMovementData(startDate, endDate, filters) {
    try {
      // 入库统计
      const stockInStats = await StockInRecord.aggregate([
        {
          $match: {
            created_at: { $gte: startDate, $lte: endDate },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } }
            },
            total_records: { $sum: 1 },
            total_quantity: { $sum: '$quantity' },
            total_cost: { $sum: { $multiply: ['$quantity', '$unit_cost'] } }
          }
        },
        { $sort: { '_id.date': 1 } }
      ]);

      const movementData = {
        stock_in: {
          daily_stats: stockInStats,
          total_records: stockInStats.reduce((sum, day) => sum + day.total_records, 0),
          total_quantity: stockInStats.reduce((sum, day) => sum + day.total_quantity, 0),
          total_cost: stockInStats.reduce((sum, day) => sum + day.total_cost, 0)
        }
      };

      const charts = [
        {
          chart_type: 'line',
          title: '每日入库趋势',
          data: {
            labels: stockInStats.map(item => item._id.date),
            datasets: [
              {
                label: '入库数量',
                data: stockInStats.map(item => item.total_quantity)
              }
            ]
          }
        }
      ];

      return {
        data: movementData,
        charts
      };

    } catch (error) {
      logger.error('收集出入库数据失败:', error);
      throw error;
    }
  }

  /**
   * 收集退货数据
   */
  async collectReturnsData(startDate, endDate, filters) {
    try {
      const returnStats = await ReturnRequest.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            avg_processing_time: { $avg: '$processing_time_hours' }
          }
        }
      ]);

      // 退货原因分析
      const reasonStats = await ReturnRequest.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.reason',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const returnsData = {
        by_status: returnStats,
        by_reason: reasonStats,
        summary: {
          total_returns: returnStats.reduce((sum, status) => sum + status.count, 0),
          avg_processing_time: returnStats.reduce((sum, status) => sum + (status.avg_processing_time || 0), 0) / returnStats.length
        }
      };

      const charts = [
        {
          chart_type: 'pie',
          title: '退货状态分布',
          data: {
            labels: returnStats.map(item => item._id),
            values: returnStats.map(item => item.count)
          }
        }
      ];

      return {
        data: returnsData,
        charts
      };

    } catch (error) {
      logger.error('收集退货数据失败:', error);
      throw error;
    }
  }

  /**
   * 收集预警数据
   */
  async collectAlertsData(startDate, endDate, filters) {
    try {
      // 这里会调用已有的alertService
      const alertService = require('./alertService');
      const alertResult = await alertService.checkAllAlerts();

      if (!alertResult.success) {
        throw new Error('获取预警数据失败');
      }

      const alertsData = {
        current_alerts: alertResult.data.alerts,
        summary: alertResult.data.summary,
        trends: await this.getAlertTrends(startDate, endDate)
      };

      const charts = [
        {
          chart_type: 'bar',
          title: '预警类型分布',
          data: {
            labels: ['低库存', '零库存', '临期', '过期', '负库存'],
            values: [
              alertResult.data.summary.lowStock || 0,
              alertResult.data.summary.outOfStock || 0,
              alertResult.data.summary.expiring || 0,
              alertResult.data.summary.expired || 0,
              alertResult.data.summary.negative || 0
            ]
          }
        }
      ];

      return {
        data: alertsData,
        charts
      };

    } catch (error) {
      logger.error('收集预警数据失败:', error);
      throw error;
    }
  }

  /**
   * 收集性能数据
   */
  async collectPerformanceData(startDate, endDate, filters) {
    try {
      // 系统操作统计
      const operationStats = await OperationLog.aggregate([
        {
          $match: {
            timestamp: { $gte: startDate, $lte: endDate },
            status: 'success'
          }
        },
        {
          $group: {
            _id: '$operation_type',
            count: { $sum: 1 },
            avg_response_time: { $avg: '$details.response_time_ms' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const performanceData = {
        operations: operationStats,
        summary: {
          total_operations: operationStats.reduce((sum, op) => sum + op.count, 0),
          avg_response_time: operationStats.reduce((sum, op) => sum + (op.avg_response_time || 0), 0) / operationStats.length
        }
      };

      const charts = [
        {
          chart_type: 'table',
          title: '系统操作统计',
          data: {
            headers: ['操作类型', '次数', '平均响应时间(ms)'],
            rows: operationStats.map(op => [
              op._id,
              op.count,
              Math.round(op.avg_response_time || 0)
            ])
          }
        }
      ];

      return {
        data: performanceData,
        charts
      };

    } catch (error) {
      logger.error('收集性能数据失败:', error);
      throw error;
    }
  }

  // 辅助方法
  formatDateRange(startDate, endDate) {
    const start = new Date(startDate).toLocaleDateString('zh-CN');
    const end = new Date(endDate).toLocaleDateString('zh-CN');
    return `${start} - ${end}`;
  }

  async calculateTotalInventoryValue() {
    const result = await Inventory.aggregate([
      { $match: { is_active: true } },
      { $group: { _id: null, total: { $sum: '$total_value' } } }
    ]);
    return result[0]?.total || 0;
  }

  async getAlertStatistics() {
    // 简化的预警统计，实际会调用alertService
    return {
      lowStock: 0,
      expiring: 0,
      expired: 0,
      outOfStock: 0,
      negative: 0
    };
  }

  async calculateTrends(startDate, endDate) {
    // 趋势计算逻辑
    return {
      inventory_trend: 'stable',
      return_trend: 'decreasing',
      alert_trend: 'stable'
    };
  }

  async getStockDistribution() {
    // 库存分布计算
    return {
      high_stock: 0,
      normal_stock: 0,
      low_stock: 0,
      out_of_stock: 0
    };
  }

  async getAlertTrends(startDate, endDate) {
    // 预警趋势分析
    return {
      daily_alerts: [],
      trend_direction: 'stable'
    };
  }

  /**
   * 生成月报
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成结果
   */
  async generateMonthlyReport(options = {}) {
    try {
      const {
        startDate,
        endDate,
        generatedBy,
        filters = {},
        title,
        description
      } = options;

      logger.info('开始生成月报', { startDate, endDate, generatedBy });

      const reportStartTime = Date.now();

      // 创建报表记录
      const report = new Report({
        report_type: 'monthly',
        title: title || `库存管理月报 (${this.formatDateRange(startDate, endDate)})`,
        description: description || '系统自动生成的库存管理月报，包含详细的月度统计和趋势分析',
        period: {
          start_date: startDate,
          end_date: endDate
        },
        filters,
        generated_by: generatedBy,
        status: 'generating'
      });

      await report.save();

      // 月报包含更详细的数据分析
      const [
        summaryData,
        inventoryData,
        stockMovementData,
        returnsData,
        alertsData,
        performanceData,
        trendsData
      ] = await Promise.all([
        this.collectSummaryData(startDate, endDate, filters),
        this.collectInventoryData(startDate, endDate, filters),
        this.collectStockMovementData(startDate, endDate, filters),
        this.collectReturnsData(startDate, endDate, filters),
        this.collectAlertsData(startDate, endDate, filters),
        this.collectPerformanceData(startDate, endDate, filters),
        this.collectTrendsData(startDate, endDate, filters) // 月报特有的趋势分析
      ]);

      // 添加数据章节
      report.addDataSection('summary', '总览摘要', summaryData.data, summaryData.charts);
      report.addDataSection('inventory', '库存状态', inventoryData.data, inventoryData.charts);
      report.addDataSection('stock_movement', '出入库统计', stockMovementData.data, stockMovementData.charts);
      report.addDataSection('returns', '退货分析', returnsData.data, returnsData.charts);
      report.addDataSection('alerts', '预警信息', alertsData.data, alertsData.charts);
      report.addDataSection('performance', '系统性能', performanceData.data, performanceData.charts);
      report.addDataSection('trends', '趋势分析', trendsData.data, trendsData.charts);

      // 更新摘要数据
      report.updateSummary(summaryData.summary);

      // 标记完成
      const generationTime = Date.now() - reportStartTime;
      report.markCompleted(generationTime);

      await report.save();

      logger.info('月报生成完成', {
        reportId: report.report_id,
        generationTime: `${generationTime}ms`
      });

      return {
        success: true,
        data: {
          report,
          generationTime
        }
      };

    } catch (error) {
      logger.error('生成月报失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成自定义报表
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成结果
   */
  async generateCustomReport(options = {}) {
    try {
      const {
        startDate,
        endDate,
        generatedBy,
        filters = {},
        title,
        description,
        sections = ['summary', 'inventory', 'stock_movement', 'returns', 'alerts']
      } = options;

      logger.info('开始生成自定义报表', { startDate, endDate, generatedBy, sections });

      const reportStartTime = Date.now();

      // 创建报表记录
      const report = new Report({
        report_type: 'custom',
        title: title || `自定义报表 (${this.formatDateRange(startDate, endDate)})`,
        description: description || '用户自定义的报表，包含指定的数据章节',
        period: {
          start_date: startDate,
          end_date: endDate
        },
        filters,
        generated_by: generatedBy,
        status: 'generating'
      });

      await report.save();

      // 根据指定的章节收集数据
      const dataPromises = [];
      let summaryData = null;

      if (sections.includes('summary')) {
        dataPromises.push(this.collectSummaryData(startDate, endDate, filters));
      }
      if (sections.includes('inventory')) {
        dataPromises.push(this.collectInventoryData(startDate, endDate, filters));
      }
      if (sections.includes('stock_movement')) {
        dataPromises.push(this.collectStockMovementData(startDate, endDate, filters));
      }
      if (sections.includes('returns')) {
        dataPromises.push(this.collectReturnsData(startDate, endDate, filters));
      }
      if (sections.includes('alerts')) {
        dataPromises.push(this.collectAlertsData(startDate, endDate, filters));
      }
      if (sections.includes('performance')) {
        dataPromises.push(this.collectPerformanceData(startDate, endDate, filters));
      }

      const results = await Promise.all(dataPromises);
      let resultIndex = 0;

      // 添加数据章节
      if (sections.includes('summary')) {
        summaryData = results[resultIndex++];
        report.addDataSection('summary', '总览摘要', summaryData.data, summaryData.charts);
      }
      if (sections.includes('inventory')) {
        const inventoryData = results[resultIndex++];
        report.addDataSection('inventory', '库存状态', inventoryData.data, inventoryData.charts);
      }
      if (sections.includes('stock_movement')) {
        const stockMovementData = results[resultIndex++];
        report.addDataSection('stock_movement', '出入库统计', stockMovementData.data, stockMovementData.charts);
      }
      if (sections.includes('returns')) {
        const returnsData = results[resultIndex++];
        report.addDataSection('returns', '退货分析', returnsData.data, returnsData.charts);
      }
      if (sections.includes('alerts')) {
        const alertsData = results[resultIndex++];
        report.addDataSection('alerts', '预警信息', alertsData.data, alertsData.charts);
      }
      if (sections.includes('performance')) {
        const performanceData = results[resultIndex++];
        report.addDataSection('performance', '系统性能', performanceData.data, performanceData.charts);
      }

      // 更新摘要数据（如果包含summary章节）
      if (summaryData) {
        report.updateSummary(summaryData.summary);
      }

      // 标记完成
      const generationTime = Date.now() - reportStartTime;
      report.markCompleted(generationTime);

      await report.save();

      logger.info('自定义报表生成完成', {
        reportId: report.report_id,
        generationTime: `${generationTime}ms`
      });

      return {
        success: true,
        data: {
          report,
          generationTime
        }
      };

    } catch (error) {
      logger.error('生成自定义报表失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 收集趋势数据（月报专用）
   */
  async collectTrendsData(startDate, endDate, filters) {
    try {
      // 计算上个月的数据进行对比
      const previousMonthStart = new Date(startDate);
      previousMonthStart.setMonth(previousMonthStart.getMonth() - 1);
      const previousMonthEnd = new Date(endDate);
      previousMonthEnd.setMonth(previousMonthEnd.getMonth() - 1);

      // 获取当月和上月的基础数据
      const [currentMonth, previousMonth] = await Promise.all([
        this.getBasicStats(startDate, endDate),
        this.getBasicStats(previousMonthStart, previousMonthEnd)
      ]);

      // 计算趋势
      const trends = {
        inventory_value: this.calculateTrend(currentMonth.inventoryValue, previousMonth.inventoryValue),
        stock_movements: this.calculateTrend(currentMonth.stockMovements, previousMonth.stockMovements),
        return_requests: this.calculateTrend(currentMonth.returnRequests, previousMonth.returnRequests),
        chat_sessions: this.calculateTrend(currentMonth.chatSessions, previousMonth.chatSessions)
      };

      const trendsData = {
        current_period: currentMonth,
        previous_period: previousMonth,
        trends: trends,
        insights: this.generateInsights(trends)
      };

      const charts = [
        {
          chart_type: 'line',
          title: '月度趋势对比',
          data: {
            labels: ['上月', '本月'],
            datasets: [
              {
                label: '库存价值',
                data: [previousMonth.inventoryValue, currentMonth.inventoryValue]
              },
              {
                label: '出入库次数',
                data: [previousMonth.stockMovements, currentMonth.stockMovements]
              }
            ]
          }
        }
      ];

      return {
        data: trendsData,
        charts
      };

    } catch (error) {
      logger.error('收集趋势数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取基础统计数据
   */
  async getBasicStats(startDate, endDate) {
    const [inventoryValue, stockMovements, returnRequests, chatSessions] = await Promise.all([
      this.calculateTotalInventoryValue(),
      StockInRecord.countDocuments({
        created_at: { $gte: startDate, $lte: endDate },
        status: 'completed'
      }),
      ReturnRequest.countDocuments({
        createdAt: { $gte: startDate, $lte: endDate }
      }),
      ChatSession.countDocuments({
        createdAt: { $gte: startDate, $lte: endDate }
      })
    ]);

    return {
      inventoryValue,
      stockMovements,
      returnRequests,
      chatSessions
    };
  }

  /**
   * 计算趋势
   */
  calculateTrend(current, previous) {
    if (previous === 0) {
      return current > 0 ? 100 : 0;
    }
    return ((current - previous) / previous * 100).toFixed(2);
  }

  /**
   * 生成洞察
   */
  generateInsights(trends) {
    const insights = [];

    Object.keys(trends).forEach(key => {
      const trend = parseFloat(trends[key]);
      if (trend > 10) {
        insights.push(`${key}显著增长${trend}%`);
      } else if (trend < -10) {
        insights.push(`${key}显著下降${Math.abs(trend)}%`);
      }
    });

    return insights.length > 0 ? insights : ['各项指标保持稳定'];
  }
}

module.exports = new ReportService();
