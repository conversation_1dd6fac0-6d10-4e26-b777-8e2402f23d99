/**
 * 盘点服务
 * 负责盘点任务的创建、管理和差异分析
 */

const { StockCount, Product, Inventory, User } = require('../models');
const logger = require('../utils/logger');

class StockCountService {
  /**
   * 创建盘点计划
   * @param {Object} options - 盘点选项
   * @returns {Promise<Object>} 创建结果
   */
  async createStockCountPlan(options = {}) {
    try {
      const {
        title,
        description,
        count_type = 'partial',
        scheduled_date,
        deadline,
        location,
        categories = [],
        product_ids = [],
        created_by,
        settings = {}
      } = options;

      logger.info('创建盘点计划', { 
        count_type, 
        scheduled_date, 
        categories: categories.length,
        products: product_ids.length 
      });

      // 创建盘点任务
      const stockCount = new StockCount({
        title,
        description,
        count_type,
        scheduled_date: new Date(scheduled_date),
        deadline: deadline ? new Date(deadline) : null,
        location,
        categories,
        created_by,
        settings: {
          allow_negative_count: settings.allow_negative_count || false,
          require_verification: settings.require_verification !== false,
          auto_adjust_inventory: settings.auto_adjust_inventory || false,
          tolerance_percentage: settings.tolerance_percentage || 5
        }
      });

      // 添加盘点项目
      if (product_ids.length > 0) {
        // 指定产品盘点
        await this.addProductsToCount(stockCount, product_ids);
      } else if (categories.length > 0) {
        // 按类别盘点
        await this.addProductsByCategoryToCount(stockCount, categories);
      } else if (count_type === 'full') {
        // 全盘
        await this.addAllProductsToCount(stockCount, location);
      }

      await stockCount.save();

      logger.info('盘点计划创建成功', { 
        count_id: stockCount.count_id,
        total_items: stockCount.items.length 
      });

      return {
        success: true,
        data: { stockCount }
      };

    } catch (error) {
      logger.error('创建盘点计划失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 添加指定产品到盘点
   */
  async addProductsToCount(stockCount, productIds) {
    try {
      const inventories = await Inventory.find({
        product_id: { $in: productIds },
        is_active: true
      }).populate('product_id', 'name category unit_price');

      for (const inventory of inventories) {
        stockCount.addCountItem(
          inventory.product_id._id,
          inventory.current_stock,
          `产品: ${inventory.product_id.name}`
        );
      }

      return stockCount;

    } catch (error) {
      logger.error('添加产品到盘点失败:', error);
      throw error;
    }
  }

  /**
   * 按类别添加产品到盘点
   */
  async addProductsByCategoryToCount(stockCount, categories) {
    try {
      const inventories = await Inventory.find({
        is_active: true
      }).populate({
        path: 'product_id',
        match: { category: { $in: categories } },
        select: 'name category unit_price'
      });

      const validInventories = inventories.filter(inv => inv.product_id);

      for (const inventory of validInventories) {
        stockCount.addCountItem(
          inventory.product_id._id,
          inventory.current_stock,
          `类别: ${inventory.product_id.category}`
        );
      }

      return stockCount;

    } catch (error) {
      logger.error('按类别添加产品到盘点失败:', error);
      throw error;
    }
  }

  /**
   * 添加所有产品到盘点（全盘）
   */
  async addAllProductsToCount(stockCount, location = null) {
    try {
      const query = { is_active: true };
      if (location) {
        query.location = location;
      }

      const inventories = await Inventory.find(query)
        .populate('product_id', 'name category unit_price');

      for (const inventory of inventories) {
        stockCount.addCountItem(
          inventory.product_id._id,
          inventory.current_stock,
          `全盘 - ${inventory.product_id.name}`
        );
      }

      return stockCount;

    } catch (error) {
      logger.error('添加所有产品到盘点失败:', error);
      throw error;
    }
  }

  /**
   * 分配盘点任务
   * @param {string} countId - 盘点ID
   * @param {Array} assignments - 分配信息
   * @returns {Promise<Object>} 分配结果
   */
  async assignStockCountTask(countId, assignments) {
    try {
      logger.info('分配盘点任务', { countId, assignments: assignments.length });

      const stockCount = await StockCount.findById(countId);
      if (!stockCount) {
        throw new Error('盘点任务不存在');
      }

      if (stockCount.status !== 'planned') {
        throw new Error('只能分配计划中的盘点任务');
      }

      // 验证用户存在
      const userIds = assignments.map(a => a.user_id);
      const users = await User.find({ _id: { $in: userIds } });
      
      if (users.length !== userIds.length) {
        throw new Error('部分用户不存在');
      }

      // 清除现有分配
      stockCount.assigned_to = [];

      // 添加新分配
      for (const assignment of assignments) {
        stockCount.assignUser(assignment.user_id, assignment.role || 'counter');
      }

      await stockCount.save();

      logger.info('盘点任务分配成功', { 
        count_id: stockCount.count_id,
        assigned_users: stockCount.assigned_to.length 
      });

      return {
        success: true,
        data: { stockCount }
      };

    } catch (error) {
      logger.error('分配盘点任务失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 提交盘点结果
   * @param {string} countId - 盘点ID
   * @param {Array} countResults - 盘点结果
   * @param {string} userId - 提交用户ID
   * @returns {Promise<Object>} 提交结果
   */
  async submitCountResults(countId, countResults, userId) {
    try {
      logger.info('提交盘点结果', { 
        countId, 
        results: countResults.length,
        userId 
      });

      const stockCount = await StockCount.findById(countId);
      if (!stockCount) {
        throw new Error('盘点任务不存在');
      }

      if (stockCount.status !== 'in_progress') {
        throw new Error('只能提交进行中的盘点任务结果');
      }

      // 验证用户权限
      const userAssignment = stockCount.assigned_to.find(
        a => a.user_id.toString() === userId.toString()
      );
      
      if (!userAssignment) {
        throw new Error('用户未被分配到此盘点任务');
      }

      // 更新盘点结果
      for (const result of countResults) {
        const { item_index, actual_quantity, notes } = result;
        
        if (item_index >= 0 && item_index < stockCount.items.length) {
          stockCount.updateCountItem(
            item_index,
            actual_quantity,
            userId,
            notes
          );
        }
      }

      // 更新用户状态
      userAssignment.status = 'completed';

      await stockCount.save();

      // 分析差异
      const analysis = await this.analyzeCountDiscrepancies(stockCount);

      logger.info('盘点结果提交成功', { 
        count_id: stockCount.count_id,
        discrepancies: analysis.discrepancy_items 
      });

      return {
        success: true,
        data: { 
          stockCount,
          analysis
        }
      };

    } catch (error) {
      logger.error('提交盘点结果失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 分析盘点差异
   * @param {Object} stockCount - 盘点对象
   * @returns {Promise<Object>} 差异分析结果
   */
  async analyzeCountDiscrepancies(stockCount) {
    try {
      const analysis = {
        total_items: stockCount.items.length,
        counted_items: 0,
        accurate_items: 0,
        discrepancy_items: 0,
        major_discrepancies: [],
        minor_discrepancies: [],
        recommendations: []
      };

      const tolerancePercentage = stockCount.settings.tolerance_percentage || 5;

      for (let i = 0; i < stockCount.items.length; i++) {
        const item = stockCount.items[i];
        
        if (item.actual_quantity !== null) {
          analysis.counted_items++;
          
          if (item.difference === 0) {
            analysis.accurate_items++;
          } else {
            analysis.discrepancy_items++;
            
            const discrepancyPercentage = Math.abs(item.difference_percentage);
            const discrepancyInfo = {
              item_index: i,
              product_id: item.product_id,
              expected: item.expected_quantity,
              actual: item.actual_quantity,
              difference: item.difference,
              percentage: discrepancyPercentage
            };
            
            if (discrepancyPercentage > tolerancePercentage) {
              analysis.major_discrepancies.push(discrepancyInfo);
            } else {
              analysis.minor_discrepancies.push(discrepancyInfo);
            }
          }
        }
      }

      // 生成建议
      if (analysis.major_discrepancies.length > 0) {
        analysis.recommendations.push('存在重大差异，建议重新盘点相关产品');
      }
      
      if (analysis.discrepancy_items > analysis.total_items * 0.1) {
        analysis.recommendations.push('差异项目较多，建议检查盘点流程');
      }
      
      if (analysis.counted_items < analysis.total_items) {
        analysis.recommendations.push('存在未盘点项目，请完成所有项目的盘点');
      }

      return analysis;

    } catch (error) {
      logger.error('分析盘点差异失败:', error);
      throw error;
    }
  }

  /**
   * 处理盘点差异调整
   * @param {string} countId - 盘点ID
   * @param {Array} adjustments - 调整信息
   * @param {string} userId - 调整用户ID
   * @returns {Promise<Object>} 调整结果
   */
  async processCountAdjustments(countId, adjustments, userId) {
    try {
      logger.info('处理盘点差异调整', { 
        countId, 
        adjustments: adjustments.length,
        userId 
      });

      const stockCount = await StockCount.findById(countId);
      if (!stockCount) {
        throw new Error('盘点任务不存在');
      }

      // 记录调整
      for (const adjustment of adjustments) {
        stockCount.adjustments.push({
          item_index: adjustment.item_index,
          adjustment_type: adjustment.adjustment_type,
          quantity_change: adjustment.quantity_change,
          reason: adjustment.reason,
          adjusted_by: userId,
          approved: false // 需要审批
        });
      }

      await stockCount.save();

      logger.info('盘点差异调整记录成功', { 
        count_id: stockCount.count_id,
        adjustments: stockCount.adjustments.length 
      });

      return {
        success: true,
        data: { stockCount }
      };

    } catch (error) {
      logger.error('处理盘点差异调整失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成盘点建议
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 建议结果
   */
  async generateCountRecommendations(options = {}) {
    try {
      const { 
        days_since_last_count = 90,
        low_stock_threshold = 0.2,
        high_value_threshold = 1000
      } = options;

      logger.info('生成盘点建议', { days_since_last_count });

      const recommendations = {
        high_priority: [],
        medium_priority: [],
        low_priority: [],
        summary: {
          total_recommendations: 0,
          estimated_time_hours: 0
        }
      };

      // 查找需要盘点的产品
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days_since_last_count);

      const inventories = await Inventory.find({
        is_active: true,
        $or: [
          { last_count_date: { $lt: cutoffDate } },
          { last_count_date: null }
        ]
      }).populate('product_id', 'name category unit_price safety_stock');

      for (const inventory of inventories) {
        const product = inventory.product_id;
        let priority = 'low';
        let reasons = [];

        // 高价值产品
        if (inventory.total_value > high_value_threshold) {
          priority = 'high';
          reasons.push('高价值产品');
        }

        // 低库存产品
        if (inventory.current_stock <= inventory.reorder_point) {
          priority = priority === 'high' ? 'high' : 'medium';
          reasons.push('低库存');
        }

        // 长时间未盘点
        const daysSinceLastCount = inventory.last_count_date 
          ? Math.floor((new Date() - inventory.last_count_date) / (1000 * 60 * 60 * 24))
          : 999;
        
        if (daysSinceLastCount > days_since_last_count * 1.5) {
          priority = priority === 'low' ? 'medium' : priority;
          reasons.push(`${daysSinceLastCount}天未盘点`);
        }

        const recommendation = {
          product_id: product._id,
          product_name: product.name,
          category: product.category,
          current_stock: inventory.current_stock,
          total_value: inventory.total_value,
          last_count_date: inventory.last_count_date,
          reasons: reasons,
          estimated_time_minutes: this.estimateCountTime(inventory)
        };

        recommendations[`${priority}_priority`].push(recommendation);
      }

      // 计算总计
      recommendations.summary.total_recommendations = 
        recommendations.high_priority.length +
        recommendations.medium_priority.length +
        recommendations.low_priority.length;

      recommendations.summary.estimated_time_hours = Math.ceil(
        [...recommendations.high_priority, ...recommendations.medium_priority, ...recommendations.low_priority]
          .reduce((sum, rec) => sum + rec.estimated_time_minutes, 0) / 60
      );

      logger.info('盘点建议生成完成', { 
        total: recommendations.summary.total_recommendations,
        high: recommendations.high_priority.length,
        medium: recommendations.medium_priority.length,
        low: recommendations.low_priority.length
      });

      return {
        success: true,
        data: recommendations
      };

    } catch (error) {
      logger.error('生成盘点建议失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 估算盘点时间
   */
  estimateCountTime(inventory) {
    // 基础时间：每个产品5分钟
    let baseTime = 5;
    
    // 根据库存数量调整
    if (inventory.current_stock > 100) {
      baseTime += 5;
    } else if (inventory.current_stock > 50) {
      baseTime += 2;
    }
    
    // 根据价值调整
    if (inventory.total_value > 1000) {
      baseTime += 3;
    }
    
    return baseTime;
  }

  /**
   * 获取盘点统计
   * @param {Object} filters - 过滤条件
   * @returns {Promise<Object>} 统计结果
   */
  async getStockCountStatistics(filters = {}) {
    try {
      const { start_date, end_date, count_type, status } = filters;

      const matchConditions = {};
      
      if (start_date && end_date) {
        matchConditions.createdAt = {
          $gte: new Date(start_date),
          $lte: new Date(end_date)
        };
      }
      
      if (count_type) {
        matchConditions.count_type = count_type;
      }
      
      if (status) {
        matchConditions.status = status;
      }

      const statistics = await StockCount.aggregate([
        { $match: matchConditions },
        {
          $group: {
            _id: null,
            total_counts: { $sum: 1 },
            completed_counts: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            in_progress_counts: {
              $sum: { $cond: [{ $eq: ['$status', 'in_progress'] }, 1, 0] }
            },
            planned_counts: {
              $sum: { $cond: [{ $eq: ['$status', 'planned'] }, 1, 0] }
            },
            total_items: { $sum: '$summary.total_items' },
            total_discrepancies: { $sum: '$summary.discrepancy_items' },
            avg_accuracy: { $avg: '$summary.accuracy_percentage' }
          }
        }
      ]);

      const result = statistics[0] || {
        total_counts: 0,
        completed_counts: 0,
        in_progress_counts: 0,
        planned_counts: 0,
        total_items: 0,
        total_discrepancies: 0,
        avg_accuracy: 0
      };

      return {
        success: true,
        data: result
      };

    } catch (error) {
      logger.error('获取盘点统计失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new StockCountService();
