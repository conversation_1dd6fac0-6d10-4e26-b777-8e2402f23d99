import React, { useState, useEffect } from 'react'
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Row,
  Col,
  Statistic,
  Tabs,
  Tag,
  Progress,
  Alert,
  DatePicker,
  InputNumber,
  Divider
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { inventoryService } from '../services/inventory'
import dayjs from 'dayjs'

const { Title } = Typography
const { Option } = Select
const { Search } = Input
const { TabPane } = Tabs
const { RangePicker } = DatePicker

const Inventory = () => {
  // 状态管理
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  // 库存总览数据
  const [overview, setOverview] = useState({})
  const [inventoryList, setInventoryList] = useState([])
  const [inventoryTotal, setInventoryTotal] = useState(0)
  const [inventoryPage, setInventoryPage] = useState(1)
  const [inventoryPageSize, setInventoryPageSize] = useState(10)

  // 入库记录数据
  const [stockInRecords, setStockInRecords] = useState([])
  const [stockInTotal, setStockInTotal] = useState(0)
  const [stockInPage, setStockInPage] = useState(1)
  const [stockInPageSize, setStockInPageSize] = useState(10)

  // 预警数据
  const [alerts, setAlerts] = useState([])
  const [alertsSummary, setAlertsSummary] = useState({})

  // 筛选条件
  const [searchText, setSearchText] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [dateRange, setDateRange] = useState([])

  // 模态框状态
  const [isStockInModalVisible, setIsStockInModalVisible] = useState(false)
  const [form] = Form.useForm()

  // 初始化数据
  useEffect(() => {
    if (activeTab === 'overview') {
      loadOverview()
      loadInventoryList()
    } else if (activeTab === 'stockin') {
      loadStockInRecords()
    } else if (activeTab === 'alerts') {
      loadAlerts()
      loadAlertsSummary()
    }
  }, [activeTab, inventoryPage, inventoryPageSize, stockInPage, stockInPageSize, searchText, selectedCategory, selectedStatus, dateRange])

  // 加载库存总览
  const loadOverview = async () => {
    try {
      const response = await inventoryService.getInventoryOverview()
      if (response.success) {
        setOverview(response.data || {})
      }
    } catch (error) {
      console.error('加载库存总览失败:', error)
    }
  }

  // 加载库存列表
  const loadInventoryList = async () => {
    setLoading(true)
    try {
      const response = await inventoryService.getInventoryList({
        page: inventoryPage,
        limit: inventoryPageSize,
        search: searchText,
        category: selectedCategory,
        status: selectedStatus
      })

      if (response.success) {
        setInventoryList(response.data.inventory || [])
        setInventoryTotal(response.data.total || 0)
      }
    } catch (error) {
      message.error('加载库存列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载入库记录
  const loadStockInRecords = async () => {
    setLoading(true)
    try {
      const response = await inventoryService.getStockInRecords({
        page: stockInPage,
        limit: stockInPageSize,
        status: selectedStatus,
        startDate: dateRange[0]?.format('YYYY-MM-DD'),
        endDate: dateRange[1]?.format('YYYY-MM-DD')
      })

      if (response.success) {
        setStockInRecords(response.data.records || [])
        setStockInTotal(response.data.total || 0)
      }
    } catch (error) {
      message.error('加载入库记录失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载预警信息
  const loadAlerts = async () => {
    setLoading(true)
    try {
      const response = await inventoryService.getAlerts()
      if (response.success) {
        setAlerts(response.data || [])
      }
    } catch (error) {
      message.error('加载预警信息失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载预警摘要
  const loadAlertsSummary = async () => {
    try {
      const response = await inventoryService.getAlertsSummary()
      if (response.success) {
        setAlertsSummary(response.data || {})
      }
    } catch (error) {
      console.error('加载预警摘要失败:', error)
    }
  }

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value)
    if (activeTab === 'overview') {
      setInventoryPage(1)
    }
  }

  // 处理分类筛选
  const handleCategoryChange = (value) => {
    setSelectedCategory(value)
    setInventoryPage(1)
  }

  // 处理状态筛选
  const handleStatusChange = (value) => {
    setSelectedStatus(value)
    if (activeTab === 'overview') {
      setInventoryPage(1)
    } else if (activeTab === 'stockin') {
      setStockInPage(1)
    }
  }

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    setDateRange(dates || [])
    setStockInPage(1)
  }

  // 打开入库模态框
  const handleOpenStockInModal = () => {
    setIsStockInModalVisible(true)
    form.resetFields()
  }

  // 关闭入库模态框
  const handleCloseStockInModal = () => {
    setIsStockInModalVisible(false)
    form.resetFields()
  }

  // 创建入库记录
  const handleCreateStockIn = async (values) => {
    try {
      await inventoryService.createStockInRecord(values)
      message.success('入库记录创建成功')
      handleCloseStockInModal()
      if (activeTab === 'stockin') {
        loadStockInRecords()
      }
      if (activeTab === 'overview') {
        loadOverview()
        loadInventoryList()
      }
    } catch (error) {
      message.error('创建入库记录失败')
    }
  }

  // 处理入库记录
  const handleProcessStockIn = async (id) => {
    try {
      await inventoryService.processStockInRecord(id)
      message.success('入库记录处理成功')
      loadStockInRecords()
      if (activeTab === 'overview') {
        loadOverview()
        loadInventoryList()
      }
    } catch (error) {
      message.error('处理入库记录失败')
    }
  }

  // 触发预警检查
  const handleTriggerAlertCheck = async () => {
    try {
      await inventoryService.triggerAlertCheck()
      message.success('预警检查已触发')
      loadAlerts()
      loadAlertsSummary()
    } catch (error) {
      message.error('触发预警检查失败')
    }
  }

  // 库存列表表格列定义
  const inventoryColumns = [
    {
      title: '商品名称',
      dataIndex: ['product', 'name'],
      key: 'product_name',
      ellipsis: true
    },
    {
      title: '条码',
      dataIndex: ['product', 'barcode'],
      key: 'barcode',
      width: 120
    },
    {
      title: '分类',
      dataIndex: ['product', 'category'],
      key: 'category',
      width: 100,
      render: (category) => <Tag color="blue">{category}</Tag>
    },
    {
      title: '当前库存',
      dataIndex: 'current_stock',
      key: 'current_stock',
      width: 100,
      render: (stock, record) => (
        <span style={{
          color: stock <= (record.min_stock || 0) ? '#ff4d4f' :
                 stock <= (record.warning_stock || 0) ? '#faad14' : '#52c41a'
        }}>
          {stock} {record.product?.unit}
        </span>
      )
    },
    {
      title: '预警库存',
      dataIndex: 'warning_stock',
      key: 'warning_stock',
      width: 100,
      render: (stock, record) => `${stock || 0} ${record.product?.unit}`
    },
    {
      title: '最低库存',
      dataIndex: 'min_stock',
      key: 'min_stock',
      width: 100,
      render: (stock, record) => `${stock || 0} ${record.product?.unit}`
    },
    {
      title: '库存状态',
      key: 'stock_status',
      width: 100,
      render: (_, record) => {
        const { current_stock, min_stock, warning_stock } = record
        if (current_stock <= (min_stock || 0)) {
          return <Tag color="red">库存不足</Tag>
        } else if (current_stock <= (warning_stock || 0)) {
          return <Tag color="orange">库存预警</Tag>
        } else {
          return <Tag color="green">库存正常</Tag>
        }
      }
    },
    {
      title: '最后更新',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    }
  ]

  // 入库记录表格列定义
  const stockInColumns = [
    {
      title: '入库单号',
      dataIndex: 'record_number',
      key: 'record_number',
      width: 150
    },
    {
      title: '商品名称',
      dataIndex: ['product', 'name'],
      key: 'product_name',
      ellipsis: true
    },
    {
      title: '入库数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (quantity, record) => `${quantity} ${record.product?.unit}`
    },
    {
      title: '单价',
      dataIndex: 'unit_cost',
      key: 'unit_cost',
      width: 100,
      render: (cost) => `¥${cost?.toFixed(2) || '0.00'}`
    },
    {
      title: '总成本',
      dataIndex: 'total_cost',
      key: 'total_cost',
      width: 100,
      render: (cost) => `¥${cost?.toFixed(2) || '0.00'}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusMap = {
          pending: { color: 'orange', text: '待处理' },
          processed: { color: 'green', text: '已处理' },
          cancelled: { color: 'red', text: '已取消' }
        }
        const config = statusMap[status] || { color: 'default', text: status }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={() => handleProcessStockIn(record.id)}
            >
              处理
            </Button>
          )}
        </Space>
      )
    }
  ]

  // 预警列表表格列定义
  const alertColumns = [
    {
      title: '预警类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type) => {
        const typeMap = {
          low_stock: { color: 'orange', text: '低库存' },
          zero_stock: { color: 'red', text: '零库存' },
          expiring: { color: 'yellow', text: '临期' },
          expired: { color: 'red', text: '过期' }
        }
        const config = typeMap[type] || { color: 'default', text: type }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '商品名称',
      dataIndex: ['product', 'name'],
      key: 'product_name',
      ellipsis: true
    },
    {
      title: '当前库存',
      dataIndex: 'current_stock',
      key: 'current_stock',
      width: 100,
      render: (stock, record) => `${stock} ${record.product?.unit}`
    },
    {
      title: '预警信息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity) => {
        const severityMap = {
          high: { color: 'red', text: '高' },
          medium: { color: 'orange', text: '中' },
          low: { color: 'yellow', text: '低' }
        }
        const config = severityMap[severity] || { color: 'default', text: severity }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    }
  ]

  return (
    <div className="fade-in">
      <div className="page-header">
        <Title level={2}>库存管理</Title>
        <p>查看库存总览、入库管理、盘点任务和库存预警</p>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 库存总览 */}
        <TabPane tab="库存总览" key="overview">
          {/* 统计卡片 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="总库存商品"
                  value={overview.total_products || 0}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="库存总值"
                  value={overview.total_value || 0}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="预警商品"
                  value={overview.warning_products || 0}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="缺货商品"
                  value={overview.out_of_stock || 0}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 库存列表 */}
          <Card>
            <div style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} sm={12} md={8}>
                  <Search
                    placeholder="搜索商品名称、条码"
                    allowClear
                    onSearch={handleSearch}
                    style={{ width: '100%' }}
                  />
                </Col>
                <Col xs={12} sm={6} md={4}>
                  <Select
                    placeholder="选择分类"
                    allowClear
                    value={selectedCategory}
                    onChange={handleCategoryChange}
                    style={{ width: '100%' }}
                  >
                    <Option value="食品">食品</Option>
                    <Option value="饮料">饮料</Option>
                    <Option value="日用品">日用品</Option>
                    <Option value="其他">其他</Option>
                  </Select>
                </Col>
                <Col xs={12} sm={6} md={4}>
                  <Select
                    placeholder="库存状态"
                    allowClear
                    value={selectedStatus}
                    onChange={handleStatusChange}
                    style={{ width: '100%' }}
                  >
                    <Option value="normal">正常</Option>
                    <Option value="warning">预警</Option>
                    <Option value="low">不足</Option>
                  </Select>
                </Col>
                <Col xs={24} sm={24} md={8}>
                  <Space wrap>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleOpenStockInModal}
                    >
                      新增入库
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => {
                        loadOverview()
                        loadInventoryList()
                      }}
                    >
                      刷新
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            <Divider />

            <Table
              columns={inventoryColumns}
              dataSource={inventoryList}
              rowKey="id"
              loading={loading}
              pagination={{
                current: inventoryPage,
                pageSize: inventoryPageSize,
                total: inventoryTotal,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, size) => {
                  setInventoryPage(page)
                  setInventoryPageSize(size)
                }
              }}
              scroll={{ x: 1200 }}
            />
          </Card>
        </TabPane>

        {/* 入库记录 */}
        <TabPane tab="入库记录" key="stockin">
          <Card>
            <div style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} sm={12} md={8}>
                  <RangePicker
                    value={dateRange}
                    onChange={handleDateRangeChange}
                    style={{ width: '100%' }}
                    placeholder={['开始日期', '结束日期']}
                  />
                </Col>
                <Col xs={12} sm={6} md={4}>
                  <Select
                    placeholder="选择状态"
                    allowClear
                    value={selectedStatus}
                    onChange={handleStatusChange}
                    style={{ width: '100%' }}
                  >
                    <Option value="pending">待处理</Option>
                    <Option value="processed">已处理</Option>
                    <Option value="cancelled">已取消</Option>
                  </Select>
                </Col>
                <Col xs={12} sm={18} md={12}>
                  <Space wrap>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleOpenStockInModal}
                    >
                      新增入库
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={loadStockInRecords}
                    >
                      刷新
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            <Divider />

            <Table
              columns={stockInColumns}
              dataSource={stockInRecords}
              rowKey="id"
              loading={loading}
              pagination={{
                current: stockInPage,
                pageSize: stockInPageSize,
                total: stockInTotal,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, size) => {
                  setStockInPage(page)
                  setStockInPageSize(size)
                }
              }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </TabPane>

        {/* 库存预警 */}
        <TabPane tab="库存预警" key="alerts">
          {/* 预警摘要 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="总预警数"
                  value={alertsSummary.total || 0}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="高危预警"
                  value={alertsSummary.high || 0}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="中危预警"
                  value={alertsSummary.medium || 0}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="低危预警"
                  value={alertsSummary.low || 0}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
          </Row>

          <Card>
            <div style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} sm={12} md={8}>
                  <Alert
                    message="预警提示"
                    description="系统会自动检查库存状态并生成预警信息，请及时处理库存不足的商品。"
                    type="info"
                    showIcon
                  />
                </Col>
                <Col xs={24} sm={12} md={16}>
                  <Space wrap style={{ float: 'right' }}>
                    <Button
                      type="primary"
                      icon={<WarningOutlined />}
                      onClick={handleTriggerAlertCheck}
                    >
                      手动检查预警
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => {
                        loadAlerts()
                        loadAlertsSummary()
                      }}
                    >
                      刷新
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            <Divider />

            <Table
              columns={alertColumns}
              dataSource={alerts}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 新增入库模态框 */}
      <Modal
        title="新增入库记录"
        open={isStockInModalVisible}
        onCancel={handleCloseStockInModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateStockIn}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="product_id"
                label="选择商品"
                rules={[{ required: true, message: '请选择商品' }]}
              >
                <Select
                  placeholder="请选择商品"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {inventoryList.map(item => (
                    <Option key={item.product_id} value={item.product_id}>
                      {item.product?.name} ({item.product?.barcode})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="quantity"
                label="入库数量"
                rules={[{ required: true, message: '请输入入库数量' }]}
              >
                <InputNumber
                  min={1}
                  placeholder="请输入数量"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unit_cost"
                label="单价"
                rules={[{ required: true, message: '请输入单价' }]}
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  placeholder="0.00"
                  style={{ width: '100%' }}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="supplier"
                label="供应商"
              >
                <Input placeholder="请输入供应商名称" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建入库记录
              </Button>
              <Button onClick={handleCloseStockInModal}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Inventory
