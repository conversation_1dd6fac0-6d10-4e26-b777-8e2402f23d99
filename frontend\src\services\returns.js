/**
 * 退货管理 API 服务
 */
import { apiRequest } from './api'

export const returnsService = {
  // 获取退货申请列表
  getReturnRequests: async (params = {}) => {
    const { page = 1, limit = 10, status, reason, startDate, endDate, search } = params
    return apiRequest.get('/returns', {
      page,
      limit,
      status,
      reason,
      startDate,
      endDate,
      search
    })
  },

  // 获取退货申请详情
  getReturnRequest: async (id) => {
    return apiRequest.get(`/returns/${id}`)
  },

  // 创建退货申请
  createReturnRequest: async (data) => {
    return apiRequest.post('/returns', data)
  },

  // 更新退货申请
  updateReturnRequest: async (id, data) => {
    return apiRequest.put(`/returns/${id}`, data)
  },

  // 审核退货申请
  reviewReturnRequest: async (id, reviewData) => {
    return apiRequest.post(`/returns/${id}/review`, reviewData)
  },

  // 批量审核退货申请
  batchReviewReturnRequests: async (ids, reviewData) => {
    return apiRequest.post('/returns/batch-review', { ids, ...reviewData })
  },

  // 自动审核退货申请
  autoReviewReturnRequest: async (id) => {
    return apiRequest.post(`/returns/${id}/auto-review`)
  },

  // 批量自动审核
  batchAutoReview: async (ids) => {
    return apiRequest.post('/returns/batch-auto-review', { ids })
  },

  // 获取AI分析结果
  getAIAnalysis: async (id) => {
    return apiRequest.get(`/returns/${id}/ai-analysis`)
  },

  // 应用AI建议
  applyAISuggestions: async (id) => {
    return apiRequest.post(`/returns/${id}/apply-ai-suggestions`)
  },

  // 获取退货统计信息
  getReturnStatistics: async (params = {}) => {
    const { period = 'month', startDate, endDate } = params
    return apiRequest.get('/returns/statistics', {
      period,
      startDate,
      endDate
    })
  },

  // 获取退货原因统计
  getReturnReasonStats: async () => {
    return apiRequest.get('/returns/reason-stats')
  },

  // 导出退货数据
  exportReturnData: async (params = {}) => {
    return apiRequest.get('/returns/export', params)
  },

  // 上传退货相关文件
  uploadReturnFile: async (file, returnId) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('returnId', returnId)
    return apiRequest.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取退货处理模板
  getReturnTemplates: async () => {
    return apiRequest.get('/returns/templates')
  },

  // 创建退货处理模板
  createReturnTemplate: async (template) => {
    return apiRequest.post('/returns/templates', template)
  },

  // 更新退货处理模板
  updateReturnTemplate: async (id, template) => {
    return apiRequest.put(`/returns/templates/${id}`, template)
  },

  // 删除退货处理模板
  deleteReturnTemplate: async (id) => {
    return apiRequest.delete(`/returns/templates/${id}`)
  }
}

export default returnsService
