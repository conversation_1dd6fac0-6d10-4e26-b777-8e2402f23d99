/* 主应用样式 */
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-content {
  flex: 1;
  padding: 24px;
  background: #f7f9fc;
  overflow-y: auto;
}

/* 登录页面样式 */
.login-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form-wrapper {
  background: white;
  padding: 48px;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
}

.login-logo {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo h1 {
  color: #2C7AFF;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
}

.login-logo p {
  color: #667085;
  font-size: 14px;
}

.login-form .ant-form-item {
  margin-bottom: 20px;
}

.login-form .ant-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
}

/* 页面标题样式 */
.page-header {
  background: white;
  padding: 24px;
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.page-header h1 {
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.page-header p {
  color: #667085;
  margin: 8px 0 0 0;
  font-size: 14px;
}

/* 卡片样式 */
.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.content-card .ant-card-body {
  padding: 24px;
}

/* 统计卡片 */
.stats-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.stats-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 16px;
}

.stats-card-icon.primary {
  background: #EFF6FF;
  color: #2C7AFF;
}

.stats-card-icon.success {
  background: #F0FDF4;
  color: #22C55E;
}

.stats-card-icon.warning {
  background: #FFFBEB;
  color: #FFAA2C;
}

.stats-card-icon.error {
  background: #FEF2F2;
  color: #EF4444;
}

.stats-card-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-card-label {
  color: #667085;
  font-size: 14px;
  font-weight: 500;
}

.stats-card-change {
  font-size: 12px;
  font-weight: 500;
  margin-top: 8px;
}

.stats-card-change.positive {
  color: #22C55E;
}

.stats-card-change.negative {
  color: #EF4444;
}

/* 表格样式 */
.data-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.data-table .ant-table-thead > tr > th {
  background: #F7F9FC;
  color: #667085;
  font-weight: 600;
  border-bottom: 1px solid #E5E7EB;
}

.data-table .ant-table-tbody > tr:hover > td {
  background: #F7F9FC;
}

/* 搜索栏样式 */
.search-bar {
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.search-bar .ant-form-item {
  margin-bottom: 0;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .app-content {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .page-header h1 {
    font-size: 20px;
  }
  
  .login-form-wrapper {
    padding: 32px 24px;
    margin: 16px;
  }
  
  .stats-card {
    padding: 16px;
  }
  
  .stats-card-value {
    font-size: 24px;
  }
  
  .search-bar {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .ant-btn {
    width: 100%;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
