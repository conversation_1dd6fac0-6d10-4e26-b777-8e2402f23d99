# 服务器配置
PORT=4000
NODE_ENV=development

# 数据库配置
MONGO_URI=mongodb+srv://username:<EMAIL>/inventory_ai_db?retryWrites=true&w=majority

# JWT 认证配置
JWT_SECRET=your_super_secret_jwt_key_here_replace_in_production
JWT_EXPIRES_IN=7d

# AWS S3 配置
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=inventory-assets

# DeepSeek AI 配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://llm.chutes.ai/v1
DEEPSEEK_MODEL=deepseek-ai/DeepSeek-V3-0324
AI_MOCK_MODE=false

# 前端配置
FRONTEND_URL=http://localhost:5173
CORS_ORIGIN=http://localhost:5173

# 邮件配置（用于周报发送）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# 文件上传配置
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 缓存配置（可选）
REDIS_URL=redis://localhost:6379

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 生产环境安全配置
ENABLE_HTTPS_REDIRECT=false
TRUST_PROXY=false
SESSION_SECURE=false

# 退货政策配置文件路径
REFUND_POLICY_PATH=../refund_policy.yaml
