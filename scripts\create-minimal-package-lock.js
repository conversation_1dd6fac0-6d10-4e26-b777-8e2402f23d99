#!/usr/bin/env node

/**
 * 创建最小化的package-lock.json文件
 * 用于解决Render部署时的依赖同步问题
 */

const fs = require('fs');
const path = require('path');

// 读取前端package.json
function createFrontendPackageLock() {
  const frontendDir = path.join(process.cwd(), 'frontend');
  const packageJsonPath = path.join(frontendDir, 'package.json');
  const packageLockPath = path.join(frontendDir, 'package-lock.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ frontend/package.json 不存在');
    return false;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // 创建最小化的package-lock.json
    const packageLock = {
      "name": packageJson.name,
      "version": packageJson.version,
      "lockfileVersion": 3,
      "requires": true,
      "packages": {
        "": {
          "name": packageJson.name,
          "version": packageJson.version,
          "dependencies": packageJson.dependencies || {},
          "devDependencies": packageJson.devDependencies || {},
          "engines": packageJson.engines || {}
        }
      }
    };
    
    // 为每个依赖创建基本条目
    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };
    
    Object.keys(allDeps).forEach(depName => {
      const version = allDeps[depName].replace(/[\^~]/, '');
      packageLock.packages[`node_modules/${depName}`] = {
        "version": version,
        "resolved": `https://registry.npmjs.org/${depName}/-/${depName}-${version}.tgz`,
        "integrity": "sha512-placeholder"
      };
    });
    
    fs.writeFileSync(packageLockPath, JSON.stringify(packageLock, null, 2));
    console.log('✅ 创建前端 package-lock.json 成功');
    return true;
    
  } catch (error) {
    console.error(`❌ 创建前端 package-lock.json 失败: ${error.message}`);
    return false;
  }
}

// 主函数
function main() {
  console.log('🔧 创建最小化 package-lock.json 文件...');
  
  const success = createFrontendPackageLock();
  
  if (success) {
    console.log('✅ 完成！现在可以提交代码并重新部署');
    console.log('📋 建议的命令:');
    console.log('git add .');
    console.log('git commit -m "Add minimal package-lock.json for Render deployment"');
    console.log('git push origin main');
  } else {
    console.log('❌ 创建失败，请手动处理');
  }
}

if (require.main === module) {
  main();
}
