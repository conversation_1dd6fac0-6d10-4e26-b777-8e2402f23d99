# 第四阶段API文档

本文档详细描述了第四阶段新增的19个API端点，包含报表管理、盘点管理、智能预警和退货自动化功能。

## 📊 报表管理API

### 1. 生成报表

**端点**: `POST /api/reports/generate`  
**权限**: `reports.create`  
**描述**: 生成周报、月报或自定义报表

**请求体**:
```json
{
  "report_type": "weekly",
  "start_date": "2025-06-08T00:00:00.000Z",
  "end_date": "2025-06-14T23:59:59.999Z",
  "title": "库存管理周报",
  "description": "系统自动生成的库存管理周报",
  "filters": {
    "categories": ["食品", "饮料"],
    "location": "仓库A"
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "报表生成成功",
  "data": {
    "report": {
      "report_id": "WR25061412345",
      "report_type": "weekly",
      "title": "库存管理周报",
      "status": "completed",
      "generated_at": "2025-06-15T08:00:00.000Z",
      "generation_time": 1500,
      "data_sections": [
        {
          "section": "summary",
          "title": "总览摘要",
          "data": {...},
          "charts": [...]
        }
      ]
    }
  }
}
```

### 2. 获取报表列表

**端点**: `GET /api/reports`  
**权限**: `reports.read`  
**描述**: 获取报表列表，支持分页和筛选

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `report_type`: 报表类型（weekly/monthly/custom）
- `status`: 状态（generating/completed/failed）
- `start_date`: 开始日期
- `end_date`: 结束日期
- `sort_by`: 排序字段（默认generated_at）
- `sort_order`: 排序方向（asc/desc，默认desc）

**响应**:
```json
{
  "success": true,
  "data": {
    "reports": [
      {
        "report_id": "WR25061412345",
        "report_type": "weekly",
        "title": "库存管理周报",
        "status": "completed",
        "generated_at": "2025-06-15T08:00:00.000Z",
        "generated_by": {
          "username": "admin",
          "name": "管理员"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total_items": 50,
      "total_pages": 3
    }
  }
}
```

### 3. 获取报表详情

**端点**: `GET /api/reports/:id`  
**权限**: `reports.read`  
**描述**: 获取指定报表的详细信息

**响应**:
```json
{
  "success": true,
  "data": {
    "report": {
      "report_id": "WR25061412345",
      "report_type": "weekly",
      "title": "库存管理周报",
      "description": "系统自动生成的库存管理周报",
      "period": {
        "start_date": "2025-06-08T00:00:00.000Z",
        "end_date": "2025-06-14T23:59:59.999Z"
      },
      "status": "completed",
      "data_sections": [
        {
          "section": "summary",
          "title": "总览摘要",
          "data": {
            "overview": {
              "total_products": 8,
              "total_inventory_value": 15000,
              "stock_in_count": 5,
              "return_requests": 2
            }
          },
          "charts": [
            {
              "chart_type": "metric",
              "title": "核心指标",
              "data": {
                "metrics": [
                  {"label": "产品总数", "value": 8, "unit": "个"},
                  {"label": "库存总值", "value": 15000, "unit": "元"}
                ]
              }
            }
          ]
        }
      ],
      "summary": {
        "total_products": 8,
        "total_stock_value": 15000,
        "stock_in_count": 5,
        "return_requests": 2
      },
      "generated_by": {
        "username": "admin",
        "name": "管理员"
      },
      "generated_at": "2025-06-15T08:00:00.000Z",
      "generation_time_ms": 1500
    }
  }
}
```

### 4. 删除报表

**端点**: `DELETE /api/reports/:id`  
**权限**: `reports.delete`  
**描述**: 删除指定报表（只有生成者或管理员可删除）

**响应**:
```json
{
  "success": true,
  "message": "报表删除成功"
}
```

### 5. 获取报表模板

**端点**: `GET /api/reports/templates/list`  
**权限**: `reports.read`  
**描述**: 获取可用的报表模板列表

**响应**:
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "weekly_standard",
        "name": "标准周报",
        "type": "weekly",
        "description": "包含库存状态、出入库统计、预警信息的标准周报",
        "sections": ["summary", "inventory", "stock_movement", "returns", "alerts", "performance"]
      },
      {
        "id": "monthly_comprehensive",
        "name": "综合月报",
        "type": "monthly",
        "description": "全面的月度报表，包含详细的趋势分析和对比",
        "sections": ["summary", "inventory", "stock_movement", "returns", "alerts", "performance", "trends"]
      }
    ]
  }
}
```

## 📋 盘点管理API

### 1. 创建盘点计划

**端点**: `POST /api/stock-count/plans`  
**权限**: `stockcount.create`  
**描述**: 创建新的盘点计划

**请求体**:
```json
{
  "title": "仓库A食品类盘点",
  "description": "对仓库A的食品类产品进行全面盘点",
  "count_type": "partial",
  "scheduled_date": "2025-06-20T08:00:00.000Z",
  "deadline": "2025-06-20T18:00:00.000Z",
  "location": "仓库A",
  "categories": ["食品", "饮料"],
  "product_ids": [],
  "settings": {
    "allow_negative_count": false,
    "require_verification": true,
    "auto_adjust_inventory": false,
    "tolerance_percentage": 5
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "盘点计划创建成功",
  "data": {
    "stockCount": {
      "count_id": "PC25062012345",
      "title": "仓库A食品类盘点",
      "count_type": "partial",
      "status": "planned",
      "scheduled_date": "2025-06-20T08:00:00.000Z",
      "items": [
        {
          "product_id": "66d1234567890abcdef12345",
          "expected_quantity": 100,
          "actual_quantity": null,
          "status": "pending"
        }
      ],
      "summary": {
        "total_items": 5,
        "counted_items": 0,
        "verified_items": 0,
        "discrepancy_items": 0
      }
    }
  }
}
```

### 2. 获取盘点计划列表

**端点**: `GET /api/stock-count/plans`  
**权限**: `stockcount.read`  
**描述**: 获取盘点计划列表

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `count_type`: 盘点类型（full/partial/cycle/spot）
- `status`: 状态（planned/in_progress/completed/cancelled）
- `location`: 位置
- `assigned_to_me`: 是否只显示分配给我的（true/false）

**响应**:
```json
{
  "success": true,
  "data": {
    "stockCounts": [
      {
        "count_id": "PC25062012345",
        "title": "仓库A食品类盘点",
        "count_type": "partial",
        "status": "planned",
        "scheduled_date": "2025-06-20T08:00:00.000Z",
        "created_by": {
          "username": "admin",
          "name": "管理员"
        },
        "assigned_to": [
          {
            "user_id": {
              "username": "counter1",
              "name": "盘点员1"
            },
            "role": "counter",
            "status": "assigned"
          }
        ]
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total_items": 10,
      "total_pages": 1
    }
  }
}
```

### 3. 获取盘点计划详情

**端点**: `GET /api/stock-count/plans/:id`  
**权限**: `stockcount.read`  
**描述**: 获取指定盘点计划的详细信息

### 4. 分配盘点任务

**端点**: `POST /api/stock-count/plans/:id/assign`  
**权限**: `stockcount.assign`  
**描述**: 分配盘点任务给用户

**请求体**:
```json
{
  "assignments": [
    {
      "user_id": "66d1234567890abcdef12345",
      "role": "counter"
    },
    {
      "user_id": "66d1234567890abcdef12346",
      "role": "verifier"
    }
  ]
}
```

### 5. 开始盘点

**端点**: `POST /api/stock-count/plans/:id/start`  
**权限**: `stockcount.execute`  
**描述**: 开始执行盘点任务

### 6. 提交盘点结果

**端点**: `POST /api/stock-count/plans/:id/submit`  
**权限**: `stockcount.execute`  
**描述**: 提交盘点结果

**请求体**:
```json
{
  "count_results": [
    {
      "item_index": 0,
      "actual_quantity": 98,
      "notes": "包装破损2个"
    },
    {
      "item_index": 1,
      "actual_quantity": 50,
      "notes": ""
    }
  ]
}
```

### 7. 获取盘点建议

**端点**: `GET /api/stock-count/recommendations`  
**权限**: `stockcount.read`  
**描述**: 获取系统生成的盘点建议

**查询参数**:
- `days_since_last_count`: 距离上次盘点天数（默认90）
- `low_stock_threshold`: 低库存阈值（默认0.2）
- `high_value_threshold`: 高价值阈值（默认1000）

**响应**:
```json
{
  "success": true,
  "data": {
    "high_priority": [
      {
        "product_id": "66d1234567890abcdef12345",
        "product_name": "高价值商品A",
        "category": "食品",
        "current_stock": 10,
        "total_value": 1500,
        "last_count_date": "2025-03-15T00:00:00.000Z",
        "reasons": ["高价值产品", "90天未盘点"],
        "estimated_time_minutes": 8
      }
    ],
    "medium_priority": [],
    "low_priority": [],
    "summary": {
      "total_recommendations": 5,
      "estimated_time_hours": 2
    }
  }
}
```

## 🧠 智能预警增强API

### 1. 获取智能预警分析

**端点**: `GET /api/alerts/intelligent-analysis`
**权限**: `alerts.read`
**描述**: 获取基于历史数据的智能预警分析

**查询参数**:
- `days_back`: 回溯天数（默认30）
- `prediction_days`: 预测天数（默认7）
- `include_trends`: 包含趋势分析（默认true）
- `include_recommendations`: 包含建议（默认true）

**响应**:
```json
{
  "success": true,
  "data": {
    "trends": {
      "66d1234567890abcdef12345": {
        "total_quantity": 150,
        "avg_daily_quantity": 5,
        "trend_direction": "increasing",
        "trend_strength": 2.5,
        "volatility": 0.15
      }
    },
    "consumption_patterns": {
      "食品": {
        "total_stock": 500,
        "avg_stock": 62.5,
        "products_count": 8,
        "low_stock_ratio": 0.25,
        "consumption_intensity": "medium",
        "risk_level": "medium"
      }
    },
    "seasonal_analysis": {
      "current_season": "summer",
      "current_factor": 1.2,
      "seasonal_factors": {
        "spring": {"factor": 1.0, "description": "春季正常消费"},
        "summer": {"factor": 1.2, "description": "夏季消费增加"}
      }
    },
    "predictions": {
      "66d1234567890abcdef12345": {
        "product_name": "商品A",
        "category": "食品",
        "current_stock": 50,
        "predicted_stock": 15,
        "daily_consumption": 5,
        "days_until_reorder": 6,
        "stockout_risk": "medium",
        "recommended_action": "准备补货"
      }
    },
    "risk_assessment": {
      "overall_risk_score": 25.5,
      "overall_risk_level": "medium",
      "high_risk_items": 2,
      "medium_risk_items": 5,
      "low_risk_items": 15,
      "risk_details": [
        {
          "product_id": "66d1234567890abcdef12345",
          "product_name": "商品A",
          "risk_score": 45,
          "risk_level": "high",
          "risk_factors": ["库存不足", "即将过期"]
        }
      ]
    },
    "recommendations": [
      {
        "type": "urgent",
        "priority": "high",
        "title": "高风险库存预警",
        "description": "检测到多个高风险库存项目，建议立即采取行动",
        "actions": [
          "优先处理高风险商品",
          "加快补货流程"
        ]
      }
    ],
    "generated_at": "2025-06-15T10:00:00.000Z"
  }
}
```

### 2. 获取库存趋势分析

**端点**: `GET /api/alerts/trends`
**权限**: `alerts.read`
**描述**: 获取库存变化趋势分析

**查询参数**:
- `days_back`: 回溯天数（默认30）

**响应**:
```json
{
  "success": true,
  "data": {
    "trends": {
      "66d1234567890abcdef12345": {
        "total_quantity": 150,
        "avg_daily_quantity": 5,
        "trend_direction": "increasing",
        "trend_strength": 2.5,
        "volatility": 0.15,
        "daily_data": [
          {"date": "2025-06-01", "quantity": 10},
          {"date": "2025-06-02", "quantity": 15}
        ]
      }
    },
    "analysis_period": 30,
    "generated_at": "2025-06-15T10:00:00.000Z"
  }
}
```

### 3. 获取库存预测

**端点**: `GET /api/alerts/predictions`
**权限**: `alerts.read`
**描述**: 获取基于消费模式的库存预测

**查询参数**:
- `prediction_days`: 预测天数（默认7）
- `days_back`: 分析回溯天数（默认30）

**响应**:
```json
{
  "success": true,
  "data": {
    "predictions": {
      "66d1234567890abcdef12345": {
        "product_name": "商品A",
        "category": "食品",
        "current_stock": 50,
        "predicted_stock": 15,
        "daily_consumption": 5,
        "days_until_reorder": 6,
        "stockout_risk": "medium",
        "recommended_action": "准备补货"
      }
    },
    "consumption_patterns": {
      "食品": {
        "consumption_intensity": "medium",
        "risk_level": "medium"
      }
    },
    "prediction_period": 7,
    "analysis_period": 30,
    "generated_at": "2025-06-15T10:00:00.000Z"
  }
}
```

### 4. 获取风险评估

**端点**: `GET /api/alerts/risk-assessment`
**权限**: `alerts.read`
**描述**: 获取综合风险评估结果

**响应**:
```json
{
  "success": true,
  "data": {
    "risk_assessment": {
      "overall_risk_score": 25.5,
      "overall_risk_level": "medium",
      "high_risk_items": 2,
      "medium_risk_items": 5,
      "low_risk_items": 15,
      "risk_details": [
        {
          "product_id": "66d1234567890abcdef12345",
          "product_name": "商品A",
          "category": "食品",
          "risk_score": 45,
          "risk_level": "high",
          "risk_factors": ["库存不足", "即将过期"],
          "current_stock": 10,
          "total_value": 1500
        }
      ],
      "recommendations": [
        "立即检查高风险商品库存",
        "加快补货流程"
      ]
    },
    "generated_at": "2025-06-15T10:00:00.000Z"
  }
}
```

## 🤖 退货自动化API

### 1. 批量审核退货申请

**端点**: `POST /api/returns/batch-review`
**权限**: `returns.approve`
**描述**: 批量审核多个退货申请

**请求体**:
```json
{
  "request_ids": [
    "66d1234567890abcdef12345",
    "66d1234567890abcdef12346"
  ],
  "action": "approve",
  "refund_method": "original",
  "notes": "批量审核通过"
}
```

**响应**:
```json
{
  "success": true,
  "message": "批量审核完成: 2/2成功",
  "data": {
    "success": [
      {
        "id": "66d1234567890abcdef12345",
        "request_number": "RET001",
        "status": "approved"
      }
    ],
    "failed": [],
    "total": 2
  }
}
```

### 2. 配置自动化审核规则

**端点**: `POST /api/returns/automation/configure`
**权限**: `returns.admin`
**描述**: 配置自动化审核规则

**请求体**:
```json
{
  "rules": [
    {
      "name": "高置信度自动通过",
      "conditions": {
        "ai_confidence": {"$gte": 0.9},
        "ai_decision": "approve",
        "risk_level": {"$in": ["low", "medium"]},
        "total_amount": {"$lte": 500}
      },
      "action": "auto_approve",
      "priority": 1
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "message": "自动化规则配置成功",
  "data": {
    "automationConfig": {
      "rules": [...],
      "updated_by": "66d1234567890abcdef12345",
      "updated_at": "2025-06-15T10:00:00.000Z",
      "enabled": true
    }
  }
}
```

### 3. 执行自动化审核

**端点**: `POST /api/returns/automation/execute`
**权限**: `returns.admin`
**描述**: 执行自动化审核流程

**查询参数**:
- `dry_run`: 是否为模拟运行（默认false）

**响应**:
```json
{
  "success": true,
  "message": "自动化审核完成",
  "data": {
    "processed": 10,
    "auto_approved": 3,
    "auto_rejected": 2,
    "manual_review": 5,
    "failed": 0,
    "details": [
      {
        "request_number": "RET001",
        "decision": "auto_approve",
        "reason": "高置信度自动通过",
        "confidence": 0.95
      }
    ]
  }
}
```

## 📝 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {...}
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total_items": 100,
      "total_pages": 5
    }
  }
}
```

## 🔐 权限说明

- `reports.create`: 创建报表权限
- `reports.read`: 查看报表权限
- `reports.delete`: 删除报表权限
- `stockcount.create`: 创建盘点计划权限
- `stockcount.read`: 查看盘点权限
- `stockcount.assign`: 分配盘点任务权限
- `stockcount.execute`: 执行盘点权限
- `alerts.read`: 查看预警权限
- `returns.approve`: 审核退货权限
- `returns.admin`: 退货管理员权限

## 📊 状态码说明

- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 🔄 请求限制

- 报表生成: 每用户每小时最多10次
- 批量操作: 每次最多处理100个项目
- 智能分析: 每用户每分钟最多5次
- 自动化审核: 每次最多处理100个申请
```
