const policyService = require('../services/policyService');
const { OperationLog } = require('../models');
const logger = require('../utils/logger');

/**
 * 政策管理控制器
 * 处理政策相关的HTTP请求
 */
class PolicyController {
  /**
   * 获取所有政策
   */
  async getAllPolicies(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        category,
        active_only = 'true',
        include_versions = 'false',
        limit = '50',
        page = '1'
      } = req.query;

      const options = {
        category: category || null,
        activeOnly: active_only === 'true',
        includeVersions: include_versions === 'true',
        limit: parseInt(limit),
        page: parseInt(page)
      };

      const result = await policyService.getAllPolicies(options);
      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        req.user?.id,
        'policy.list',
        'policy',
        null,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: '获取政策列表'
        }
      );

      res.json({
        success: true,
        data: result,
        response_time_ms: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('获取政策列表失败:', error);

      await OperationLog.logError(
        req.user?.id,
        'policy.list',
        'policy',
        null,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          description: '获取政策列表失败'
        },
        error
      );

      res.status(500).json({
        success: false,
        message: '获取政策列表失败',
        error: error.message
      });
    }
  }

  /**
   * 根据ID获取政策详情
   */
  async getPolicyById(req, res) {
    const startTime = Date.now();
    
    try {
      const { policy_id } = req.params;
      const { include_versions = 'false' } = req.query;

      if (!policy_id) {
        return res.status(400).json({
          success: false,
          message: '政策ID是必需的'
        });
      }

      const policy = await policyService.getPolicyById(
        policy_id, 
        include_versions === 'true'
      );

      if (!policy) {
        return res.status(404).json({
          success: false,
          message: '政策不存在'
        });
      }

      // 增加使用计数
      await policyService.incrementUsage(policy_id);

      const responseTime = Date.now() - startTime;

      await OperationLog.logSuccess(
        req.user?.id,
        'policy.detail',
        'policy',
        policy._id.toString(),
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `获取政策详情: ${policy_id}`
        }
      );

      res.json({
        success: true,
        data: policy,
        response_time_ms: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('获取政策详情失败:', error);

      await OperationLog.logError(
        req.user?.id,
        'policy.detail',
        'policy',
        null,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          description: '获取政策详情失败'
        },
        error
      );

      res.status(500).json({
        success: false,
        message: '获取政策详情失败',
        error: error.message
      });
    }
  }

  /**
   * 搜索政策
   */
  async searchPolicies(req, res) {
    const startTime = Date.now();
    
    try {
      const { keyword, category, active_only = 'true', limit = '20' } = req.query;

      if (!keyword) {
        return res.status(400).json({
          success: false,
          message: '搜索关键词是必需的'
        });
      }

      const options = {
        category: category || null,
        activeOnly: active_only === 'true',
        limit: parseInt(limit)
      };

      const policies = await policyService.searchPolicies(keyword, options);
      const responseTime = Date.now() - startTime;

      await OperationLog.logSuccess(
        req.user?.id,
        'policy.search',
        'policy',
        null,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `搜索政策: ${keyword}`
        }
      );

      res.json({
        success: true,
        data: {
          keyword,
          results: policies,
          count: policies.length
        },
        response_time_ms: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('搜索政策失败:', error);

      await OperationLog.logError(
        req.user?.id,
        'policy.search',
        'policy',
        null,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          description: '搜索政策失败'
        },
        error
      );

      res.status(500).json({
        success: false,
        message: '搜索政策失败',
        error: error.message
      });
    }
  }

  /**
   * 创建新政策
   */
  async createPolicy(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        name,
        category,
        description,
        current_content,
        tags = [],
        keywords = [],
        priority = 5,
        effective_date,
        expiry_date
      } = req.body;

      // 验证必需字段
      if (!name || !category || !current_content) {
        return res.status(400).json({
          success: false,
          message: '政策名称、类别和内容是必需的'
        });
      }

      const policyData = {
        name,
        category,
        description,
        current_content,
        current_version: '1.0.0',
        tags,
        keywords,
        priority,
        effective_date: effective_date ? new Date(effective_date) : new Date(),
        expiry_date: expiry_date ? new Date(expiry_date) : null
      };

      const policy = await policyService.createPolicy(policyData, req.user.id);
      const responseTime = Date.now() - startTime;

      await OperationLog.logSuccess(
        req.user.id,
        'policy.create',
        'policy',
        policy._id.toString(),
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          description: `创建政策: ${name}`
        }
      );

      res.status(201).json({
        success: true,
        data: policy,
        message: '政策创建成功',
        response_time_ms: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('创建政策失败:', error);

      await OperationLog.logError(
        req.user?.id,
        'policy.create',
        'policy',
        null,
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          description: '创建政策失败'
        },
        error
      );

      res.status(500).json({
        success: false,
        message: '创建政策失败',
        error: error.message
      });
    }
  }

  /**
   * 更新政策
   */
  async updatePolicy(req, res) {
    const startTime = Date.now();
    
    try {
      const { policy_id } = req.params;
      const { change_summary = '', ...updateData } = req.body;

      if (!policy_id) {
        return res.status(400).json({
          success: false,
          message: '政策ID是必需的'
        });
      }

      const policy = await policyService.updatePolicy(
        policy_id,
        updateData,
        req.user.id,
        change_summary
      );

      const responseTime = Date.now() - startTime;

      await OperationLog.logSuccess(
        req.user.id,
        'policy.update',
        'policy',
        policy._id.toString(),
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `更新政策: ${policy_id}`
        }
      );

      res.json({
        success: true,
        data: policy,
        message: '政策更新成功',
        response_time_ms: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('更新政策失败:', error);

      await OperationLog.logError(
        req.user?.id,
        'policy.update',
        'policy',
        null,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          description: '更新政策失败'
        },
        error
      );

      res.status(500).json({
        success: false,
        message: '更新政策失败',
        error: error.message
      });
    }
  }

  /**
   * 删除政策
   */
  async deletePolicy(req, res) {
    const startTime = Date.now();
    
    try {
      const { policy_id } = req.params;

      if (!policy_id) {
        return res.status(400).json({
          success: false,
          message: '政策ID是必需的'
        });
      }

      const success = await policyService.deletePolicy(policy_id);

      if (!success) {
        return res.status(404).json({
          success: false,
          message: '政策不存在'
        });
      }

      const responseTime = Date.now() - startTime;

      await OperationLog.logSuccess(
        req.user.id,
        'policy.delete',
        'policy',
        null,
        {
          action: 'delete',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `删除政策: ${policy_id}`
        }
      );

      res.json({
        success: true,
        message: '政策删除成功',
        response_time_ms: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('删除政策失败:', error);

      await OperationLog.logError(
        req.user?.id,
        'policy.delete',
        'policy',
        null,
        {
          action: 'delete',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          description: '删除政策失败'
        },
        error
      );

      res.status(500).json({
        success: false,
        message: '删除政策失败',
        error: error.message
      });
    }
  }

  /**
   * 从policy.json导入政策
   */
  async importPolicies(req, res) {
    const startTime = Date.now();
    
    try {
      const result = await policyService.importFromFile(req.user.id);
      const responseTime = Date.now() - startTime;

      await OperationLog.logSuccess(
        req.user.id,
        'policy.import',
        'policy',
        null,
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: '导入政策文件'
        }
      );

      res.json({
        success: true,
        data: result,
        message: '政策导入完成',
        response_time_ms: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('导入政策失败:', error);

      await OperationLog.logError(
        req.user?.id,
        'policy.import',
        'policy',
        null,
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          description: '导入政策失败'
        },
        error
      );

      res.status(500).json({
        success: false,
        message: '导入政策失败',
        error: error.message
      });
    }
  }

  /**
   * 获取政策统计信息
   */
  async getStatistics(req, res) {
    const startTime = Date.now();
    
    try {
      const stats = await policyService.getStatistics();
      const responseTime = Date.now() - startTime;

      res.json({
        success: true,
        data: stats,
        response_time_ms: responseTime
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('获取政策统计失败:', error);

      res.status(500).json({
        success: false,
        message: '获取政策统计失败',
        error: error.message
      });
    }
  }
}

module.exports = new PolicyController();
