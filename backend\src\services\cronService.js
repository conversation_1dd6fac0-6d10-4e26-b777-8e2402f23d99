const cron = require('node-cron');
const alertService = require('./alertService');
const logger = require('../utils/logger');

/**
 * 定时任务服务类
 * 管理所有定时任务，包括预警检查、数据清理等
 */
class CronService {
  constructor() {
    this.tasks = new Map();
    this.isRunning = false;

    // 任务配置
    this.taskConfigs = {
      alertCheck: {
        name: '预警检查任务',
        schedule: '0 */2 * * *', // 每2小时执行一次
        enabled: true,
        handler: this.runAlertCheck.bind(this)
      },
      dailyAlertCheck: {
        name: '每日预警检查',
        schedule: '0 9 * * *', // 每天上午9点执行
        enabled: true,
        handler: this.runDailyAlertCheck.bind(this)
      },
      weeklyReport: {
        name: '周报生成任务',
        schedule: '0 10 * * 1', // 每周一上午10点执行
        enabled: false, // 暂时禁用，第四阶段实现
        handler: this.runWeeklyReport.bind(this)
      },
      dataCleanup: {
        name: '数据清理任务',
        schedule: '0 2 * * 0', // 每周日凌晨2点执行
        enabled: true,
        handler: this.runDataCleanup.bind(this)
      }
    };

    logger.info('CronService初始化完成', {
      totalTasks: Object.keys(this.taskConfigs).length,
      enabledTasks: Object.values(this.taskConfigs).filter(task => task.enabled).length
    });
  }

  /**
   * 启动所有定时任务
   */
  start() {
    try {
      if (this.isRunning) {
        logger.warn('定时任务服务已在运行');
        return;
      }

      logger.info('启动定时任务服务');

      Object.entries(this.taskConfigs).forEach(([taskId, config]) => {
        if (config.enabled) {
          this.scheduleTask(taskId, config);
        }
      });

      this.isRunning = true;
      logger.info('定时任务服务启动成功', {
        runningTasks: this.tasks.size
      });

    } catch (error) {
      logger.error('启动定时任务服务失败', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * 停止所有定时任务
   */
  stop() {
    try {
      if (!this.isRunning) {
        logger.warn('定时任务服务未在运行');
        return;
      }

      logger.info('停止定时任务服务');

      this.tasks.forEach((task, taskId) => {
        task.destroy();
        logger.info(`任务 ${taskId} 已停止`);
      });

      this.tasks.clear();
      this.isRunning = false;

      logger.info('定时任务服务已停止');

    } catch (error) {
      logger.error('停止定时任务服务失败', {
        error: error.message
      });
    }
  }

  /**
   * 调度单个任务
   * @param {string} taskId - 任务ID
   * @param {Object} config - 任务配置
   */
  scheduleTask(taskId, config) {
    try {
      if (this.tasks.has(taskId)) {
        logger.warn(`任务 ${taskId} 已存在，跳过调度`);
        return;
      }

      const task = cron.schedule(config.schedule, async () => {
        await this.executeTask(taskId, config);
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      });

      task.start();
      this.tasks.set(taskId, task);

      logger.info(`任务 ${taskId} 调度成功`, {
        name: config.name,
        schedule: config.schedule
      });

    } catch (error) {
      logger.error(`调度任务 ${taskId} 失败`, {
        error: error.message,
        config
      });
    }
  }

  /**
   * 执行任务
   * @param {string} taskId - 任务ID
   * @param {Object} config - 任务配置
   */
  async executeTask(taskId, config) {
    const startTime = Date.now();
    
    try {
      logger.info(`开始执行任务: ${config.name}`, { taskId });

      await config.handler();

      const duration = Date.now() - startTime;
      logger.info(`任务执行成功: ${config.name}`, {
        taskId,
        duration: `${duration}ms`
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`任务执行失败: ${config.name}`, {
        taskId,
        duration: `${duration}ms`,
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * 预警检查任务处理器
   */
  async runAlertCheck() {
    try {
      logger.info('执行预警检查任务');

      const result = await alertService.checkAllAlerts();

      if (result.success) {
        const { alerts, summary } = result.data;
        
        logger.info('预警检查完成', {
          totalAlerts: summary.total,
          critical: summary.critical,
          high: summary.high,
          medium: summary.medium
        });

        // 如果有紧急或高优先级预警，记录详细信息
        const urgentAlerts = alerts.filter(alert => 
          alert.priority === 'critical' || alert.priority === 'high'
        );

        if (urgentAlerts.length > 0) {
          logger.warn('发现紧急预警', {
            count: urgentAlerts.length,
            alerts: urgentAlerts.map(alert => ({
              type: alert.type,
              message: alert.message,
              priority: alert.priority
            }))
          });
        }

      } else {
        logger.error('预警检查失败', { error: result.error });
      }

    } catch (error) {
      logger.error('预警检查任务执行失败', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * 每日预警检查任务处理器
   */
  async runDailyAlertCheck() {
    try {
      logger.info('执行每日预警检查任务');

      const result = await alertService.checkAllAlerts();

      if (result.success) {
        const { alerts, summary } = result.data;
        
        // 生成每日预警报告
        const report = {
          date: new Date().toISOString().split('T')[0],
          summary,
          criticalAlerts: alerts.filter(alert => alert.priority === 'critical'),
          highAlerts: alerts.filter(alert => alert.priority === 'high'),
          totalValue: 0 // 可以后续计算受影响的库存价值
        };

        logger.info('每日预警报告生成完成', {
          date: report.date,
          totalAlerts: summary.total,
          criticalCount: report.criticalAlerts.length,
          highCount: report.highAlerts.length
        });

        // 这里可以添加发送通知的逻辑（如果需要的话）
        // 例如：保存到数据库、发送系统通知等

      } else {
        logger.error('每日预警检查失败', { error: result.error });
      }

    } catch (error) {
      logger.error('每日预警检查任务执行失败', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * 周报生成任务处理器（第四阶段实现）
   */
  async runWeeklyReport() {
    try {
      logger.info('执行周报生成任务');
      
      // 第四阶段实现周报生成逻辑
      logger.info('周报生成功能将在第四阶段实现');

    } catch (error) {
      logger.error('周报生成任务执行失败', {
        error: error.message
      });
    }
  }

  /**
   * 数据清理任务处理器
   */
  async runDataCleanup() {
    try {
      logger.info('执行数据清理任务');

      // 清理30天前的操作日志（保留重要操作）
      const { OperationLog } = require('../models');
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deleteResult = await OperationLog.deleteMany({
        created_at: { $lt: thirtyDaysAgo },
        operation_type: { $nin: ['user.login', 'user.logout', 'product.create', 'product.delete'] }
      });

      logger.info('数据清理完成', {
        deletedLogs: deleteResult.deletedCount,
        cutoffDate: thirtyDaysAgo.toISOString()
      });

    } catch (error) {
      logger.error('数据清理任务执行失败', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * 手动执行任务
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} 执行结果
   */
  async runTaskManually(taskId) {
    try {
      const config = this.taskConfigs[taskId];
      
      if (!config) {
        throw new Error(`任务 ${taskId} 不存在`);
      }

      logger.info(`手动执行任务: ${config.name}`, { taskId });

      await this.executeTask(taskId, config);

      return {
        success: true,
        message: `任务 ${config.name} 执行成功`
      };

    } catch (error) {
      logger.error(`手动执行任务失败`, {
        taskId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取任务状态
   * @returns {Object} 任务状态信息
   */
  getTaskStatus() {
    const status = {
      isRunning: this.isRunning,
      totalTasks: Object.keys(this.taskConfigs).length,
      runningTasks: this.tasks.size,
      tasks: {}
    };

    Object.entries(this.taskConfigs).forEach(([taskId, config]) => {
      status.tasks[taskId] = {
        name: config.name,
        schedule: config.schedule,
        enabled: config.enabled,
        isRunning: this.tasks.has(taskId)
      };
    });

    return status;
  }

  /**
   * 启用/禁用任务
   * @param {string} taskId - 任务ID
   * @param {boolean} enabled - 是否启用
   * @returns {Object} 操作结果
   */
  toggleTask(taskId, enabled) {
    try {
      const config = this.taskConfigs[taskId];
      
      if (!config) {
        throw new Error(`任务 ${taskId} 不存在`);
      }

      if (enabled && !this.tasks.has(taskId)) {
        // 启用任务
        this.scheduleTask(taskId, config);
        config.enabled = true;
        
        logger.info(`任务 ${config.name} 已启用`);
        
      } else if (!enabled && this.tasks.has(taskId)) {
        // 禁用任务
        const task = this.tasks.get(taskId);
        task.destroy();
        this.tasks.delete(taskId);
        config.enabled = false;
        
        logger.info(`任务 ${config.name} 已禁用`);
      }

      return {
        success: true,
        message: `任务 ${config.name} ${enabled ? '已启用' : '已禁用'}`
      };

    } catch (error) {
      logger.error(`切换任务状态失败`, {
        taskId,
        enabled,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new CronService();
