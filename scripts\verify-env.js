#!/usr/bin/env node

/**
 * 环境变量配置验证脚本
 * 用于检查项目的环境变量配置是否正确
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  if (exists) {
    log(`✅ ${description}: ${filePath}`, 'green');
  } else {
    log(`❌ ${description}: ${filePath} (文件不存在)`, 'red');
  }
  return exists;
}

function loadEnvFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    
    content.split('\n').forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return env;
  } catch (error) {
    log(`❌ 无法读取环境变量文件: ${filePath}`, 'red');
    return null;
  }
}

function checkRequiredVars(env, requiredVars, description) {
  log(`\n🔍 检查 ${description} 必需变量:`, 'blue');
  
  let allPresent = true;
  requiredVars.forEach(varName => {
    if (env[varName] && env[varName] !== 'your_value_here' && env[varName] !== '') {
      log(`  ✅ ${varName}: ${env[varName].substring(0, 20)}${env[varName].length > 20 ? '...' : ''}`, 'green');
    } else {
      log(`  ❌ ${varName}: 未配置或使用默认值`, 'red');
      allPresent = false;
    }
  });
  
  return allPresent;
}

function checkOptionalVars(env, optionalVars, description) {
  log(`\n🔍 检查 ${description} 可选变量:`, 'blue');
  
  optionalVars.forEach(varName => {
    if (env[varName] && env[varName] !== 'your_value_here' && env[varName] !== '') {
      log(`  ✅ ${varName}: 已配置`, 'green');
    } else {
      log(`  ⚠️  ${varName}: 未配置 (可选)`, 'yellow');
    }
  });
}

function main() {
  log('🔧 环境变量配置验证', 'blue');
  log('=' * 50, 'blue');
  
  const projectRoot = path.resolve(__dirname, '..');
  const backendEnvPath = path.join(projectRoot, 'backend', '.env');
  const frontendEnvPath = path.join(projectRoot, 'frontend', '.env');
  
  // 检查文件是否存在
  log('\n📁 检查环境变量文件:', 'blue');
  const backendExists = checkFileExists(backendEnvPath, '后端环境变量文件');
  const frontendExists = checkFileExists(frontendEnvPath, '前端环境变量文件');
  
  if (!backendExists || !frontendExists) {
    log('\n❌ 环境变量文件缺失，请先创建必要的 .env 文件', 'red');
    process.exit(1);
  }
  
  // 加载环境变量
  const backendEnv = loadEnvFile(backendEnvPath);
  const frontendEnv = loadEnvFile(frontendEnvPath);
  
  if (!backendEnv || !frontendEnv) {
    log('\n❌ 无法加载环境变量文件', 'red');
    process.exit(1);
  }
  
  // 检查后端必需变量
  const backendRequired = ['PORT', 'NODE_ENV', 'JWT_SECRET', 'FRONTEND_URL'];
  const backendOptional = ['MONGO_URI', 'AWS_ACCESS_KEY_ID', 'LLM_API_KEY', 'SMTP_USER'];
  
  const backendOk = checkRequiredVars(backendEnv, backendRequired, '后端');
  checkOptionalVars(backendEnv, backendOptional, '后端');
  
  // 检查前端必需变量
  const frontendRequired = ['VITE_API_BASE_URL', 'VITE_SOCKET_URL'];
  const frontendOptional = ['VITE_APP_TITLE', 'VITE_ENABLE_AI_CHAT', 'VITE_DEBUG'];
  
  const frontendOk = checkRequiredVars(frontendEnv, frontendRequired, '前端');
  checkOptionalVars(frontendEnv, frontendOptional, '前端');
  
  // 检查配置一致性
  log('\n🔗 检查前后端配置一致性:', 'blue');
  
  const backendPort = backendEnv.PORT || '4000';
  const frontendApiUrl = frontendEnv.VITE_API_BASE_URL || '';
  const expectedApiUrl = `http://localhost:${backendPort}/api`;
  
  if (frontendApiUrl === expectedApiUrl) {
    log(`  ✅ API地址配置一致: ${frontendApiUrl}`, 'green');
  } else {
    log(`  ❌ API地址配置不一致:`, 'red');
    log(`    前端配置: ${frontendApiUrl}`, 'red');
    log(`    期望配置: ${expectedApiUrl}`, 'red');
  }
  
  // 总结
  log('\n📊 验证结果:', 'blue');
  if (backendOk && frontendOk) {
    log('✅ 环境变量配置验证通过！项目可以正常启动。', 'green');
    process.exit(0);
  } else {
    log('❌ 环境变量配置存在问题，请检查上述错误并修正。', 'red');
    log('💡 提示: 请参考 docs/ENVIRONMENT_VARIABLES.md 获取详细配置说明', 'yellow');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkFileExists, loadEnvFile, checkRequiredVars };
