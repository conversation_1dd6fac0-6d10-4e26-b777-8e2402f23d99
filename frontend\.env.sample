# ===========================================
# 仓库库存管理及AI客服系统 - 前端环境变量模板
# ===========================================

# API 配置
# 后端API服务地址
VITE_API_BASE_URL=http://localhost:4000/api

# WebSocket 配置
# Socket.io 连接地址
VITE_SOCKET_URL=http://localhost:4000

# 应用配置
# 应用标题
VITE_APP_TITLE=库存管理系统 | HubGoodFood

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用环境
VITE_NODE_ENV=development

# 功能开关
# 是否启用AI客服功能
VITE_ENABLE_AI_CHAT=true

# 是否启用条码扫描功能
VITE_ENABLE_BARCODE_SCANNER=true

# 是否启用文件上传功能
VITE_ENABLE_FILE_UPLOAD=true

# 是否启用PWA功能
VITE_ENABLE_PWA=false

# UI 配置
# 主题色
VITE_PRIMARY_COLOR=#2C7AFF

# 是否启用暗色模式
VITE_ENABLE_DARK_MODE=false

# 默认语言
VITE_DEFAULT_LOCALE=zh-CN

# 调试配置
# 是否启用调试模式
VITE_DEBUG=true

# 是否显示开发工具
VITE_SHOW_DEV_TOOLS=true

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITORING=false

# 第三方服务配置（可选）
# 百度地图API密钥
VITE_BAIDU_MAP_API_KEY=your_baidu_map_api_key_here

# 微信小程序配置
VITE_WECHAT_APP_ID=your_wechat_app_id_here

# 错误监控配置
VITE_SENTRY_DSN=your_sentry_dsn_here

# 分析工具配置
VITE_GOOGLE_ANALYTICS_ID=your_ga_id_here

# 开发环境配置
# 是否启用热重载
VITE_HMR=true

# 是否启用源码映射
VITE_SOURCE_MAP=true

# 构建配置
# 构建输出目录
VITE_BUILD_OUTPUT_DIR=dist

# 是否启用代码分割
VITE_CODE_SPLITTING=true

# 是否启用压缩（开发环境建议关闭）
VITE_MINIFY=false
