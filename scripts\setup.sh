#!/bin/bash

# macOS/Linux Shell 脚本 - 项目快速设置
# 适用于 macOS Terminal 和 Linux Shell

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo
echo "========================================"
echo "  Chat AI 3.0 项目快速设置 (macOS/Linux)"
echo "========================================"
echo

# 检查 Node.js
echo "检查 Node.js..."
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js 18+${NC}"
    echo "macOS: brew install node"
    echo "Ubuntu: sudo apt install nodejs npm"
    echo "CentOS: sudo yum install nodejs npm"
    exit 1
fi

# 检查 npm
echo "检查 npm..."
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm 未安装${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js 和 npm 已安装${NC}"

# 显示版本信息
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
echo -e "${BLUE}Node.js 版本: $NODE_VERSION${NC}"
echo -e "${BLUE}npm 版本: $NPM_VERSION${NC}"

# 检查环境变量文件
echo
echo "检查环境配置..."
if [ ! -f "backend/.env" ]; then
    echo -e "${YELLOW}⚠️  环境变量文件不存在，正在创建...${NC}"
    cp backend/.env.sample backend/.env
    echo -e "${GREEN}✅ 已创建环境变量文件，请编辑 backend/.env 填入配置${NC}"
    echo
    echo "按 Enter 键继续..."
    read
fi

# 安装依赖
echo
echo "安装项目依赖..."
echo "正在安装根目录依赖..."
if ! npm install; then
    echo -e "${RED}❌ 根目录依赖安装失败${NC}"
    exit 1
fi

echo "正在安装后端依赖..."
cd backend
if ! npm install; then
    echo -e "${RED}❌ 后端依赖安装失败${NC}"
    exit 1
fi

echo "正在安装前端依赖..."
cd ../frontend
if ! npm install; then
    echo -e "${RED}❌ 前端依赖安装失败${NC}"
    exit 1
fi

cd ..

# 初始化数据库
echo
echo "初始化数据库..."
cd backend
if ! npm run seed; then
    echo -e "${YELLOW}⚠️  数据库初始化失败，请检查 MongoDB 连接${NC}"
fi

cd ..

echo
echo "========================================"
echo -e "  ${GREEN}🎉 设置完成！${NC}"
echo "========================================"
echo
echo "快速启动命令:"
echo "  npm run dev          # 同时启动前后端"
echo "  npm run dev:backend  # 仅启动后端"
echo "  npm run dev:frontend # 仅启动前端"
echo "  npm run check-env    # 检查环境"
echo
echo "访问地址:"
echo "  前端: http://localhost:5173"
echo "  后端: http://localhost:4000"
echo "  API文档: http://localhost:4000/api-docs"
echo
