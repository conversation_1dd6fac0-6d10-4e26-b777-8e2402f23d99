# 📁 配置文件说明

本目录包含项目的各种配置文件，用于不同的功能模块和部署环境。

## 📋 文件列表

### `openapi.yaml`
**用途**: API文档配置  
**描述**: OpenAPI 3.0 规范的API接口文档定义  
**使用**: 
- 自动生成API文档
- 接口规范定义
- 前后端接口约定

### `refund_policy.yaml`
**用途**: 退货政策配置  
**描述**: AI客服系统使用的退货政策规则  
**使用**:
- AI客服判断退货资格
- 自动化退货流程
- 政策规则配置

### `render.yaml`
**用途**: Render平台部署配置  
**描述**: 用于Render.com平台的部署配置文件  
**使用**:
- 生产环境部署
- 自动化CI/CD
- 环境变量配置

### `seed.json`
**用途**: 数据库种子数据  
**描述**: 初始化数据库时使用的示例数据  
**使用**:
- 开发环境初始化
- 测试数据准备
- 演示数据配置

## 🔧 使用说明

### 开发环境
```bash
# 使用种子数据初始化数据库
cd backend && npm run seed
```

### 生产环境
```bash
# 部署到Render平台
# 确保render.yaml配置正确
git push origin main
```

### API文档
```bash
# 启动后端服务后访问
http://localhost:4000/api-docs
```

## ⚠️ 注意事项

1. **不要直接修改生产环境配置文件**
2. **敏感信息应使用环境变量**
3. **配置文件修改后需要重启相关服务**
4. **部署前请验证配置文件格式**

## 📚 相关文档

- [环境配置指南](../docs/ENV_SETUP_GUIDE.md)
- [项目需求文档](../docs/inventory_ai_support_system.md)
- [快速设置指南](../docs/QUICK_SETUP.md)
