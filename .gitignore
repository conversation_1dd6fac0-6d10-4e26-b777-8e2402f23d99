# 依赖包
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 构建输出
dist/
build/
*.tsbuildinfo

# 日志文件
logs/
*.log

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc 测试覆盖率
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# Microbundle 缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的 REPL 历史
.node_repl_history

# 输出的 npm 包
*.tgz

# Yarn 完整性文件
.yarn-integrity

# parcel-bundler 缓存
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建/生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# 临时文件夹
tmp/
temp/

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws
out/

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# OS 生成的文件
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.localized
.AppleDouble
.LSOverride

# Windows
ehthumbs.db
Thumbs.db
Thumbs.db:encryptable
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 上传的文件
uploads/
public/uploads/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Docker
.dockerignore

# 测试文件
test-results/
playwright-report/
test-results.xml
coverage/
.nyc_output/
junit.xml

# 开发工具缓存
.eslintcache
.stylelintcache
.sass-cache/
.cache/
.parcel-cache/

# 包管理器
.pnp.*
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnpm-debug.log*

# 环境和配置
.env.backup
.env.*.local
.vscode/settings.json
.history/

# 运行时和进程
*.pid
*.seed
*.pid.lock
.pm2/

# 系统和应用特定
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# 开发服务器
.vercel
.netlify
.firebase/
.serverless/

# 数据库
*.db-journal
*.db-wal
*.db-shm

# 日志和调试
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
debug.log
error.log
