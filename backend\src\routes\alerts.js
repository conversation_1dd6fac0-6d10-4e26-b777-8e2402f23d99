const express = require('express');
const alertController = require('../controllers/alertController');
const { authenticateToken, requirePermission } = require('../middleware/auth');

const router = express.Router();

// 所有预警路由都需要认证
router.use(authenticateToken);

/**
 * @desc    获取所有预警
 * @route   GET /api/alerts
 * @access  Private
 * @query   priority: 优先级过滤, type: 类型过滤, limit: 限制数量
 */
router.get('/', 
  requirePermission('alerts.read'), 
  alertController.getAllAlerts
);

/**
 * @desc    获取预警摘要
 * @route   GET /api/alerts/summary
 * @access  Private
 */
router.get('/summary', 
  requirePermission('alerts.read'), 
  alertController.getAlertSummary
);

/**
 * @desc    获取特定类型的预警
 * @route   GET /api/alerts/type/:type
 * @access  Private
 * @params  type: 预警类型
 */
router.get('/type/:type', 
  requirePermission('alerts.read'), 
  alertController.getAlertsByType
);

/**
 * @desc    手动触发预警检查
 * @route   POST /api/alerts/check
 * @access  Private
 */
router.post('/check', 
  requirePermission('alerts.write'), 
  alertController.triggerAlertCheck
);

/**
 * @desc    获取预警配置
 * @route   GET /api/alerts/config
 * @access  Private
 */
router.get('/config', 
  requirePermission('alerts.read'), 
  alertController.getAlertConfig
);

/**
 * @desc    更新预警配置
 * @route   PUT /api/alerts/config
 * @access  Private
 * @body    expiringDays: 临期天数, lowStockMultiplier: 低库存倍数, enabledAlerts: 启用的预警类型
 */
router.put('/config', 
  requirePermission('alerts.write'), 
  alertController.updateAlertConfig
);

/**
 * @desc    获取定时任务状态
 * @route   GET /api/alerts/cron/status
 * @access  Private
 */
router.get('/cron/status', 
  requirePermission('system.read'), 
  alertController.getCronStatus
);

/**
 * @desc    手动执行定时任务
 * @route   POST /api/alerts/cron/:taskId/run
 * @access  Private
 * @params  taskId: 任务ID
 */
router.post('/cron/:taskId/run',
  requirePermission('system.write'),
  alertController.runCronTask
);

/**
 * @desc    获取智能预警分析
 * @route   GET /api/alerts/intelligent-analysis
 * @access  Private
 * @query   days_back: 回溯天数, prediction_days: 预测天数, include_trends: 包含趋势, include_recommendations: 包含建议
 */
router.get('/intelligent-analysis',
  requirePermission('alerts.read'),
  alertController.getIntelligentAnalysis
);

/**
 * @desc    获取库存趋势分析
 * @route   GET /api/alerts/trends
 * @access  Private
 * @query   days_back: 回溯天数
 */
router.get('/trends',
  requirePermission('alerts.read'),
  alertController.getStockTrends
);

/**
 * @desc    获取库存预测
 * @route   GET /api/alerts/predictions
 * @access  Private
 * @query   prediction_days: 预测天数, days_back: 分析回溯天数
 */
router.get('/predictions',
  requirePermission('alerts.read'),
  alertController.getStockPredictions
);

/**
 * @desc    获取风险评估
 * @route   GET /api/alerts/risk-assessment
 * @access  Private
 */
router.get('/risk-assessment',
  requirePermission('alerts.read'),
  alertController.getRiskAssessment
);

module.exports = router;
