<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="仓库库存管理及AI客服系统" />
    <meta name="keywords" content="库存管理,AI客服,仓库管理,退货处理" />
    <title>库存管理系统 | HubGoodFood</title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- PWA 支持 -->
    <meta name="theme-color" content="#2C7AFF" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />
    
    <style>
      /* 加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f7f9fc;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #2C7AFF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 基础样式重置 */
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        font-family: 'Inter', '思源黑体', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f7f9fc;
      }
    </style>
  </head>
  <body>
    <!-- 加载动画 -->
    <div id="loading">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- React 应用挂载点 -->
    <div id="root"></div>
    
    <script type="module" src="/src/main.jsx"></script>
    
    <script>
      // 隐藏加载动画
      window.addEventListener('load', function() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.display = 'none';
        }
      });
    </script>
  </body>
</html>
