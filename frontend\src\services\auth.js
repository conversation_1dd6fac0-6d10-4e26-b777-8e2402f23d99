import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // token过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    
    const message = error.response?.data?.error || error.message || '请求失败'
    return Promise.reject(new Error(message))
  }
)

export const authService = {
  // 用户登录
  async login(credentials) {
    try {
      const response = await api.post('/auth/login', credentials)
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.error || '登录失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 获取当前用户信息
  async getCurrentUser() {
    try {
      const response = await api.get('/auth/me')
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.error || '获取用户信息失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 用户登出
  async logout() {
    try {
      await api.post('/auth/logout')
    } catch (error) {
      // 即使API调用失败，也要清除本地状态
      console.error('登出API调用失败:', error)
    }
  },

  // 检查token是否有效
  async validateToken() {
    try {
      const response = await api.get('/auth/me')
      return response.success
    } catch (error) {
      return false
    }
  }
}

export default api
