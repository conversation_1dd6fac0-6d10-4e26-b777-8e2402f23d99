require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');

/**
 * 政策导入功能测试
 * 测试从policy.json文件导入政策数据的功能
 */

async function testPolicyImport() {
  console.log('📜 开始政策导入功能测试...\n');

  try {
    // 测试1: 检查policy.json文件是否存在
    console.log('📋 测试1: 检查policy.json文件');
    const policyFilePath = path.join(process.cwd(), '..', 'policy.json');
    
    try {
      const fileStats = await fs.stat(policyFilePath);
      console.log('✅ policy.json文件存在');
      console.log(`- 文件大小: ${fileStats.size} 字节`);
      console.log(`- 修改时间: ${fileStats.mtime.toLocaleString()}`);
    } catch (error) {
      console.log('❌ policy.json文件不存在');
      return;
    }

    // 测试2: 读取和解析policy.json文件
    console.log('\n📋 测试2: 读取和解析policy.json');
    let policyData;
    
    try {
      const fileContent = await fs.readFile(policyFilePath, 'utf8');
      policyData = JSON.parse(fileContent);
      console.log('✅ policy.json文件解析成功');
      console.log(`- 版本: ${policyData.version || '未指定'}`);
      console.log(`- 平台: ${policyData.platform || '未指定'}`);
      console.log(`- 更新时间: ${policyData.last_updated || '未指定'}`);
    } catch (error) {
      console.log('❌ policy.json文件解析失败:', error.message);
      return;
    }

    // 测试3: 分析政策结构
    console.log('\n📋 测试3: 分析政策结构');
    
    if (policyData.sections) {
      console.log('✅ 政策分类结构正确');
      console.log(`- 政策分类数量: ${Object.keys(policyData.sections).length}`);
      
      for (const [category, items] of Object.entries(policyData.sections)) {
        console.log(`- ${category}: ${Array.isArray(items) ? items.length : 0}条规则`);
      }
    } else {
      console.log('❌ 政策结构不正确，缺少sections字段');
      return;
    }

    // 测试4: 验证政策内容
    console.log('\n📋 测试4: 验证政策内容');
    
    const expectedCategories = [
      'mission',
      'group_rules', 
      'product_quality',
      'delivery',
      'payment',
      'pickup',
      'after_sale',
      'community'
    ];

    let validCategories = 0;
    let totalRules = 0;

    for (const category of expectedCategories) {
      if (policyData.sections[category]) {
        validCategories++;
        const rules = policyData.sections[category];
        if (Array.isArray(rules)) {
          totalRules += rules.length;
          console.log(`✅ ${category}: ${rules.length}条规则`);
          
          // 显示前2条规则作为示例
          if (rules.length > 0) {
            console.log(`   示例: "${rules[0].substring(0, 50)}..."`);
          }
        } else {
          console.log(`⚠️ ${category}: 格式不正确（非数组）`);
        }
      } else {
        console.log(`❌ ${category}: 缺失`);
      }
    }

    console.log(`\n政策内容统计:`);
    console.log(`- 有效分类: ${validCategories}/${expectedCategories.length}`);
    console.log(`- 总规则数: ${totalRules}`);

    // 测试5: 模拟政策服务导入
    console.log('\n📋 测试5: 模拟政策服务导入');
    
    const mockPolicyService = {
      async importFromFile(userId) {
        console.log('🔄 开始模拟导入...');
        
        const results = {
          imported: 0,
          updated: 0,
          errors: []
        };

        for (const [category, items] of Object.entries(policyData.sections)) {
          try {
            if (Array.isArray(items) && items.length > 0) {
              console.log(`  导入 ${category}: ${items.length}条规则`);
              results.imported++;
            } else {
              console.log(`  跳过 ${category}: 无有效规则`);
            }
          } catch (error) {
            console.log(`  错误 ${category}: ${error.message}`);
            results.errors.push({
              category,
              error: error.message
            });
          }
        }

        return {
          success: true,
          ...results
        };
      }
    };

    const importResult = await mockPolicyService.importFromFile('test_user_id');
    
    if (importResult.success) {
      console.log('✅ 模拟导入成功');
      console.log(`- 导入分类: ${importResult.imported}`);
      console.log(`- 更新分类: ${importResult.updated}`);
      console.log(`- 错误数量: ${importResult.errors.length}`);
    } else {
      console.log('❌ 模拟导入失败');
    }

    // 测试6: 政策内容质量检查
    console.log('\n📋 测试6: 政策内容质量检查');
    
    let qualityScore = 0;
    let maxScore = 0;

    // 检查关键政策是否存在
    const keyPolicies = {
      'delivery': ['配送', '运费', '时间'],
      'after_sale': ['退货', '质量', '反馈'],
      'payment': ['付款', 'venmo', '备注'],
      'pickup': ['取货', '地点', '时间']
    };

    for (const [category, keywords] of Object.entries(keyPolicies)) {
      maxScore += keywords.length;
      
      if (policyData.sections[category]) {
        const content = JSON.stringify(policyData.sections[category]).toLowerCase();
        
        for (const keyword of keywords) {
          if (content.includes(keyword)) {
            qualityScore++;
            console.log(`✅ ${category}包含关键词"${keyword}"`);
          } else {
            console.log(`⚠️ ${category}缺少关键词"${keyword}"`);
          }
        }
      }
    }

    const qualityPercentage = maxScore > 0 ? (qualityScore / maxScore * 100).toFixed(1) : 0;
    console.log(`\n政策质量评分: ${qualityScore}/${maxScore} (${qualityPercentage}%)`);

    // 测试7: 生成导入报告
    console.log('\n📋 测试7: 生成导入报告');
    
    const report = {
      timestamp: new Date().toISOString(),
      file_info: {
        path: policyFilePath,
        size: (await fs.stat(policyFilePath)).size,
        version: policyData.version || 'unknown'
      },
      structure_analysis: {
        total_categories: Object.keys(policyData.sections).length,
        valid_categories: validCategories,
        total_rules: totalRules,
        expected_categories: expectedCategories.length
      },
      quality_analysis: {
        score: qualityScore,
        max_score: maxScore,
        percentage: parseFloat(qualityPercentage)
      },
      import_simulation: importResult
    };

    console.log('✅ 导入报告生成完成');
    console.log(JSON.stringify(report, null, 2));

    console.log('\n🎉 政策导入功能测试完成！');
    console.log('\n📊 测试总结:');
    console.log('- 文件存在性检查: ✅');
    console.log('- 文件解析: ✅');
    console.log('- 结构分析: ✅');
    console.log('- 内容验证: ✅');
    console.log('- 模拟导入: ✅');
    console.log('- 质量检查: ✅');
    console.log('- 报告生成: ✅');
    
    if (qualityPercentage >= 80) {
      console.log('🎉 政策文件质量: 优秀');
    } else if (qualityPercentage >= 60) {
      console.log('✅ 政策文件质量: 良好');
    } else {
      console.log('⚠️ 政策文件质量: 需要改进');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  testPolicyImport().catch(console.error);
}

module.exports = testPolicyImport;
