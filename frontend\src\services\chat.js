/**
 * 聊天和AI客服 API 服务
 */
import { apiRequest } from './api'
import io from 'socket.io-client'

export const chatService = {
  // WebSocket 连接实例
  socket: null,

  // 初始化 WebSocket 连接
  initSocket: () => {
    if (!chatService.socket) {
      chatService.socket = io('/', {
        transports: ['websocket'],
        autoConnect: false
      })
    }
    return chatService.socket
  },

  // 连接 WebSocket
  connect: () => {
    if (chatService.socket && !chatService.socket.connected) {
      chatService.socket.connect()
    }
  },

  // 断开 WebSocket 连接
  disconnect: () => {
    if (chatService.socket && chatService.socket.connected) {
      chatService.socket.disconnect()
    }
  },

  // 创建聊天会话
  createChatSession: async (customerInfo = {}) => {
    return apiRequest.post('/chat/sessions', {
      customer_info: customerInfo,
      channel: 'web_widget',
      language: 'zh-CN'
    })
  },

  // 获取聊天会话列表
  getChatSessions: async (params = {}) => {
    const { page = 1, limit = 10, status, startDate, endDate } = params
    return apiRequest.get('/chat/sessions', {
      page,
      limit,
      status,
      startDate,
      endDate
    })
  },

  // 获取聊天会话详情
  getChatSession: async (sessionId) => {
    return apiRequest.get(`/chat/sessions/${sessionId}`)
  },

  // 发送消息
  sendMessage: async (sessionId, message, messageType = 'text') => {
    return apiRequest.post(`/chat/sessions/${sessionId}/messages`, {
      message,
      message_type: messageType
    })
  },

  // 获取聊天历史
  getChatHistory: async (sessionId, params = {}) => {
    const { page = 1, limit = 50 } = params
    return apiRequest.get(`/chat/sessions/${sessionId}/messages`, {
      page,
      limit
    })
  },

  // 升级到人工客服
  escalateToHuman: async (sessionId, reason) => {
    return apiRequest.post(`/chat/sessions/${sessionId}/escalate`, { reason })
  },

  // 结束聊天会话
  endChatSession: async (sessionId) => {
    return apiRequest.post(`/chat/sessions/${sessionId}/end`)
  },

  // 获取聊天统计信息
  getChatStatistics: async (params = {}) => {
    const { period = 'week', startDate, endDate } = params
    return apiRequest.get('/chat/statistics', {
      period,
      startDate,
      endDate
    })
  },

  // 获取AI客服配置
  getAIConfig: async () => {
    return apiRequest.get('/chat/ai-config')
  },

  // 更新AI客服配置
  updateAIConfig: async (config) => {
    return apiRequest.put('/chat/ai-config', config)
  },

  // 获取政策列表
  getPolicies: async (params = {}) => {
    const { page = 1, limit = 10, search, category } = params
    return apiRequest.get('/policy', {
      page,
      limit,
      search,
      category
    })
  },

  // 搜索政策
  searchPolicies: async (query) => {
    return apiRequest.get('/policy/search', { q: query })
  },

  // 获取政策统计
  getPolicyStatistics: async () => {
    return apiRequest.get('/policy/statistics')
  },

  // 获取政策详情
  getPolicy: async (policyId) => {
    return apiRequest.get(`/policy/${policyId}`)
  },

  // 创建政策
  createPolicy: async (policy) => {
    return apiRequest.post('/policy', policy)
  },

  // 更新政策
  updatePolicy: async (policyId, policy) => {
    return apiRequest.put(`/policy/${policyId}`, policy)
  },

  // 删除政策
  deletePolicy: async (policyId) => {
    return apiRequest.delete(`/policy/${policyId}`)
  },

  // 导入政策文件
  importPolicies: async (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return apiRequest.post('/policy/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // WebSocket 事件监听器
  onMessage: (callback) => {
    if (chatService.socket) {
      chatService.socket.on('chat-message', callback)
    }
  },

  onAIResponse: (callback) => {
    if (chatService.socket) {
      chatService.socket.on('ai-response', callback)
    }
  },

  onUserJoined: (callback) => {
    if (chatService.socket) {
      chatService.socket.on('user-joined', callback)
    }
  },

  onUserLeft: (callback) => {
    if (chatService.socket) {
      chatService.socket.on('user-left', callback)
    }
  },

  onError: (callback) => {
    if (chatService.socket) {
      chatService.socket.on('chat-error', callback)
    }
  },

  // 移除事件监听器
  offMessage: () => {
    if (chatService.socket) {
      chatService.socket.off('chat-message')
    }
  },

  offAIResponse: () => {
    if (chatService.socket) {
      chatService.socket.off('ai-response')
    }
  },

  offUserJoined: () => {
    if (chatService.socket) {
      chatService.socket.off('user-joined')
    }
  },

  offUserLeft: () => {
    if (chatService.socket) {
      chatService.socket.off('user-left')
    }
  },

  offError: () => {
    if (chatService.socket) {
      chatService.socket.off('chat-error')
    }
  }
}

export default chatService
