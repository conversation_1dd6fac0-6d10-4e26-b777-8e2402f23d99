const { Product, Inventory, StockInRecord, OperationLog } = require('../models');
const logger = require('../utils/logger');

/**
 * 入库服务类
 * 处理入库流程优化、批量入库、成本计算等功能
 */
class StockInService {
  constructor() {
    // 入库状态定义
    this.stockInStatus = {
      PENDING: 'pending',
      PROCESSING: 'processing',
      COMPLETED: 'completed',
      CANCELLED: 'cancelled',
      FAILED: 'failed'
    };

    // 批量入库配置
    this.batchConfig = {
      maxBatchSize: 100, // 最大批量处理数量
      chunkSize: 10, // 每次处理的块大小
      timeoutMs: 30000 // 超时时间
    };

    logger.info('StockInService初始化完成', {
      maxBatchSize: this.batchConfig.maxBatchSize,
      chunkSize: this.batchConfig.chunkSize
    });
  }

  /**
   * 批量创建入库记录
   * @param {Array} stockInData - 入库数据数组
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 批量创建结果
   */
  async createBatchStockIn(stockInData, userId) {
    try {
      if (!Array.isArray(stockInData) || stockInData.length === 0) {
        throw new Error('入库数据不能为空');
      }

      if (stockInData.length > this.batchConfig.maxBatchSize) {
        throw new Error(`批量入库数量不能超过 ${this.batchConfig.maxBatchSize} 条`);
      }

      logger.info('开始批量创建入库记录', {
        userId,
        batchSize: stockInData.length
      });

      const results = [];
      const errors = [];

      // 分块处理
      for (let i = 0; i < stockInData.length; i += this.batchConfig.chunkSize) {
        const chunk = stockInData.slice(i, i + this.batchConfig.chunkSize);
        
        const chunkResults = await Promise.allSettled(
          chunk.map(data => this.createSingleStockIn(data, userId))
        );

        chunkResults.forEach((result, index) => {
          const originalIndex = i + index;
          
          if (result.status === 'fulfilled' && result.value.success) {
            results.push({
              index: originalIndex,
              data: result.value.data
            });
          } else {
            errors.push({
              index: originalIndex,
              error: result.status === 'rejected' ? result.reason.message : result.value.error,
              data: chunk[index]
            });
          }
        });
      }

      logger.info('批量创建入库记录完成', {
        userId,
        totalItems: stockInData.length,
        successCount: results.length,
        errorCount: errors.length
      });

      return {
        success: errors.length === 0,
        data: {
          results,
          errors,
          summary: {
            total: stockInData.length,
            success: results.length,
            failed: errors.length,
            successRate: ((results.length / stockInData.length) * 100).toFixed(2) + '%'
          }
        }
      };

    } catch (error) {
      logger.error('批量创建入库记录失败', {
        userId,
        error: error.message,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建单个入库记录
   * @param {Object} stockInData - 入库数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 创建结果
   */
  async createSingleStockIn(stockInData, userId) {
    try {
      const {
        supplier,
        supplier_invoice,
        items,
        warehouse_location,
        delivery_note,
        notes,
        auto_process = false
      } = stockInData;

      // 验证必需字段
      if (!supplier || !items || !Array.isArray(items) || items.length === 0) {
        throw new Error('供应商和商品信息是必需的');
      }

      // 验证商品信息并计算总计
      const validatedItems = [];
      let totalQuantity = 0;
      let totalAmount = 0;

      for (const item of items) {
        const validatedItem = await this.validateStockInItem(item);
        validatedItems.push(validatedItem);
        totalQuantity += validatedItem.quantity;
        totalAmount += validatedItem.quantity * validatedItem.unit_cost;
      }

      // 创建入库记录
      const stockInRecord = new StockInRecord({
        supplier,
        supplier_invoice,
        items: validatedItems,
        warehouse_location,
        delivery_note,
        notes,
        total_quantity: totalQuantity,
        total_amount: totalAmount,
        created_by: userId,
        status: auto_process ? this.stockInStatus.PROCESSING : this.stockInStatus.PENDING
      });

      await stockInRecord.save();

      // 如果设置了自动处理，立即处理入库
      if (auto_process) {
        await this.processStockIn(stockInRecord._id, userId);
      }

      return {
        success: true,
        data: {
          stockInRecord: {
            id: stockInRecord._id,
            record_number: stockInRecord.record_number,
            supplier: stockInRecord.supplier,
            total_quantity: stockInRecord.total_quantity,
            total_amount: stockInRecord.total_amount,
            status: stockInRecord.status,
            created_at: stockInRecord.created_at
          }
        }
      };

    } catch (error) {
      logger.error('创建单个入库记录失败', {
        userId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 验证入库商品信息
   * @param {Object} item - 商品信息
   * @returns {Promise<Object>} 验证后的商品信息
   */
  async validateStockInItem(item) {
    const { product_id, barcode, quantity, unit_cost, expiry_date, batch_number, location } = item;

    // 验证必需字段
    if (!quantity || quantity <= 0) {
      throw new Error('商品数量必须大于0');
    }

    if (!unit_cost || unit_cost < 0) {
      throw new Error('商品单价不能为负数');
    }

    let product;

    // 通过product_id或barcode查找商品
    if (product_id) {
      product = await Product.findById(product_id);
    } else if (barcode) {
      product = await Product.findOne({ barcode, is_active: true });
    } else {
      throw new Error('必须提供商品ID或条码');
    }

    if (!product) {
      throw new Error('商品不存在或已停用');
    }

    // 验证过期日期
    if (expiry_date) {
      const expiryDate = new Date(expiry_date);
      if (expiryDate <= new Date()) {
        throw new Error('过期日期不能早于当前日期');
      }
    }

    return {
      product_id: product._id,
      product_name: product.name,
      quantity: parseFloat(quantity),
      unit_cost: parseFloat(unit_cost),
      expiry_date: expiry_date ? new Date(expiry_date) : null,
      batch_number: batch_number || null,
      location: location || null
    };
  }

  /**
   * 处理入库记录
   * @param {string} recordId - 入库记录ID
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 处理结果
   */
  async processStockIn(recordId, userId) {
    try {
      const stockInRecord = await StockInRecord.findById(recordId);

      if (!stockInRecord) {
        throw new Error('入库记录不存在');
      }

      if (stockInRecord.status !== this.stockInStatus.PENDING) {
        throw new Error(`入库记录状态为 ${stockInRecord.status}，无法处理`);
      }

      logger.info('开始处理入库记录', {
        recordId,
        recordNumber: stockInRecord.record_number,
        userId
      });

      // 更新状态为处理中
      stockInRecord.status = this.stockInStatus.PROCESSING;
      stockInRecord.processed_by = userId;
      stockInRecord.processed_at = new Date();
      await stockInRecord.save();

      const processResults = [];
      const processErrors = [];

      // 处理每个商品的入库
      for (const item of stockInRecord.items) {
        try {
          const result = await this.processStockInItem(item, stockInRecord, userId);
          processResults.push(result);
        } catch (error) {
          processErrors.push({
            product_id: item.product_id,
            product_name: item.product_name,
            error: error.message
          });
        }
      }

      // 更新最终状态
      if (processErrors.length === 0) {
        stockInRecord.status = this.stockInStatus.COMPLETED;
        stockInRecord.completed_at = new Date();
      } else {
        stockInRecord.status = this.stockInStatus.FAILED;
        stockInRecord.error_details = processErrors;
      }

      await stockInRecord.save();

      logger.info('入库记录处理完成', {
        recordId,
        status: stockInRecord.status,
        successCount: processResults.length,
        errorCount: processErrors.length
      });

      return {
        success: processErrors.length === 0,
        data: {
          recordId,
          status: stockInRecord.status,
          processResults,
          processErrors,
          summary: {
            totalItems: stockInRecord.items.length,
            successItems: processResults.length,
            failedItems: processErrors.length
          }
        }
      };

    } catch (error) {
      logger.error('处理入库记录失败', {
        recordId,
        userId,
        error: error.message,
        stack: error.stack
      });

      // 更新记录状态为失败
      try {
        await StockInRecord.findByIdAndUpdate(recordId, {
          status: this.stockInStatus.FAILED,
          error_details: [{ error: error.message }]
        });
      } catch (updateError) {
        logger.error('更新入库记录状态失败', { updateError: updateError.message });
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 处理单个商品的入库
   * @param {Object} item - 商品信息
   * @param {Object} stockInRecord - 入库记录
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 处理结果
   */
  async processStockInItem(item, stockInRecord, userId) {
    try {
      // 查找或创建库存记录
      let inventory = await Inventory.findOne({ product_id: item.product_id });

      if (!inventory) {
        // 创建新的库存记录
        inventory = new Inventory({
          product_id: item.product_id,
          current_stock: 0,
          reserved_stock: 0,
          available_stock: 0,
          location: item.location || stockInRecord.warehouse_location,
          reorder_point: 0,
          cost_per_unit: item.unit_cost,
          total_value: 0,
          updated_by: userId
        });
      }

      // 计算新的库存数据
      const oldStock = inventory.current_stock;
      const newStock = oldStock + item.quantity;
      const oldValue = inventory.total_value;
      const addedValue = item.quantity * item.unit_cost;
      const newValue = oldValue + addedValue;

      // 计算加权平均成本
      const newCostPerUnit = newStock > 0 ? newValue / newStock : item.unit_cost;

      // 更新库存
      inventory.current_stock = newStock;
      inventory.available_stock = newStock - inventory.reserved_stock;
      inventory.cost_per_unit = newCostPerUnit;
      inventory.total_value = newValue;
      inventory.last_stock_in_date = new Date();
      inventory.updated_by = userId;
      inventory.updated_at = new Date();

      await inventory.save();

      // 记录库存变动日志
      await OperationLog.logSuccess(
        userId,
        'inventory.stock_in',
        'inventory',
        inventory._id.toString(),
        {
          action: 'update',
          before_data: {
            current_stock: oldStock,
            total_value: oldValue
          },
          after_data: {
            current_stock: newStock,
            total_value: newValue,
            cost_per_unit: newCostPerUnit
          },
          description: `入库操作: ${item.product_name} +${item.quantity}`
        }
      );

      return {
        product_id: item.product_id,
        product_name: item.product_name,
        quantity: item.quantity,
        unit_cost: item.unit_cost,
        old_stock: oldStock,
        new_stock: newStock,
        cost_per_unit: newCostPerUnit
      };

    } catch (error) {
      logger.error('处理商品入库失败', {
        productId: item.product_id,
        productName: item.product_name,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * 批量处理入库记录
   * @param {Array} recordIds - 入库记录ID数组
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 批量处理结果
   */
  async processBatchStockIn(recordIds, userId) {
    try {
      if (!Array.isArray(recordIds) || recordIds.length === 0) {
        throw new Error('入库记录ID列表不能为空');
      }

      logger.info('开始批量处理入库记录', {
        userId,
        recordCount: recordIds.length
      });

      const results = [];
      const errors = [];

      // 并发处理入库记录
      const processPromises = recordIds.map(async (recordId, index) => {
        try {
          const result = await this.processStockIn(recordId, userId);
          
          if (result.success) {
            results.push({
              index,
              recordId,
              result: result.data
            });
          } else {
            errors.push({
              index,
              recordId,
              error: result.error
            });
          }
        } catch (error) {
          errors.push({
            index,
            recordId,
            error: error.message
          });
        }
      });

      await Promise.all(processPromises);

      logger.info('批量处理入库记录完成', {
        userId,
        totalRecords: recordIds.length,
        successCount: results.length,
        errorCount: errors.length
      });

      return {
        success: errors.length === 0,
        data: {
          results,
          errors,
          summary: {
            total: recordIds.length,
            success: results.length,
            failed: errors.length,
            successRate: ((results.length / recordIds.length) * 100).toFixed(2) + '%'
          }
        }
      };

    } catch (error) {
      logger.error('批量处理入库记录失败', {
        userId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 取消入库记录
   * @param {string} recordId - 入库记录ID
   * @param {string} userId - 用户ID
   * @param {string} reason - 取消原因
   * @returns {Promise<Object>} 取消结果
   */
  async cancelStockIn(recordId, userId, reason = '') {
    try {
      const stockInRecord = await StockInRecord.findById(recordId);

      if (!stockInRecord) {
        throw new Error('入库记录不存在');
      }

      if (stockInRecord.status === this.stockInStatus.COMPLETED) {
        throw new Error('已完成的入库记录无法取消');
      }

      if (stockInRecord.status === this.stockInStatus.CANCELLED) {
        throw new Error('入库记录已被取消');
      }

      // 更新状态
      stockInRecord.status = this.stockInStatus.CANCELLED;
      stockInRecord.cancelled_by = userId;
      stockInRecord.cancelled_at = new Date();
      stockInRecord.cancel_reason = reason;

      await stockInRecord.save();

      logger.info('入库记录已取消', {
        recordId,
        recordNumber: stockInRecord.record_number,
        userId,
        reason
      });

      return {
        success: true,
        data: {
          recordId,
          status: stockInRecord.status,
          cancelled_at: stockInRecord.cancelled_at,
          cancel_reason: reason
        }
      };

    } catch (error) {
      logger.error('取消入库记录失败', {
        recordId,
        userId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取入库统计信息
   * @param {Object} filters - 过滤条件
   * @returns {Promise<Object>} 统计信息
   */
  async getStockInStatistics(filters = {}) {
    try {
      const { startDate, endDate, supplier, status } = filters;

      // 构建查询条件
      const matchConditions = {};

      if (startDate || endDate) {
        matchConditions.created_at = {};
        if (startDate) matchConditions.created_at.$gte = new Date(startDate);
        if (endDate) matchConditions.created_at.$lte = new Date(endDate);
      }

      if (supplier) {
        matchConditions.supplier = { $regex: supplier, $options: 'i' };
      }

      if (status) {
        matchConditions.status = status;
      }

      const statistics = await StockInRecord.aggregate([
        { $match: matchConditions },
        {
          $group: {
            _id: null,
            totalRecords: { $sum: 1 },
            totalQuantity: { $sum: '$total_quantity' },
            totalAmount: { $sum: '$total_amount' },
            avgAmount: { $avg: '$total_amount' },
            statusCounts: {
              $push: '$status'
            }
          }
        },
        {
          $project: {
            _id: 0,
            totalRecords: 1,
            totalQuantity: 1,
            totalAmount: 1,
            avgAmount: { $round: ['$avgAmount', 2] }
          }
        }
      ]);

      // 统计各状态数量
      const statusStats = await StockInRecord.aggregate([
        { $match: matchConditions },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const statusCounts = {};
      statusStats.forEach(stat => {
        statusCounts[stat._id] = stat.count;
      });

      const result = statistics[0] || {
        totalRecords: 0,
        totalQuantity: 0,
        totalAmount: 0,
        avgAmount: 0
      };

      result.statusCounts = statusCounts;

      return {
        success: true,
        data: result
      };

    } catch (error) {
      logger.error('获取入库统计信息失败', {
        filters,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new StockInService();
