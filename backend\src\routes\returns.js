const express = require('express');
const returnController = require('../controllers/returnController');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const router = express.Router();

// @desc    创建退货申请
// @route   POST /api/returns
// @access  Public
router.post('/', returnController.createReturnRequest);

// @desc    获取退货申请列表
// @route   GET /api/returns
// @access  Private
router.get('/', authenticateToken, requirePermission('returns.read'), returnController.getReturnRequests);

// @desc    获取退货申请详情
// @route   GET /api/returns/:id
// @access  Private
router.get('/:id', authenticateToken, requirePermission('returns.read'), returnController.getReturnRequestById);

// @desc    审核退货申请
// @route   POST /api/returns/:id/review
// @access  Private
router.post('/:id/review', authenticateToken, requirePermission('returns.approve'), returnController.reviewReturnRequest);

// @desc    获取AI分析结果
// @route   GET /api/returns/:id/ai-analysis
// @access  Private
router.get('/:id/ai-analysis', authenticateToken, requirePermission('returns.read'), returnController.getAIAnalysis);

// @desc    应用AI建议
// @route   POST /api/returns/:id/apply-ai-suggestions
// @access  Private
router.post('/:id/apply-ai-suggestions', authenticateToken, requirePermission('returns.approve'), returnController.applyAISuggestions);

// @desc    批量审核退货申请
// @route   POST /api/returns/batch-review
// @access  Private
router.post('/batch-review', authenticateToken, requirePermission('returns.approve'), returnController.batchReviewReturnRequests);

// @desc    配置自动化审核规则
// @route   POST /api/returns/automation/configure
// @access  Private
router.post('/automation/configure', authenticateToken, requirePermission('returns.admin'), returnController.configureAutomationRules);

// @desc    执行自动化审核
// @route   POST /api/returns/automation/execute
// @access  Private
router.post('/automation/execute', authenticateToken, requirePermission('returns.admin'), returnController.executeAutomatedReview);

// 兼容旧版API
// @desc    管理员审核退货 (兼容性)
// @route   POST /api/returns/:id/approve
// @access  Private
router.post('/:id/approve', authenticateToken, requirePermission('returns.approve'), returnController.reviewReturnRequest);

module.exports = router;
