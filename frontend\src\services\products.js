/**
 * 商品管理 API 服务
 */
import { apiRequest } from './api'

export const productsService = {
  // 获取商品列表
  getProducts: async (params = {}) => {
    const { page = 1, limit = 10, search, category, status } = params
    return apiRequest.get('/products', {
      page,
      limit,
      search,
      category,
      status
    })
  },

  // 获取商品详情
  getProduct: async (id) => {
    return apiRequest.get(`/products/${id}`)
  },

  // 创建商品
  createProduct: async (productData) => {
    return apiRequest.post('/products', productData)
  },

  // 更新商品
  updateProduct: async (id, productData) => {
    return apiRequest.put(`/products/${id}`, productData)
  },

  // 删除商品
  deleteProduct: async (id) => {
    return apiRequest.delete(`/products/${id}`)
  },

  // 批量删除商品
  batchDeleteProducts: async (ids) => {
    return apiRequest.post('/products/batch-delete', { ids })
  },

  // 通过条码查询商品
  getProductByBarcode: async (barcode) => {
    return apiRequest.get(`/products/barcode/${barcode}`)
  },

  // 验证条码
  validateBarcode: async (barcode) => {
    return apiRequest.post('/products/validate-barcode', { barcode })
  },

  // 生成条码
  generateBarcodes: async (count = 1, format = 'EAN13') => {
    return apiRequest.post('/products/generate-barcodes', { count, format })
  },

  // 获取商品分类列表
  getCategories: async () => {
    return apiRequest.get('/products/categories')
  },

  // 获取支持的条码格式
  getBarcodeFormats: async () => {
    return apiRequest.get('/products/barcode-formats')
  },

  // 上传商品图片
  uploadImage: async (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return apiRequest.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 批量上传图片
  uploadMultipleImages: async (files) => {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })
    return apiRequest.post('/files/upload-multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取商品统计信息
  getProductStats: async () => {
    return apiRequest.get('/products/statistics')
  }
}

export default productsService
