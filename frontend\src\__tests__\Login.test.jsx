/**
 * Login组件单元测试
 * 测试登录表单验证、提交流程、错误处理等功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Login from '../pages/Login';

// 模拟Ant Design的message组件
jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    message: {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
      info: jest.fn(),
    },
  };
});

// 测试组件包装器
const renderLogin = (props = {}) => {
  const defaultProps = {
    onLogin: jest.fn(),
    ...props,
  };

  return render(
    <ConfigProvider locale={zhCN}>
      <Login {...defaultProps} />
    </ConfigProvider>
  );
};

describe('Login组件测试', () => {
  let mockOnLogin;

  beforeEach(() => {
    mockOnLogin = jest.fn();
    jest.clearAllMocks();
  });

  describe('组件渲染测试', () => {
    test('应该正确渲染登录表单', () => {
      renderLogin({ onLogin: mockOnLogin });

      // 验证品牌信息
      expect(screen.getByText('HubGoodFood')).toBeInTheDocument();
      expect(screen.getByText('仓库库存管理及AI客服系统')).toBeInTheDocument();

      // 验证表单字段
      expect(screen.getByPlaceholderText('用户名')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('密码')).toBeInTheDocument();

      // 验证登录按钮
      expect(screen.getByRole('button', { name: /登录/ })).toBeInTheDocument();

      // 验证测试账号提示
      expect(screen.getByText('测试账号: admin / admin123')).toBeInTheDocument();
    });

    test('应该显示正确的图标', () => {
      renderLogin({ onLogin: mockOnLogin });

      // 验证用户名输入框图标
      const usernameInput = screen.getByPlaceholderText('用户名');
      expect(usernameInput.closest('.ant-input-affix-wrapper')).toHaveClass('ant-input-affix-wrapper');

      // 验证密码输入框图标
      const passwordInput = screen.getByPlaceholderText('密码');
      expect(passwordInput.closest('.ant-input-affix-wrapper')).toHaveClass('ant-input-affix-wrapper');
    });
  });

  describe('表单验证测试', () => {
    test('应该验证用户名必填', async () => {
      const user = userEvent.setup();
      renderLogin({ onLogin: mockOnLogin });

      const submitButton = screen.getByRole('button', { name: /登录/ });
      
      // 不填写用户名直接提交
      await user.click(submitButton);

      // 验证错误信息
      await waitFor(() => {
        expect(screen.getByText('请输入用户名')).toBeInTheDocument();
      });

      // 验证onLogin未被调用
      expect(mockOnLogin).not.toHaveBeenCalled();
    });

    test('应该验证密码必填', async () => {
      const user = userEvent.setup();
      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const submitButton = screen.getByRole('button', { name: /登录/ });

      // 只填写用户名
      await user.type(usernameInput, 'testuser');
      await user.click(submitButton);

      // 验证错误信息
      await waitFor(() => {
        expect(screen.getByText('请输入密码')).toBeInTheDocument();
      });

      // 验证onLogin未被调用
      expect(mockOnLogin).not.toHaveBeenCalled();
    });

    test('应该验证两个字段都必填', async () => {
      const user = userEvent.setup();
      renderLogin({ onLogin: mockOnLogin });

      const submitButton = screen.getByRole('button', { name: /登录/ });
      
      // 不填写任何字段直接提交
      await user.click(submitButton);

      // 验证两个错误信息都显示
      await waitFor(() => {
        expect(screen.getByText('请输入用户名')).toBeInTheDocument();
        expect(screen.getByText('请输入密码')).toBeInTheDocument();
      });

      // 验证onLogin未被调用
      expect(mockOnLogin).not.toHaveBeenCalled();
    });
  });

  describe('表单提交测试', () => {
    test('应该在填写完整信息后成功提交', async () => {
      const user = userEvent.setup();
      mockOnLogin.mockResolvedValue(true);

      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const submitButton = screen.getByRole('button', { name: /登录/ });

      // 填写表单
      await user.type(usernameInput, 'admin');
      await user.type(passwordInput, 'admin123');

      // 提交表单
      await user.click(submitButton);

      // 验证onLogin被正确调用
      await waitFor(() => {
        expect(mockOnLogin).toHaveBeenCalledWith({
          username: 'admin',
          password: 'admin123'
        });
      });
    });

    test('应该在提交时显示加载状态', async () => {
      const user = userEvent.setup();
      // 模拟延迟的登录请求
      mockOnLogin.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve(true), 50)));

      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');

      // 填写表单
      await user.type(usernameInput, 'admin');
      await user.type(passwordInput, 'admin123');

      // 提交表单
      const submitButton = screen.getByRole('button', { name: /登录/ });
      await user.click(submitButton);

      // 验证加载状态文本
      expect(screen.getByText('登录中...')).toBeInTheDocument();

      // 等待加载完成
      await waitFor(() => {
        expect(screen.getByText('登录')).toBeInTheDocument();
      }, { timeout: 3000 });
    });

    test('应该处理登录成功的情况', async () => {
      const user = userEvent.setup();
      mockOnLogin.mockResolvedValue(true);

      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const submitButton = screen.getByRole('button', { name: /登录/ });

      // 填写并提交表单
      await user.type(usernameInput, 'admin');
      await user.type(passwordInput, 'admin123');
      await user.click(submitButton);

      // 验证登录函数被调用
      await waitFor(() => {
        expect(mockOnLogin).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('错误处理测试', () => {
    test('应该处理登录失败的情况', async () => {
      const user = userEvent.setup();
      mockOnLogin.mockResolvedValue(false);

      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const submitButton = screen.getByRole('button', { name: /登录/ });

      // 填写并提交表单
      await user.type(usernameInput, 'wronguser');
      await user.type(passwordInput, 'wrongpass');
      await user.click(submitButton);

      // 验证错误信息显示
      await waitFor(() => {
        expect(screen.getByText('用户名或密码错误')).toBeInTheDocument();
      });
    });

    test('应该处理登录异常的情况', async () => {
      const user = userEvent.setup();
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockOnLogin.mockRejectedValue(new Error('网络错误'));

      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const submitButton = screen.getByRole('button', { name: /登录/ });

      // 填写并提交表单
      await user.type(usernameInput, 'admin');
      await user.type(passwordInput, 'admin123');
      await user.click(submitButton);

      // 验证错误被记录
      await waitFor(() => {
        expect(consoleError).toHaveBeenCalledWith('登录错误:', expect.any(Error));
      });

      consoleError.mockRestore();
    });
  });

  describe('用户交互测试', () => {
    test('应该支持键盘导航', async () => {
      const user = userEvent.setup();
      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');

      // 测试Tab键导航
      await user.click(usernameInput);
      expect(usernameInput).toHaveFocus();

      await user.tab();
      expect(passwordInput).toHaveFocus();

      await user.tab();
      const submitButton = screen.getByRole('button', { name: /登录/ });
      expect(submitButton).toHaveFocus();
    });

    test('应该支持Enter键提交表单', async () => {
      const user = userEvent.setup();
      mockOnLogin.mockResolvedValue(true);

      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');

      // 填写表单
      await user.type(usernameInput, 'admin');
      await user.type(passwordInput, 'admin123');

      // 在密码框按Enter提交
      await user.keyboard('{Enter}');

      // 验证表单被提交
      await waitFor(() => {
        expect(mockOnLogin).toHaveBeenCalledWith({
          username: 'admin',
          password: 'admin123'
        });
      });
    });

    test('应该正确渲染密码输入框', async () => {
      renderLogin({ onLogin: mockOnLogin });

      const passwordInput = screen.getByPlaceholderText('密码');

      // 验证密码输入框存在
      expect(passwordInput).toBeInTheDocument();

      // 验证初始状态是密码类型
      expect(passwordInput).toHaveAttribute('type', 'password');

      // 验证密码输入框在密码容器中
      const passwordWrapper = passwordInput.closest('.ant-input-password');
      expect(passwordWrapper).toBeInTheDocument();
    });
  });

  describe('边界情况测试', () => {
    test('应该处理空字符串输入', async () => {
      const user = userEvent.setup();
      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const submitButton = screen.getByRole('button', { name: /登录/ });

      // 清空输入框并输入空格字符
      await user.clear(usernameInput);
      await user.clear(passwordInput);
      await user.type(usernameInput, '   ');
      await user.type(passwordInput, '   ');

      // 清空输入框（模拟用户删除空格）
      await user.clear(usernameInput);
      await user.clear(passwordInput);

      await user.click(submitButton);

      // 验证仍然显示必填错误
      await waitFor(() => {
        expect(screen.getByText('请输入用户名')).toBeInTheDocument();
        expect(screen.getByText('请输入密码')).toBeInTheDocument();
      });
    });

    test('应该处理特殊字符输入', async () => {
      const user = userEvent.setup();
      mockOnLogin.mockResolvedValue(true);

      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const submitButton = screen.getByRole('button', { name: /登录/ });

      // 输入包含特殊字符的用户名和密码
      await user.type(usernameInput, '<EMAIL>');
      await user.type(passwordInput, 'pass!@#$%^&*()');
      await user.click(submitButton);

      // 验证特殊字符被正确处理
      await waitFor(() => {
        expect(mockOnLogin).toHaveBeenCalledWith({
          username: '<EMAIL>',
          password: 'pass!@#$%^&*()'
        });
      });
    });

    test('应该处理长文本输入', async () => {
      const user = userEvent.setup();
      mockOnLogin.mockResolvedValue(true);

      renderLogin({ onLogin: mockOnLogin });

      const usernameInput = screen.getByPlaceholderText('用户名');
      const passwordInput = screen.getByPlaceholderText('密码');
      const submitButton = screen.getByRole('button', { name: /登录/ });

      const longUsername = 'a'.repeat(100);
      const longPassword = 'b'.repeat(100);

      // 输入长文本
      await user.type(usernameInput, longUsername);
      await user.type(passwordInput, longPassword);
      await user.click(submitButton);

      // 验证长文本被正确处理
      await waitFor(() => {
        expect(mockOnLogin).toHaveBeenCalledWith({
          username: longUsername,
          password: longPassword
        });
      });
    });
  });
});
