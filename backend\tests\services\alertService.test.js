// Mock数据库模型
jest.mock('../../src/models', () => ({
  Inventory: {
    aggregate: jest.fn()
  },
  Product: {
    aggregate: jest.fn()
  }
}));

const alertService = require('../../src/services/alertService');
const { Inventory, Product } = require('../../src/models');

describe('AlertService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkLowStockAlerts', () => {
    it('应该检测到低库存商品', async () => {
      const mockLowStockData = [
        {
          product_id: '507f1f77bcf86cd799439011',
          current_stock: 5,
          reorder_point: 10,
          location: 'A-01',
          product: {
            name: '测试商品',
            category: 'produce',
            barcode: '1234567890123',
            unit_price: 10.5
          }
        }
      ];

      Inventory.aggregate.mockResolvedValue(mockLowStockData);

      const alerts = await alertService.checkLowStockAlerts();

      expect(alerts).toHaveLength(1);
      expect(alerts[0]).toMatchObject({
        type: 'LOW_STOCK',
        priority: 'medium',
        title: '低库存预警',
        message: '测试商品 库存不足'
      });
      expect(alerts[0].details).toMatchObject({
        product_name: '测试商品',
        current_stock: 5,
        reorder_point: 10,
        shortage: 5
      });
    });

    it('应该处理空结果', async () => {
      Inventory.aggregate.mockResolvedValue([]);

      const alerts = await alertService.checkLowStockAlerts();

      expect(alerts).toHaveLength(0);
    });

    it('应该处理数据库错误', async () => {
      Inventory.aggregate.mockRejectedValue(new Error('Database error'));

      const alerts = await alertService.checkLowStockAlerts();

      expect(alerts).toHaveLength(0);
    });
  });

  describe('checkOutOfStockAlerts', () => {
    it('应该检测到零库存商品', async () => {
      const mockOutOfStockData = [
        {
          product_id: '507f1f77bcf86cd799439011',
          current_stock: 0,
          reorder_point: 10,
          location: 'A-01',
          product: {
            name: '缺货商品',
            category: 'produce',
            barcode: '1234567890123',
            unit_price: 15.0
          }
        }
      ];

      Inventory.aggregate.mockResolvedValue(mockOutOfStockData);

      const alerts = await alertService.checkOutOfStockAlerts();

      expect(alerts).toHaveLength(1);
      expect(alerts[0]).toMatchObject({
        type: 'OUT_OF_STOCK',
        priority: 'high',
        title: '零库存预警',
        message: '缺货商品 已缺货'
      });
      expect(alerts[0].details.current_stock).toBe(0);
    });
  });

  describe('checkExpiringAlerts', () => {
    it('应该检测到临期商品', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 3); // 3天后过期

      const mockExpiringData = [
        {
          _id: '507f1f77bcf86cd799439011',
          name: '临期商品',
          category: 'produce',
          barcode: '1234567890123',
          expiry_date: futureDate,
          unit_price: 8.0,
          inventory: {
            current_stock: 20,
            location: 'B-02'
          }
        }
      ];

      Product.aggregate.mockResolvedValue(mockExpiringData);

      const alerts = await alertService.checkExpiringAlerts();

      expect(alerts).toHaveLength(1);
      expect(alerts[0]).toMatchObject({
        type: 'EXPIRING_SOON',
        priority: 'medium',
        title: '临期商品预警'
      });
      expect(alerts[0].message).toContain('临期商品');
      expect(alerts[0].message).toContain('3 天后过期');
      expect(alerts[0].details.days_to_expiry).toBe(3);
    });
  });

  describe('checkExpiredAlerts', () => {
    it('应该检测到过期商品', async () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 2); // 2天前过期

      const mockExpiredData = [
        {
          _id: '507f1f77bcf86cd799439011',
          name: '过期商品',
          category: 'produce',
          barcode: '1234567890123',
          expiry_date: pastDate,
          unit_price: 12.0,
          inventory: {
            current_stock: 5,
            location: 'C-03'
          }
        }
      ];

      Product.aggregate.mockResolvedValue(mockExpiredData);

      const alerts = await alertService.checkExpiredAlerts();

      expect(alerts).toHaveLength(1);
      expect(alerts[0]).toMatchObject({
        type: 'EXPIRED',
        priority: 'high',
        title: '过期商品预警'
      });
      expect(alerts[0].message).toContain('过期商品');
      expect(alerts[0].message).toContain('已过期 2 天');
      expect(alerts[0].details.days_expired).toBe(2);
    });
  });

  describe('checkNegativeStockAlerts', () => {
    it('应该检测到负库存商品', async () => {
      const mockNegativeStockData = [
        {
          product_id: '507f1f77bcf86cd799439011',
          current_stock: -5,
          location: 'D-04',
          product: {
            name: '负库存商品',
            category: 'produce',
            barcode: '1234567890123'
          }
        }
      ];

      Inventory.aggregate.mockResolvedValue(mockNegativeStockData);

      const alerts = await alertService.checkNegativeStockAlerts();

      expect(alerts).toHaveLength(1);
      expect(alerts[0]).toMatchObject({
        type: 'NEGATIVE_STOCK',
        priority: 'critical',
        title: '负库存预警',
        message: '负库存商品 库存为负数'
      });
      expect(alerts[0].details.current_stock).toBe(-5);
    });
  });

  describe('checkAllAlerts', () => {
    it('应该检查所有类型的预警', async () => {
      // Mock各种预警数据
      Inventory.aggregate
        .mockResolvedValueOnce([]) // checkLowStockAlerts
        .mockResolvedValueOnce([]) // checkOutOfStockAlerts
        .mockResolvedValueOnce([]); // checkNegativeStockAlerts

      Product.aggregate
        .mockResolvedValueOnce([]) // checkExpiringAlerts
        .mockResolvedValueOnce([]); // checkExpiredAlerts

      const result = await alertService.checkAllAlerts();

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('alerts');
      expect(result.data).toHaveProperty('summary');
      expect(result.data).toHaveProperty('checkTime');
      expect(Array.isArray(result.data.alerts)).toBe(true);
    });

    it('应该正确生成预警摘要', async () => {
      const mockLowStockData = [
        {
          product_id: '507f1f77bcf86cd799439011',
          current_stock: 5,
          reorder_point: 10,
          location: 'A-01',
          product: {
            name: '测试商品1',
            category: 'produce',
            barcode: '1234567890123',
            unit_price: 10.5
          }
        }
      ];

      const mockOutOfStockData = [
        {
          product_id: '507f1f77bcf86cd799439012',
          current_stock: 0,
          reorder_point: 5,
          location: 'A-02',
          product: {
            name: '测试商品2',
            category: 'produce',
            barcode: '1234567890124',
            unit_price: 8.0
          }
        }
      ];

      Inventory.aggregate
        .mockResolvedValueOnce(mockLowStockData) // checkLowStockAlerts
        .mockResolvedValueOnce(mockOutOfStockData) // checkOutOfStockAlerts
        .mockResolvedValueOnce([]); // checkNegativeStockAlerts

      Product.aggregate
        .mockResolvedValueOnce([]) // checkExpiringAlerts
        .mockResolvedValueOnce([]); // checkExpiredAlerts

      const result = await alertService.checkAllAlerts();

      expect(result.success).toBe(true);
      expect(result.data.summary).toMatchObject({
        total: 2,
        critical: 0,
        high: 1,
        medium: 1,
        low: 0
      });
      expect(result.data.summary.byType).toMatchObject({
        LOW_STOCK: 1,
        OUT_OF_STOCK: 1
      });
    });

    it('应该按优先级排序预警', async () => {
      const mockNegativeStockData = [
        {
          product_id: '507f1f77bcf86cd799439011',
          current_stock: -5,
          location: 'D-04',
          product: {
            name: '负库存商品',
            category: 'produce',
            barcode: '1234567890123'
          }
        }
      ];

      const mockLowStockData = [
        {
          product_id: '507f1f77bcf86cd799439012',
          current_stock: 5,
          reorder_point: 10,
          location: 'A-01',
          product: {
            name: '低库存商品',
            category: 'produce',
            barcode: '1234567890124',
            unit_price: 10.5
          }
        }
      ];

      Inventory.aggregate
        .mockResolvedValueOnce(mockLowStockData) // checkLowStockAlerts
        .mockResolvedValueOnce([]) // checkOutOfStockAlerts
        .mockResolvedValueOnce(mockNegativeStockData); // checkNegativeStockAlerts

      Product.aggregate
        .mockResolvedValueOnce([]) // checkExpiringAlerts
        .mockResolvedValueOnce([]); // checkExpiredAlerts

      const result = await alertService.checkAllAlerts();

      expect(result.success).toBe(true);
      expect(result.data.alerts).toHaveLength(2);
      
      // 第一个应该是critical优先级的负库存预警
      expect(result.data.alerts[0].priority).toBe('critical');
      expect(result.data.alerts[0].type).toBe('NEGATIVE_STOCK');
      
      // 第二个应该是medium优先级的低库存预警
      expect(result.data.alerts[1].priority).toBe('medium');
      expect(result.data.alerts[1].type).toBe('LOW_STOCK');
    });
  });

  describe('getAlertConfig', () => {
    it('应该返回预警配置', () => {
      const config = alertService.getAlertConfig();

      expect(config).toHaveProperty('expiringDays');
      expect(config).toHaveProperty('lowStockMultiplier');
      expect(config).toHaveProperty('enabledAlerts');
      expect(config).toHaveProperty('alertTypes');
      expect(Array.isArray(config.enabledAlerts)).toBe(true);
      expect(typeof config.alertTypes).toBe('object');
    });
  });

  describe('updateAlertConfig', () => {
    it('应该更新预警配置', () => {
      const newConfig = {
        expiringDays: 14,
        lowStockMultiplier: 1.5,
        enabledAlerts: ['LOW_STOCK', 'OUT_OF_STOCK']
      };

      const result = alertService.updateAlertConfig(newConfig);

      expect(result.success).toBe(true);
      expect(result.config.expiringDays).toBe(14);
      expect(result.config.lowStockMultiplier).toBe(1.5);
      expect(result.config.enabledAlerts).toEqual(['LOW_STOCK', 'OUT_OF_STOCK']);
    });

    it('应该验证配置值的范围', () => {
      const invalidConfig = {
        expiringDays: 50, // 超过最大值30
        lowStockMultiplier: 10 // 超过最大值5.0
      };

      const result = alertService.updateAlertConfig(invalidConfig);

      expect(result.success).toBe(true);
      expect(result.config.expiringDays).toBe(30); // 应该被限制为30
      expect(result.config.lowStockMultiplier).toBe(5.0); // 应该被限制为5.0
    });

    it('应该过滤无效的预警类型', () => {
      const configWithInvalidTypes = {
        enabledAlerts: ['LOW_STOCK', 'INVALID_TYPE', 'OUT_OF_STOCK']
      };

      const result = alertService.updateAlertConfig(configWithInvalidTypes);

      expect(result.success).toBe(true);
      expect(result.config.enabledAlerts).toEqual(['LOW_STOCK', 'OUT_OF_STOCK']);
    });
  });

  describe('generateAlertSummary', () => {
    it('应该正确生成预警摘要', () => {
      const alerts = [
        { type: 'LOW_STOCK', priority: 'medium' },
        { type: 'OUT_OF_STOCK', priority: 'high' },
        { type: 'NEGATIVE_STOCK', priority: 'critical' },
        { type: 'LOW_STOCK', priority: 'medium' }
      ];

      const summary = alertService.generateAlertSummary(alerts);

      expect(summary).toMatchObject({
        total: 4,
        critical: 1,
        high: 1,
        medium: 2,
        low: 0,
        byType: {
          LOW_STOCK: 2,
          OUT_OF_STOCK: 1,
          NEGATIVE_STOCK: 1
        }
      });
    });

    it('应该处理空预警列表', () => {
      const summary = alertService.generateAlertSummary([]);

      expect(summary).toMatchObject({
        total: 0,
        critical: 0,
        high: 0,
        medium: 0,
        low: 0,
        byType: {}
      });
    });
  });
});
