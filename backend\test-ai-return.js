require('dotenv').config();
const returnPolicyService = require('./src/services/returnPolicyService');

/**
 * AI辅助退货功能测试脚本
 * 测试退货原因分析、政策匹配和智能决策
 */

async function testAIReturnFeatures() {
  console.log('🚀 开始AI辅助退货功能测试...\n');

  try {
    // 测试数据
    const testCases = [
      {
        name: '质量问题退货',
        reason: '收到的鸡肉有异味，怀疑变质了，不敢食用',
        productInfo: {
          name: '走地鸡',
          category: '禽类',
          price: 25.99
        },
        purchaseInfo: {
          purchase_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前
          pickup_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)    // 1天前
        }
      },
      {
        name: '商品错误退货',
        reason: '订的是有机蔬菜，但收到的是普通蔬菜',
        productInfo: {
          name: '有机蔬菜包',
          category: '蔬菜',
          price: 18.50
        },
        purchaseInfo: {
          purchase_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          pickup_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
        }
      },
      {
        name: '不满意退货',
        reason: '蔬菜不够新鲜，叶子有点黄',
        productInfo: {
          name: '绿叶蔬菜',
          category: '蔬菜',
          price: 12.00
        },
        purchaseInfo: {
          purchase_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          pickup_date: new Date(Date.now() - 12 * 60 * 60 * 1000) // 12小时前
        }
      },
      {
        name: '超时退货申请',
        reason: '商品质量有问题',
        productInfo: {
          name: '干货包',
          category: '干货',
          price: 35.00
        },
        purchaseInfo: {
          purchase_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前
          pickup_date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000)    // 6天前
        }
      }
    ];

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`📋 测试案例 ${i + 1}: ${testCase.name}`);
      console.log(`退货原因: "${testCase.reason}"`);
      console.log(`产品: ${testCase.productInfo.name} (${testCase.productInfo.category})`);

      // 测试1: AI原因分析
      console.log('\n🔍 步骤1: AI原因分析');
      const reasonAnalysis = await returnPolicyService.analyzeReturnReason(
        testCase.reason,
        testCase.productInfo,
        testCase.purchaseInfo
      );

      if (reasonAnalysis.success) {
        console.log('✅ 原因分析成功');
        console.log(`- 类别: ${reasonAnalysis.category}`);
        console.log(`- 严重程度: ${reasonAnalysis.severity}`);
        console.log(`- 质量问题: ${reasonAnalysis.is_quality_issue ? '是' : '否'}`);
        console.log(`- 有效原因: ${reasonAnalysis.is_valid_reason ? '是' : '否'}`);
        console.log(`- 置信度: ${reasonAnalysis.confidence.toFixed(2)}`);
        console.log(`- 建议处理: ${reasonAnalysis.suggested_action}`);
      } else {
        console.log('❌ 原因分析失败');
        continue;
      }

      // 测试2: 政策匹配
      console.log('\n📜 步骤2: 政策匹配');
      const mockReturnRequest = {
        total_amount: testCase.productInfo.price,
        items: [{
          product_name: testCase.productInfo.name,
          reason: testCase.reason,
          quantity: 1,
          unit_price: testCase.productInfo.price
        }],
        purchase_info: testCase.purchaseInfo
      };

      const policyMatch = await returnPolicyService.matchReturnPolicy(
        reasonAnalysis,
        mockReturnRequest
      );

      if (policyMatch.success) {
        console.log('✅ 政策匹配成功');
        console.log(`- 匹配政策数量: ${policyMatch.policies.length}`);
        
        if (policyMatch.bestMatch) {
          console.log(`- 最佳匹配: ${policyMatch.bestMatch.policy.name}`);
          console.log(`- 匹配度: ${policyMatch.bestMatch.matchScore.toFixed(2)}`);
          console.log(`- 适用规则: ${policyMatch.bestMatch.applicableRules.length}条`);
        }
      } else {
        console.log('❌ 政策匹配失败');
        console.log(`- 原因: ${policyMatch.message}`);
      }

      // 测试3: AI决策生成
      console.log('\n🤖 步骤3: AI决策生成');
      const decision = await returnPolicyService.generateReturnDecision(
        reasonAnalysis,
        policyMatch,
        mockReturnRequest
      );

      if (decision.success) {
        console.log('✅ AI决策生成成功');
        console.log(`- 决策: ${decision.decision}`);
        console.log(`- 置信度: ${decision.confidence.toFixed(2)}`);
        console.log(`- 建议处理: ${decision.recommended_action}`);
        console.log(`- 退款金额: $${decision.refund_amount.toFixed(2)}`);
        console.log(`- 退款比例: ${decision.refund_percentage}%`);
        console.log(`- 风险级别: ${decision.risk_level}`);
        console.log(`- 判断理由: ${decision.reasoning}`);
      } else {
        console.log('❌ AI决策生成失败');
      }

      // 测试4: 表单填充建议
      console.log('\n📝 步骤4: 表单填充建议');
      const suggestions = returnPolicyService.generateFormSuggestions(
        reasonAnalysis,
        decision
      );

      console.log('✅ 表单建议生成成功');
      console.log(`- 建议优先级: ${suggestions.priority}`);
      console.log(`- 退款方式: ${suggestions.refund_method}`);
      console.log(`- 处理备注: ${suggestions.processing_notes}`);
      console.log(`- 后续动作: ${suggestions.follow_up_actions.length}项`);
      console.log(`- 客户沟通: ${suggestions.customer_communication.substring(0, 50)}...`);

      console.log('\n' + '='.repeat(80) + '\n');
    }

    // 测试5: 综合性能测试
    console.log('📊 步骤5: 性能测试');
    const startTime = Date.now();
    
    const performanceTest = testCases[0]; // 使用第一个测试案例
    
    const perfReasonAnalysis = await returnPolicyService.analyzeReturnReason(
      performanceTest.reason,
      performanceTest.productInfo,
      performanceTest.purchaseInfo
    );
    
    const perfPolicyMatch = await returnPolicyService.matchReturnPolicy(
      perfReasonAnalysis,
      {
        total_amount: performanceTest.productInfo.price,
        items: [{ reason: performanceTest.reason }],
        purchase_info: performanceTest.purchaseInfo
      }
    );
    
    const perfDecision = await returnPolicyService.generateReturnDecision(
      perfReasonAnalysis,
      perfPolicyMatch,
      { total_amount: performanceTest.productInfo.price }
    );
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    console.log('✅ 性能测试完成');
    console.log(`- 总处理时间: ${totalTime}ms`);
    console.log(`- 平均每步时间: ${(totalTime / 3).toFixed(1)}ms`);

    console.log('\n🎉 AI辅助退货功能测试完成！');
    console.log('\n📊 测试总结:');
    console.log('- AI原因分析: ✅');
    console.log('- 政策匹配: ✅');
    console.log('- AI决策生成: ✅');
    console.log('- 表单填充建议: ✅');
    console.log('- 性能测试: ✅');
    console.log(`- 测试案例数量: ${testCases.length}`);
    console.log(`- 总处理时间: ${totalTime}ms`);

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  testAIReturnFeatures().catch(console.error);
}

module.exports = testAIReturnFeatures;
