# API使用指南

> **版本**: v3.0  
> **创建时间**: 2025-06-15  
> **最后更新**: 2025-06-15  
> **适用范围**: 第三阶段AI客服功能

## 📋 目录

- [概述](#概述)
- [认证和权限](#认证和权限)
- [AI客服API使用](#ai客服api使用)
- [政策管理API使用](#政策管理api使用)
- [实时聊天集成](#实时聊天集成)
- [AI辅助退货使用](#ai辅助退货使用)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)

## 概述

本指南介绍如何使用第三阶段新增的AI客服功能API，包括政策管理、实时聊天、AI辅助退货等核心功能。

### 新增功能概览
- 🤖 **AI智能对话**: 基于DeepSeek的智能客服
- 📜 **政策管理**: 动态政策加载和版本控制
- 🔍 **RAG检索**: 多源信息检索和上下文生成
- 💬 **实时聊天**: WebSocket实时通信
- 🔄 **AI辅助退货**: 智能退货分析和决策

### 基础配置
```javascript
const API_BASE_URL = 'http://localhost:4000/api';
const WS_URL = 'http://localhost:4000';
```

## 认证和权限

### JWT认证
```javascript
// 登录获取token
const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'your_username',
    password: 'your_password'
  })
});

const { token } = await loginResponse.json();

// 在后续请求中使用token
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
};
```

### 权限级别
- **公开**: 政策查询、搜索
- **用户**: 聊天功能、退货申请
- **管理员**: 政策管理、统计数据

## AI客服API使用

### 1. 基础AI对话

```javascript
// 发送消息给AI
async function sendMessageToAI(message, sessionId) {
  const response = await fetch(`${API_BASE_URL}/chat/message`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      sessionId: sessionId,
      message: message,
      messageType: 'text'
    })
  });
  
  const result = await response.json();
  return result.data.aiResponse;
}

// 使用示例
const aiResponse = await sendMessageToAI('请问配送政策是什么？', 'session_001');
console.log('AI回复:', aiResponse.content);
```

### 2. 意图分析

```javascript
// 分析用户意图
async function analyzeUserIntent(message) {
  // 注意：这是内部API，通常在聊天消息处理中自动调用
  const response = await fetch(`${API_BASE_URL}/ai/analyze-intent`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({ message })
  });
  
  const result = await response.json();
  return result.data.intent;
}
```

### 3. 获取聊天历史

```javascript
// 获取会话历史
async function getChatHistory(sessionId, limit = 50) {
  const response = await fetch(
    `${API_BASE_URL}/chat/sessions/${sessionId}/messages?limit=${limit}`,
    { headers }
  );
  
  const result = await response.json();
  return result.data.messages;
}
```

## 政策管理API使用

### 1. 搜索政策

```javascript
// 搜索相关政策
async function searchPolicies(keyword, category = null) {
  const params = new URLSearchParams({
    keyword: keyword,
    active_only: 'true',
    limit: '10'
  });
  
  if (category) {
    params.append('category', category);
  }
  
  const response = await fetch(`${API_BASE_URL}/policy/search?${params}`);
  const result = await response.json();
  
  return result.data.results;
}

// 使用示例
const deliveryPolicies = await searchPolicies('配送', 'delivery');
deliveryPolicies.forEach(policy => {
  console.log(`${policy.name}: 相关性 ${policy.relevanceScore}`);
});
```

### 2. 获取政策详情

```javascript
// 获取完整政策信息
async function getPolicyDetails(policyId, includeVersions = false) {
  const params = includeVersions ? '?include_versions=true' : '';
  const response = await fetch(`${API_BASE_URL}/policy/${policyId}${params}`);
  
  const result = await response.json();
  return result.data;
}

// 使用示例
const policy = await getPolicyDetails('policy_001', true);
console.log('政策内容:', policy.current_content);
console.log('版本历史:', policy.versions);
```

### 3. 导入政策（管理员）

```javascript
// 从policy.json导入政策
async function importPolicies() {
  const response = await fetch(`${API_BASE_URL}/policy/import`, {
    method: 'POST',
    headers: headers
  });
  
  const result = await response.json();
  console.log(`导入成功: ${result.data.imported}个政策`);
  
  if (result.data.errors.length > 0) {
    console.log('导入错误:', result.data.errors);
  }
  
  return result;
}
```

### 4. 创建新政策（管理员）

```javascript
// 创建新政策
async function createPolicy(policyData) {
  const response = await fetch(`${API_BASE_URL}/policy`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(policyData)
  });
  
  return await response.json();
}

// 使用示例
const newPolicy = {
  name: '客户服务政策',
  category: 'other',
  description: '客户服务相关规定',
  current_content: [
    '客服工作时间：周一至周五 9:00-18:00',
    '响应时间：工作时间内2小时内回复'
  ],
  tags: ['客服', '服务时间'],
  priority: 6
};

const result = await createPolicy(newPolicy);
```

## 实时聊天集成

### 1. WebSocket连接

```javascript
import io from 'socket.io-client';

// 建立WebSocket连接
const socket = io(WS_URL, {
  auth: {
    token: token
  }
});

// 加入聊天
socket.emit('join-chat', {
  userId: 'user_123',
  sessionId: 'session_001'
});

// 监听AI响应
socket.on('ai-response', (data) => {
  console.log('AI回复:', data.content);
  displayMessage('ai', data.content);
});

// 监听用户消息
socket.on('user-message', (data) => {
  console.log('用户消息:', data.content);
  displayMessage('user', data.content);
});
```

### 2. 发送消息

```javascript
// 发送聊天消息
function sendChatMessage(message) {
  socket.emit('chat-message', {
    sessionId: 'session_001',
    message: message,
    messageType: 'text'
  });
}

// 发送输入状态
function sendTypingStatus(isTyping) {
  socket.emit('user-typing', {
    sessionId: 'session_001',
    isTyping: isTyping
  });
}
```

### 3. 获取会话状态

```javascript
// 获取会话状态
socket.emit('get-session-status', { sessionId: 'session_001' });

socket.on('session-status', (data) => {
  console.log('会话状态:', data);
  console.log('在线用户:', data.activeUsers);
  console.log('消息数量:', data.messageCount);
});
```

## AI辅助退货使用

### 1. 获取AI分析结果

```javascript
// 获取退货申请的AI分析
async function getReturnAIAnalysis(returnId) {
  const response = await fetch(`${API_BASE_URL}/returns/${returnId}/ai-analysis`, {
    headers: headers
  });
  
  const result = await response.json();
  return result.data;
}

// 使用示例
const analysis = await getReturnAIAnalysis('return_001');
console.log('退货类别:', analysis.category);
console.log('建议处理:', analysis.suggested_action);
console.log('置信度:', analysis.confidence);
```

### 2. 应用AI建议

```javascript
// 应用AI分析建议
async function applyAISuggestions(returnId, options = {}) {
  const response = await fetch(`${API_BASE_URL}/returns/${returnId}/apply-ai-suggestions`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      apply_decision: options.applyDecision || true,
      apply_suggestions: options.applySuggestions || true,
      manual_review: options.manualReview || false
    })
  });
  
  return await response.json();
}

// 使用示例
const result = await applyAISuggestions('return_001', {
  applyDecision: true,
  applySuggestions: true
});

console.log('应用结果:', result.message);
```

### 3. 完整退货处理流程

```javascript
// 完整的AI辅助退货处理
async function processReturnWithAI(returnId) {
  try {
    // 1. 获取AI分析
    const analysis = await getReturnAIAnalysis(returnId);
    console.log('AI分析完成:', analysis);
    
    // 2. 根据置信度决定是否自动处理
    if (analysis.confidence > 0.8 && analysis.category !== 'other') {
      // 高置信度，自动应用建议
      const result = await applyAISuggestions(returnId);
      console.log('自动处理完成:', result);
      return { status: 'auto_processed', result };
    } else {
      // 低置信度，标记为需要人工审核
      console.log('需要人工审核');
      return { status: 'manual_review_required', analysis };
    }
  } catch (error) {
    console.error('处理失败:', error);
    return { status: 'error', error: error.message };
  }
}
```

## 错误处理

### 常见错误类型

```javascript
// 统一错误处理函数
async function handleAPICall(apiCall) {
  try {
    const response = await apiCall();
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API错误 ${response.status}: ${errorData.message}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API调用失败:', error);
    
    // 根据错误类型进行处理
    if (error.message.includes('401')) {
      // 认证失败，重新登录
      redirectToLogin();
    } else if (error.message.includes('403')) {
      // 权限不足
      showPermissionError();
    } else if (error.message.includes('404')) {
      // 资源不存在
      showNotFoundError();
    } else {
      // 其他错误
      showGenericError(error.message);
    }
    
    throw error;
  }
}
```

### WebSocket错误处理

```javascript
// WebSocket连接错误处理
socket.on('connect_error', (error) => {
  console.error('WebSocket连接失败:', error);
  showConnectionError();
});

socket.on('disconnect', (reason) => {
  console.log('WebSocket断开连接:', reason);
  if (reason === 'io server disconnect') {
    // 服务器主动断开，需要重新连接
    socket.connect();
  }
});

// 重连机制
socket.on('reconnect', (attemptNumber) => {
  console.log('WebSocket重连成功:', attemptNumber);
  hideConnectionError();
});
```

## 最佳实践

### 1. 性能优化

```javascript
// 使用防抖减少API调用
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 搜索防抖
const debouncedSearch = debounce(async (keyword) => {
  const results = await searchPolicies(keyword);
  displaySearchResults(results);
}, 300);
```

### 2. 缓存策略

```javascript
// 简单的内存缓存
class APICache {
  constructor(ttl = 300000) { // 5分钟TTL
    this.cache = new Map();
    this.ttl = ttl;
  }
  
  set(key, value) {
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }
}

const cache = new APICache();

// 带缓存的政策查询
async function getCachedPolicy(policyId) {
  const cacheKey = `policy_${policyId}`;
  let policy = cache.get(cacheKey);
  
  if (!policy) {
    policy = await getPolicyDetails(policyId);
    cache.set(cacheKey, policy);
  }
  
  return policy;
}
```

### 3. 批量操作

```javascript
// 批量获取政策
async function getBatchPolicies(policyIds) {
  const promises = policyIds.map(id => getPolicyDetails(id));
  const results = await Promise.allSettled(promises);
  
  return results.map((result, index) => ({
    policyId: policyIds[index],
    success: result.status === 'fulfilled',
    data: result.status === 'fulfilled' ? result.value : null,
    error: result.status === 'rejected' ? result.reason : null
  }));
}
```

### 4. 实时状态管理

```javascript
// 聊天状态管理
class ChatManager {
  constructor() {
    this.sessions = new Map();
    this.socket = null;
  }
  
  connect(token) {
    this.socket = io(WS_URL, { auth: { token } });
    this.setupEventListeners();
  }
  
  setupEventListeners() {
    this.socket.on('ai-response', (data) => {
      this.handleAIResponse(data);
    });
    
    this.socket.on('user-message', (data) => {
      this.handleUserMessage(data);
    });
  }
  
  sendMessage(sessionId, message) {
    this.socket.emit('chat-message', {
      sessionId,
      message,
      messageType: 'text'
    });
  }
  
  handleAIResponse(data) {
    const session = this.sessions.get(data.sessionId);
    if (session) {
      session.addMessage('ai', data.content);
      session.updateUI();
    }
  }
}
```

---

**📝 文档维护**
- 创建者: AI Assistant
- 最后更新: 2025-06-15
- 版本: v3.0
- 状态: 第三阶段完成
