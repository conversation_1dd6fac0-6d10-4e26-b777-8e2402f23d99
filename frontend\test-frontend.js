/**
 * 前端功能测试脚本
 * 测试前端构建、组件加载和基本功能
 */

import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'

console.log('🚀 开始前端功能测试...\n')

// 测试结果统计
let totalTests = 0
let passedTests = 0
let failedTests = 0

// 测试函数
function test(name, testFn) {
  totalTests++
  try {
    console.log(`📋 测试: ${name}`)
    testFn()
    console.log(`✅ 通过: ${name}\n`)
    passedTests++
  } catch (error) {
    console.log(`❌ 失败: ${name}`)
    console.log(`   错误: ${error.message}\n`)
    failedTests++
  }
}

// 1. 测试项目结构
test('项目结构完整性', () => {
  const requiredFiles = [
    'package.json',
    'vite.config.js',
    'index.html',
    'src/App.jsx',
    'src/main.jsx',
    'src/services/api.js',
    'src/services/products.js',
    'src/services/inventory.js',
    'src/services/returns.js',
    'src/services/chat.js',
    'src/services/reports.js',
    'src/pages/Products.jsx',
    'src/pages/Inventory.jsx',
    'src/pages/Returns.jsx',
    'src/pages/Chat.jsx',
    'src/pages/Dashboard.jsx',
    'src/pages/Login.jsx'
  ]

  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      throw new Error(`缺少必需文件: ${file}`)
    }
  }
})

// 2. 测试package.json配置
test('package.json配置正确', () => {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  
  if (packageJson.name !== 'inventory-ai-frontend') {
    throw new Error('项目名称不正确')
  }
  
  if (!packageJson.scripts.build) {
    throw new Error('缺少build脚本')
  }
  
  if (!packageJson.scripts.dev) {
    throw new Error('缺少dev脚本')
  }
  
  const requiredDeps = ['react', 'react-dom', 'antd', 'axios', 'react-router-dom']
  for (const dep of requiredDeps) {
    if (!packageJson.dependencies[dep]) {
      throw new Error(`缺少必需依赖: ${dep}`)
    }
  }
})

// 3. 测试构建输出
test('构建输出文件存在', () => {
  const distPath = 'dist'
  if (!fs.existsSync(distPath)) {
    throw new Error('dist目录不存在，请先运行 npm run build')
  }
  
  const requiredFiles = ['index.html']
  for (const file of requiredFiles) {
    const filePath = path.join(distPath, file)
    if (!fs.existsSync(filePath)) {
      throw new Error(`构建输出缺少文件: ${file}`)
    }
  }
  
  // 检查是否有CSS和JS文件
  const files = fs.readdirSync(path.join(distPath, 'assets'))
  const hasCSS = files.some(file => file.endsWith('.css'))
  const hasJS = files.some(file => file.endsWith('.js'))
  
  if (!hasCSS) {
    throw new Error('构建输出缺少CSS文件')
  }
  
  if (!hasJS) {
    throw new Error('构建输出缺少JS文件')
  }
})

// 4. 测试组件文件语法
test('React组件语法正确', () => {
  const componentFiles = [
    'src/pages/Products.jsx',
    'src/pages/Inventory.jsx',
    'src/pages/Returns.jsx',
    'src/pages/Chat.jsx'
  ]
  
  for (const file of componentFiles) {
    const content = fs.readFileSync(file, 'utf8')
    
    // 检查基本React组件结构
    if (!content.includes('import React')) {
      throw new Error(`${file}: 缺少React导入`)
    }
    
    if (!content.includes('export default')) {
      throw new Error(`${file}: 缺少默认导出`)
    }
    
    // 检查是否使用了Ant Design组件
    if (!content.includes('antd')) {
      throw new Error(`${file}: 未使用Ant Design组件`)
    }
  }
})

// 5. 测试API服务文件
test('API服务配置正确', () => {
  const serviceFiles = [
    'src/services/api.js',
    'src/services/products.js',
    'src/services/inventory.js',
    'src/services/returns.js',
    'src/services/chat.js'
  ]
  
  for (const file of serviceFiles) {
    const content = fs.readFileSync(file, 'utf8')
    
    // 检查是否导出了服务对象
    if (!content.includes('export')) {
      throw new Error(`${file}: 缺少导出`)
    }
  }
  
  // 检查api.js是否有基础配置
  const apiContent = fs.readFileSync('src/services/api.js', 'utf8')
  if (!apiContent.includes('axios')) {
    throw new Error('api.js: 缺少axios配置')
  }
})

// 6. 测试Vite配置
test('Vite配置正确', () => {
  const viteConfig = fs.readFileSync('vite.config.js', 'utf8')
  
  if (!viteConfig.includes('@vitejs/plugin-react')) {
    throw new Error('Vite配置缺少React插件')
  }
  
  if (!viteConfig.includes('proxy')) {
    throw new Error('Vite配置缺少代理设置')
  }
})

// 7. 测试依赖安装
test('依赖安装完整', () => {
  if (!fs.existsSync('node_modules')) {
    throw new Error('node_modules目录不存在，请运行 npm install')
  }
  
  const requiredModules = ['react', 'antd', 'axios', 'react-router-dom']
  for (const module of requiredModules) {
    if (!fs.existsSync(`node_modules/${module}`)) {
      throw new Error(`缺少依赖模块: ${module}`)
    }
  }
})

// 输出测试结果
console.log('📊 测试结果统计:')
console.log(`总测试数: ${totalTests}`)
console.log(`通过: ${passedTests}`)
console.log(`失败: ${failedTests}`)
console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

if (failedTests === 0) {
  console.log('\n🎉 所有测试通过！前端功能正常')
  process.exit(0)
} else {
  console.log('\n⚠️  部分测试失败，请检查上述错误')
  process.exit(1)
}
