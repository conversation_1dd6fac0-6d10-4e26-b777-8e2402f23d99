import React from 'react'
import { Row, Col, Card, Statistic, Typography } from 'antd'
import { 
  ShoppingCartOutlined, 
  WarningOutlined, 
  CheckCircleOutlined,
  MessageOutlined 
} from '@ant-design/icons'

const { Title } = Typography

const Dashboard = () => {
  // 模拟统计数据
  const stats = [
    {
      title: '总商品数',
      value: 156,
      icon: <ShoppingCartOutlined />,
      color: 'primary',
      change: '+12%'
    },
    {
      title: '库存预警',
      value: 8,
      icon: <WarningOutlined />,
      color: 'warning',
      change: '-3%'
    },
    {
      title: '今日入库',
      value: 23,
      icon: <CheckCircleOutlined />,
      color: 'success',
      change: '+18%'
    },
    {
      title: 'AI客服会话',
      value: 45,
      icon: <MessageOutlined />,
      color: 'info',
      change: '+25%'
    }
  ]

  return (
    <div className="fade-in">
      <div className="page-header">
        <Title level={2}>仪表盘</Title>
        <p>欢迎回来！这里是您的库存管理概览</p>
      </div>

      <Row gutter={[24, 24]}>
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card className="stats-card">
              <div className={`stats-card-icon ${stat.color}`}>
                {stat.icon}
              </div>
              <div className="stats-card-value">{stat.value}</div>
              <div className="stats-card-label">{stat.title}</div>
              <div className={`stats-card-change ${stat.change.startsWith('+') ? 'positive' : 'negative'}`}>
                {stat.change} 较上周
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="库存趋势" className="content-card">
            <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <p style={{ color: '#999' }}>图表组件将在后续开发中添加</p>
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="最新动态" className="content-card">
            <div style={{ height: 300 }}>
              <div style={{ padding: '16px 0', borderBottom: '1px solid #f0f0f0' }}>
                <p style={{ margin: 0, fontWeight: 500 }}>新增商品：日本贝贝南瓜</p>
                <p style={{ margin: 0, color: '#999', fontSize: 12 }}>2分钟前</p>
              </div>
              <div style={{ padding: '16px 0', borderBottom: '1px solid #f0f0f0' }}>
                <p style={{ margin: 0, fontWeight: 500 }}>库存预警：走地鸡库存不足</p>
                <p style={{ margin: 0, color: '#999', fontSize: 12 }}>15分钟前</p>
              </div>
              <div style={{ padding: '16px 0' }}>
                <p style={{ margin: 0, fontWeight: 500 }}>退货申请：客户张三申请退货</p>
                <p style={{ margin: 0, color: '#999', fontSize: 12 }}>1小时前</p>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
