/**
 * 报表控制器
 * 处理报表相关的HTTP请求
 */

const reportService = require('../services/reportService');
const { Report, OperationLog } = require('../models');
const logger = require('../utils/logger');

class ReportController {
  /**
   * 生成报表
   */
  async generateReport(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const { 
        report_type = 'weekly',
        start_date,
        end_date,
        title,
        description,
        filters = {}
      } = req.body;

      logger.info('开始生成报表', {
        userId,
        report_type,
        start_date,
        end_date
      });

      // 验证日期
      const startDate = new Date(start_date);
      const endDate = new Date(end_date);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: '日期格式无效'
        });
      }

      if (startDate >= endDate) {
        return res.status(400).json({
          success: false,
          message: '开始日期必须早于结束日期'
        });
      }

      // 根据报表类型调用相应的生成方法
      let result;
      switch (report_type) {
        case 'weekly':
          result = await reportService.generateWeeklyReport({
            startDate,
            endDate,
            generatedBy: userId,
            filters,
            title,
            description
          });
          break;
        case 'monthly':
          result = await reportService.generateMonthlyReport({
            startDate,
            endDate,
            generatedBy: userId,
            filters,
            title,
            description
          });
          break;
        case 'custom':
          result = await reportService.generateCustomReport({
            startDate,
            endDate,
            generatedBy: userId,
            filters,
            title,
            description
          });
          break;
        default:
          return res.status(400).json({
            success: false,
            message: '不支持的报表类型'
          });
      }

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '报表生成失败',
          error: result.error
        });
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'report.generate',
        'report',
        result.data.report._id.toString(),
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          after_data: {
            report_id: result.data.report.report_id,
            report_type: result.data.report.report_type,
            generation_time_ms: result.data.generationTime
          },
          description: `生成${report_type}报表: ${result.data.report.report_id}`
        }
      );

      res.status(201).json({
        success: true,
        message: '报表生成成功',
        data: {
          report: result.data.report,
          generation_time: result.data.generationTime
        }
      });

    } catch (error) {
      logger.error('生成报表错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'report.generate',
        'report',
        'unknown',
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '报表生成失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取报表列表
   */
  async getReports(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const {
        page = 1,
        limit = 20,
        report_type,
        status,
        start_date,
        end_date,
        sort_by = 'generated_at',
        sort_order = 'desc'
      } = req.query;

      logger.info('获取报表列表', {
        userId,
        page,
        limit,
        report_type,
        status
      });

      // 构建查询条件
      const query = {};
      
      if (report_type) {
        query.report_type = report_type;
      }
      
      if (status) {
        query.status = status;
      }
      
      if (start_date && end_date) {
        query.generated_at = {
          $gte: new Date(start_date),
          $lte: new Date(end_date)
        };
      }

      // 构建排序选项
      const sortOptions = {};
      sortOptions[sort_by] = sort_order === 'desc' ? -1 : 1;

      // 分页参数
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // 查询报表
      const [reports, total] = await Promise.all([
        Report.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('generated_by', 'username name')
          .select('-data_sections'), // 列表不返回详细数据
        Report.countDocuments(query)
      ]);

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'report.view',
        'report',
        'list',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            total_reports: total,
            returned_reports: reports.length,
            filters: { report_type, status }
          },
          description: `查看报表列表: ${reports.length}条`
        }
      );

      res.json({
        success: true,
        data: {
          reports,
          pagination: {
            current_page: parseInt(page),
            per_page: parseInt(limit),
            total_items: total,
            total_pages: Math.ceil(total / parseInt(limit))
          },
          filters: {
            report_type,
            status,
            start_date,
            end_date
          }
        }
      });

    } catch (error) {
      logger.error('获取报表列表错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'report.view',
        'report',
        'list',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '获取报表列表失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取报表详情
   */
  async getReportById(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const { id } = req.params;

      logger.info('获取报表详情', { userId, reportId: id });

      const report = await Report.findById(id)
        .populate('generated_by', 'username name');

      if (!report) {
        return res.status(404).json({
          success: false,
          message: '报表不存在'
        });
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'report.view',
        'report',
        id,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            report_id: report.report_id,
            report_type: report.report_type,
            status: report.status
          },
          description: `查看报表详情: ${report.report_id}`
        }
      );

      res.json({
        success: true,
        data: { report }
      });

    } catch (error) {
      logger.error('获取报表详情错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'report.view',
        'report',
        req.params.id,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '获取报表详情失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 删除报表
   */
  async deleteReport(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const { id } = req.params;

      logger.info('删除报表', { userId, reportId: id });

      const report = await Report.findById(id);

      if (!report) {
        return res.status(404).json({
          success: false,
          message: '报表不存在'
        });
      }

      // 检查权限（只有生成者或管理员可以删除）
      if (report.generated_by.toString() !== userId && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '没有权限删除此报表'
        });
      }

      await Report.findByIdAndDelete(id);

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'report.delete',
        'report',
        id,
        {
          action: 'delete',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          before_data: {
            report_id: report.report_id,
            report_type: report.report_type
          },
          description: `删除报表: ${report.report_id}`
        }
      );

      res.json({
        success: true,
        message: '报表删除成功'
      });

    } catch (error) {
      logger.error('删除报表错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'report.delete',
        'report',
        req.params.id,
        {
          action: 'delete',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '删除报表失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取报表模板
   */
  async getReportTemplates(req, res) {
    try {
      const templates = [
        {
          id: 'weekly_standard',
          name: '标准周报',
          type: 'weekly',
          description: '包含库存状态、出入库统计、预警信息的标准周报',
          sections: ['summary', 'inventory', 'stock_movement', 'returns', 'alerts', 'performance']
        },
        {
          id: 'monthly_comprehensive',
          name: '综合月报',
          type: 'monthly',
          description: '全面的月度报表，包含详细的趋势分析和对比',
          sections: ['summary', 'inventory', 'stock_movement', 'returns', 'alerts', 'performance', 'trends']
        },
        {
          id: 'custom_flexible',
          name: '自定义报表',
          type: 'custom',
          description: '可自定义时间范围和内容的灵活报表',
          sections: ['summary', 'inventory', 'stock_movement', 'returns', 'alerts']
        }
      ];

      res.json({
        success: true,
        data: { templates }
      });

    } catch (error) {
      logger.error('获取报表模板错误:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = new ReportController();
