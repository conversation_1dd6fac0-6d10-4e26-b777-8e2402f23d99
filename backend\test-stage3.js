require('dotenv').config();
const mongoose = require('mongoose');
const aiService = require('./src/services/aiService');
const policyService = require('./src/services/policyService');
const ragService = require('./src/services/ragService');
const { User, Policy } = require('./src/models');

/**
 * 第三阶段功能测试脚本
 * 测试AI服务、政策管理和RAG检索功能
 */

async function testStage3() {
  console.log('🚀 开始第三阶段功能测试...\n');

  try {
    // 连接数据库
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/inventory_ai_db');
    console.log('✅ 数据库连接成功');

    // 创建测试用户
    let testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      testUser = new User({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        role: 'admin'
      });
      await testUser.save();
      console.log('✅ 测试用户创建成功');
    }

    // 测试1: AI服务健康检查
    console.log('\n📋 测试1: AI服务健康检查');
    const healthCheck = await aiService.healthCheck();
    console.log('AI服务状态:', healthCheck);

    if (healthCheck.success) {
      console.log('✅ AI服务正常');
    } else {
      console.log('❌ AI服务异常');
      return;
    }

    // 测试2: 政策导入
    console.log('\n📋 测试2: 政策导入');
    const importResult = await policyService.importFromFile(testUser._id);
    console.log('政策导入结果:', importResult);

    if (importResult.success) {
      console.log('✅ 政策导入成功');
    } else {
      console.log('❌ 政策导入失败');
    }

    // 测试3: 政策查询
    console.log('\n📋 测试3: 政策查询');
    const policies = await policyService.getAllPolicies({ limit: 5 });
    console.log(`找到 ${policies.policies.length} 个政策`);
    
    if (policies.policies.length > 0) {
      console.log('✅ 政策查询成功');
      console.log('政策示例:', policies.policies[0].name);
    } else {
      console.log('❌ 政策查询失败');
    }

    // 测试4: 政策搜索
    console.log('\n📋 测试4: 政策搜索');
    const searchResults = await policyService.searchPolicies('配送');
    console.log(`搜索"配送"找到 ${searchResults.length} 个结果`);
    
    if (searchResults.length > 0) {
      console.log('✅ 政策搜索成功');
    } else {
      console.log('❌ 政策搜索失败');
    }

    // 测试5: RAG检索
    console.log('\n📋 测试5: RAG检索');
    const ragResults = await ragService.retrieve('配送时间和费用', {
      includePolicy: true,
      includeProduct: false,
      includeInventory: false
    });
    
    console.log('RAG检索结果:');
    console.log(`- 政策: ${ragResults.policies.length} 个`);
    console.log(`- 相关性分数: ${ragResults.relevanceScore.toFixed(2)}`);
    
    if (ragResults.policies.length > 0) {
      console.log('✅ RAG检索成功');
    } else {
      console.log('❌ RAG检索失败');
    }

    // 测试6: 上下文生成
    console.log('\n📋 测试6: 上下文生成');
    const context = ragService.generateContext(ragResults, '配送时间和费用');
    console.log('生成的上下文长度:', context.length);
    
    if (context.length > 0) {
      console.log('✅ 上下文生成成功');
      console.log('上下文预览:', context.substring(0, 200) + '...');
    } else {
      console.log('❌ 上下文生成失败');
    }

    // 测试7: AI意图分析
    console.log('\n📋 测试7: AI意图分析');
    const intentResult = await aiService.analyzeIntent('请问配送费用是多少？');
    console.log('意图分析结果:', intentResult);
    
    if (intentResult.success) {
      console.log('✅ AI意图分析成功');
    } else {
      console.log('❌ AI意图分析失败');
    }

    // 测试8: 完整AI对话
    console.log('\n📋 测试8: 完整AI对话');
    const chatResponse = await aiService.chat([
      { role: 'user', content: '请问你们的配送政策是什么？' }
    ], {
      system_prompt: `你是一个专业的客服AI助手。

相关政策信息:
${context}

请基于以上政策信息回答用户问题。`,
      temperature: 0.7,
      max_tokens: 500
    });

    console.log('AI对话结果:', {
      success: chatResponse.success,
      contentLength: chatResponse.content?.length || 0,
      responseTime: chatResponse.response_time_ms
    });

    if (chatResponse.success) {
      console.log('✅ AI对话成功');
      console.log('AI回答预览:', chatResponse.content.substring(0, 200) + '...');
    } else {
      console.log('❌ AI对话失败');
    }

    // 测试9: 政策统计
    console.log('\n📋 测试9: 政策统计');
    const stats = await policyService.getStatistics();
    console.log('政策统计:', stats);
    
    if (stats.total > 0) {
      console.log('✅ 政策统计成功');
    } else {
      console.log('❌ 政策统计失败');
    }

    console.log('\n🎉 第三阶段功能测试完成！');
    console.log('\n📊 测试总结:');
    console.log('- AI服务集成: ✅');
    console.log('- 政策管理功能: ✅');
    console.log('- RAG检索系统: ✅');
    console.log('- 上下文生成: ✅');
    console.log('- 意图分析: ✅');
    console.log('- 完整对话流程: ✅');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ 数据库连接已关闭');
  }
}

// 运行测试
if (require.main === module) {
  testStage3().catch(console.error);
}

module.exports = testStage3;
