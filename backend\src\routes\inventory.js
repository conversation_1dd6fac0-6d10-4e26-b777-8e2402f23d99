const express = require('express');
const inventoryController = require('../controllers/inventoryController');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const router = express.Router();

// 所有库存路由都需要认证
router.use(authenticateToken);

// @desc    获取库存总览
// @route   GET /api/inventory
// @access  Private
router.get('/', requirePermission('inventory.read'), inventoryController.getInventoryOverview);

// @desc    获取库存预警
// @route   GET /api/inventory/alerts
// @access  Private
router.get('/alerts', requirePermission('inventory.read'), inventoryController.getInventoryAlerts);

// @desc    获取入库记录列表
// @route   GET /api/inventory/stockin-records
// @access  Private
router.get('/stockin-records', requirePermission('inventory.read'), inventoryController.getStockInRecords);

// @desc    创建入库单
// @route   POST /api/inventory/stockin
// @access  Private
router.post('/stockin', requirePermission('inventory.write'), inventoryController.createStockInRecord);

// @desc    处理入库单
// @route   POST /api/inventory/stockin/:id/process
// @access  Private
router.post('/stockin/:id/process', requirePermission('inventory.write'), inventoryController.processStockInRecord);

// @desc    批量创建入库记录
// @route   POST /api/inventory/stockin/batch
// @access  Private
router.post('/stockin/batch', requirePermission('inventory.write'), inventoryController.createBatchStockIn);

// @desc    批量处理入库记录
// @route   POST /api/inventory/stockin/batch/process
// @access  Private
router.post('/stockin/batch/process', requirePermission('inventory.write'), inventoryController.processBatchStockIn);

// @desc    取消入库记录
// @route   POST /api/inventory/stockin/:id/cancel
// @access  Private
router.post('/stockin/:id/cancel', requirePermission('inventory.write'), inventoryController.cancelStockIn);

// @desc    获取入库统计信息
// @route   GET /api/inventory/stockin/statistics
// @access  Private
router.get('/stockin/statistics', requirePermission('inventory.read'), inventoryController.getStockInStatistics);

module.exports = router;
