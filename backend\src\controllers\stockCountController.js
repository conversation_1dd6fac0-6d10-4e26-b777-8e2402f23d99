/**
 * 盘点控制器
 * 处理盘点任务相关的HTTP请求
 */

const stockCountService = require('../services/stockCountService');
const { StockCount, OperationLog } = require('../models');
const logger = require('../utils/logger');

class StockCountController {
  /**
   * 创建盘点计划
   */
  async createStockCountPlan(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const {
        title,
        description,
        count_type = 'partial',
        scheduled_date,
        deadline,
        location,
        categories = [],
        product_ids = [],
        settings = {}
      } = req.body;

      logger.info('创建盘点计划', {
        userId,
        count_type,
        scheduled_date,
        categories: categories.length,
        products: product_ids.length
      });

      // 验证必需字段
      if (!title || !scheduled_date) {
        return res.status(400).json({
          success: false,
          message: '标题和计划日期是必需的'
        });
      }

      // 验证日期
      const scheduledDate = new Date(scheduled_date);
      if (isNaN(scheduledDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: '计划日期格式无效'
        });
      }

      const result = await stockCountService.createStockCountPlan({
        title,
        description,
        count_type,
        scheduled_date: scheduledDate,
        deadline: deadline ? new Date(deadline) : null,
        location,
        categories,
        product_ids,
        created_by: userId,
        settings
      });

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '创建盘点计划失败',
          error: result.error
        });
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'stockcount.create',
        'stockcount',
        result.data.stockCount._id.toString(),
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          after_data: {
            count_id: result.data.stockCount.count_id,
            count_type: result.data.stockCount.count_type,
            total_items: result.data.stockCount.items.length
          },
          description: `创建盘点计划: ${result.data.stockCount.count_id}`
        }
      );

      res.status(201).json({
        success: true,
        message: '盘点计划创建成功',
        data: {
          stockCount: result.data.stockCount
        }
      });

    } catch (error) {
      logger.error('创建盘点计划错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'stockcount.create',
        'stockcount',
        'unknown',
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '创建盘点计划失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取盘点计划列表
   */
  async getStockCountPlans(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const {
        page = 1,
        limit = 20,
        count_type,
        status,
        start_date,
        end_date,
        location,
        assigned_to_me,
        sort_by = 'createdAt',
        sort_order = 'desc'
      } = req.query;

      logger.info('获取盘点计划列表', {
        userId,
        page,
        limit,
        count_type,
        status
      });

      // 构建查询条件
      const query = {};
      
      if (count_type) {
        query.count_type = count_type;
      }
      
      if (status) {
        query.status = status;
      }
      
      if (location) {
        query.location = { $regex: location, $options: 'i' };
      }
      
      if (start_date && end_date) {
        query.createdAt = {
          $gte: new Date(start_date),
          $lte: new Date(end_date)
        };
      }
      
      if (assigned_to_me === 'true') {
        query['assigned_to.user_id'] = userId;
      }

      // 构建排序选项
      const sortOptions = {};
      sortOptions[sort_by] = sort_order === 'desc' ? -1 : 1;

      // 分页参数
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // 查询盘点计划
      const [stockCounts, total] = await Promise.all([
        StockCount.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('created_by', 'username name')
          .populate('assigned_to.user_id', 'username name'),
        StockCount.countDocuments(query)
      ]);

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'stockcount.view',
        'stockcount',
        'list',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            total_plans: total,
            returned_plans: stockCounts.length,
            filters: { count_type, status, location }
          },
          description: `查看盘点计划列表: ${stockCounts.length}条`
        }
      );

      res.json({
        success: true,
        data: {
          stockCounts,
          pagination: {
            current_page: parseInt(page),
            per_page: parseInt(limit),
            total_items: total,
            total_pages: Math.ceil(total / parseInt(limit))
          },
          filters: {
            count_type,
            status,
            location,
            start_date,
            end_date,
            assigned_to_me
          }
        }
      });

    } catch (error) {
      logger.error('获取盘点计划列表错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'stockcount.view',
        'stockcount',
        'list',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '获取盘点计划列表失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取盘点计划详情
   */
  async getStockCountPlanById(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const { id } = req.params;

      logger.info('获取盘点计划详情', { userId, planId: id });

      const stockCount = await StockCount.findById(id)
        .populate('created_by', 'username name')
        .populate('assigned_to.user_id', 'username name')
        .populate('items.product_id', 'name category barcode unit_price')
        .populate('items.counted_by', 'username name')
        .populate('items.verified_by', 'username name');

      if (!stockCount) {
        return res.status(404).json({
          success: false,
          message: '盘点计划不存在'
        });
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'stockcount.view',
        'stockcount',
        id,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            count_id: stockCount.count_id,
            count_type: stockCount.count_type,
            status: stockCount.status
          },
          description: `查看盘点计划详情: ${stockCount.count_id}`
        }
      );

      res.json({
        success: true,
        data: { stockCount }
      });

    } catch (error) {
      logger.error('获取盘点计划详情错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'stockcount.view',
        'stockcount',
        req.params.id,
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '获取盘点计划详情失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 分配盘点任务
   */
  async assignStockCountTask(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const { id } = req.params;
      const { assignments } = req.body;

      logger.info('分配盘点任务', { userId, planId: id, assignments: assignments?.length });

      if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
        return res.status(400).json({
          success: false,
          message: '分配信息不能为空'
        });
      }

      const result = await stockCountService.assignStockCountTask(id, assignments);

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '分配盘点任务失败',
          error: result.error
        });
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'stockcount.assign',
        'stockcount',
        id,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            count_id: result.data.stockCount.count_id,
            assigned_users: result.data.stockCount.assigned_to.length
          },
          description: `分配盘点任务: ${result.data.stockCount.count_id}`
        }
      );

      res.json({
        success: true,
        message: '盘点任务分配成功',
        data: {
          stockCount: result.data.stockCount
        }
      });

    } catch (error) {
      logger.error('分配盘点任务错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'stockcount.assign',
        'stockcount',
        req.params.id,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '分配盘点任务失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 开始盘点
   */
  async startStockCount(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const { id } = req.params;

      logger.info('开始盘点', { userId, planId: id });

      const stockCount = await StockCount.findById(id);

      if (!stockCount) {
        return res.status(404).json({
          success: false,
          message: '盘点计划不存在'
        });
      }

      if (stockCount.status !== 'planned') {
        return res.status(400).json({
          success: false,
          message: '只能开始计划中的盘点任务'
        });
      }

      stockCount.startCount();
      await stockCount.save();

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'stockcount.start',
        'stockcount',
        id,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            count_id: stockCount.count_id,
            status: stockCount.status,
            start_date: stockCount.start_date
          },
          description: `开始盘点: ${stockCount.count_id}`
        }
      );

      res.json({
        success: true,
        message: '盘点已开始',
        data: { stockCount }
      });

    } catch (error) {
      logger.error('开始盘点错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'stockcount.start',
        'stockcount',
        req.params.id,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '开始盘点失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 提交盘点结果
   */
  async submitCountResults(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const { id } = req.params;
      const { count_results } = req.body;

      logger.info('提交盘点结果', { 
        userId, 
        planId: id, 
        results: count_results?.length 
      });

      if (!count_results || !Array.isArray(count_results)) {
        return res.status(400).json({
          success: false,
          message: '盘点结果不能为空'
        });
      }

      const result = await stockCountService.submitCountResults(id, count_results, userId);

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '提交盘点结果失败',
          error: result.error
        });
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'stockcount.submit',
        'stockcount',
        id,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            count_id: result.data.stockCount.count_id,
            submitted_items: count_results.length,
            discrepancies: result.data.analysis.discrepancy_items
          },
          description: `提交盘点结果: ${result.data.stockCount.count_id}`
        }
      );

      res.json({
        success: true,
        message: '盘点结果提交成功',
        data: {
          stockCount: result.data.stockCount,
          analysis: result.data.analysis
        }
      });

    } catch (error) {
      logger.error('提交盘点结果错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'stockcount.submit',
        'stockcount',
        req.params.id,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '提交盘点结果失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取盘点建议
   */
  async getCountRecommendations(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const {
        days_since_last_count = 90,
        low_stock_threshold = 0.2,
        high_value_threshold = 1000
      } = req.query;

      logger.info('获取盘点建议', { userId, days_since_last_count });

      const result = await stockCountService.generateCountRecommendations({
        days_since_last_count: parseInt(days_since_last_count),
        low_stock_threshold: parseFloat(low_stock_threshold),
        high_value_threshold: parseFloat(high_value_threshold)
      });

      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: '获取盘点建议失败',
          error: result.error
        });
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'stockcount.recommendations',
        'stockcount',
        'recommendations',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          after_data: {
            total_recommendations: result.data.summary.total_recommendations,
            high_priority: result.data.high_priority.length,
            medium_priority: result.data.medium_priority.length
          },
          description: `获取盘点建议: ${result.data.summary.total_recommendations}条`
        }
      );

      res.json({
        success: true,
        data: result.data
      });

    } catch (error) {
      logger.error('获取盘点建议错误:', error);

      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await OperationLog.logError(
        req.user?.userId,
        'stockcount.recommendations',
        'stockcount',
        'recommendations',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          error_message: error.message,
          description: '获取盘点建议失败'
        }
      );

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = new StockCountController();
