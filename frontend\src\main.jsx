import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import App from './App.jsx'
import './styles/index.css'

// 设置 dayjs 中文语言
dayjs.locale('zh-cn')

// Ant Design 主题配置
const theme = {
  token: {
    // 主色调
    colorPrimary: '#2C7AFF',
    colorSuccess: '#22C55E',
    colorError: '#EF4444',
    colorWarning: '#FFAA2C',
    
    // 圆角
    borderRadius: 4,
    borderRadiusLG: 12,
    
    // 字体
    fontFamily: 'Inter, "思源黑体", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    
    // 间距
    padding: 16,
    margin: 16,
    
    // 阴影
    boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
    boxShadowSecondary: '0 4px 12px rgba(0,0,0,0.08)',
  },
  components: {
    Button: {
      primaryShadow: '0 2px 4px rgba(44, 122, 255, 0.2)',
      defaultShadow: '0 2px 4px rgba(0,0,0,0.05)',
    },
    Card: {
      boxShadowTertiary: '0 2px 8px rgba(0,0,0,0.05)',
    },
    Table: {
      headerBg: '#F7F9FC',
      headerColor: '#667085',
      rowHoverBg: '#F7F9FC',
    },
    Layout: {
      siderBg: '#FFFFFF',
      headerBg: '#FFFFFF',
      bodyBg: '#F7F9FC',
    }
  }
}

// 隐藏加载动画
const hideLoading = () => {
  const loading = document.getElementById('loading')
  if (loading) {
    loading.style.display = 'none'
  }
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ConfigProvider 
      locale={zhCN} 
      theme={theme}
      componentSize="middle"
    >
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </ConfigProvider>
  </React.StrictMode>,
)

// 应用加载完成后隐藏加载动画
setTimeout(hideLoading, 100)
