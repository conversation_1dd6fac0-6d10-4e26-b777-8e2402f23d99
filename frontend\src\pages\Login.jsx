import React, { useState } from 'react'
import { Form, Input, But<PERSON>, Card, Typography, Space } from 'antd'
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

const Login = ({ onLogin }) => {
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()

  const handleSubmit = async (values) => {
    setLoading(true)
    try {
      const success = await onLogin(values)
      if (!success) {
        // 登录失败，重置表单
        form.setFields([
          {
            name: 'password',
            errors: ['用户名或密码错误']
          }
        ])
      }
    } catch (error) {
      console.error('登录错误:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="login-container">
      <Card className="login-form-wrapper">
        <div className="login-logo">
          <Title level={2} style={{ color: '#2C7AFF', marginBottom: 8 }}>
            HubGoodFood
          </Title>
          <Text type="secondary">仓库库存管理及AI客服系统</Text>
        </div>

        <Form
          form={form}
          name="login"
          className="login-form"
          onFinish={handleSubmit}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              {
                required: true,
                message: '请输入用户名'
              }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              {
                required: true,
                message: '请输入密码'
              }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="login-form-button btn-scale"
              loading={loading}
              icon={<LoginOutlined />}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Space direction="vertical" size="small">
            <Text type="secondary" style={{ fontSize: 12 }}>
              测试账号: admin / admin123
            </Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              © 2025 HubGoodFood. All rights reserved.
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default Login
