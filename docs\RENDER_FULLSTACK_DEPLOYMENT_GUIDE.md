# 🚀 Render全栈部署指南

## 📋 部署概览

本指南将帮助您在Render平台上部署Chat-AI-3.0项目的前后端服务。

### 🏗️ 架构说明
- **后端服务**: inventory-ai-backend (Node.js API)
- **前端服务**: inventory-ai-frontend (React SPA + Express服务器)
- **数据库**: MongoDB Atlas (云数据库)
- **文件存储**: AWS S3 (云存储)

## 🔧 部署前准备

### 1. 确保代码已推送到GitHub
```bash
git add .
git commit -m "Ready for Render fullstack deployment"
git push origin main
```

### 2. 准备必需的服务凭证
- MongoDB Atlas连接字符串
- AWS S3访问密钥
- DeepSeek AI API密钥

### 3. 安装前端依赖
```bash
cd frontend
npm install
```

## 🖥️ 后端服务部署

### 步骤1: 创建后端Web Service

1. 登录 [Render Dashboard](https://dashboard.render.com)
2. 点击 "New +" → "Web Service"
3. 连接您的GitHub仓库
4. 选择 `Chat-AI-3.0` 仓库

### 步骤2: 配置后端服务设置

```yaml
Name: inventory-ai-backend
Environment: Node
Region: Singapore
Branch: main
Root Directory: backend
Build Command: npm ci --only=production
Start Command: npm start
Health Check Path: /health
```

### 步骤3: 配置后端环境变量

在Render Dashboard的Environment页面中添加以下环境变量：

#### 必需配置（需要实际值）
```bash
MONGO_URI=mongodb+srv://prod_user:<EMAIL>/inventory_ai_prod
JWT_SECRET=71c255c5340f0bd7a75a6c8c6e1a376d7b904688f49696dbfcb480cfcc177417
AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_KEY
S3_BUCKET=inventory-assets-prod
DEEPSEEK_API_KEY=YOUR_DEEPSEEK_API_KEY
```

#### 自动配置
```bash
NODE_ENV=production
PORT=4000
DEEPSEEK_BASE_URL=https://llm.chutes.ai/v1
DEEPSEEK_MODEL=deepseek-ai/DeepSeek-V3-0324
AI_MOCK_MODE=false
FRONTEND_URL=https://inventory-ai-frontend.onrender.com
CORS_ORIGIN=https://inventory-ai-frontend.onrender.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200
LOG_LEVEL=warn
ENABLE_HTTPS_REDIRECT=true
TRUST_PROXY=true
SESSION_SECURE=true
```

### 步骤4: 部署后端服务

点击 "Create Web Service" 开始部署。等待部署完成并记录服务URL。

## 🌐 前端服务部署

### 步骤1: 创建前端Web Service

1. 在Render Dashboard中点击 "New +" → "Web Service"
2. 连接同一个GitHub仓库
3. 选择 `Chat-AI-3.0` 仓库

### 步骤2: 配置前端服务设置

```yaml
Name: inventory-ai-frontend
Environment: Node
Region: Singapore
Branch: main
Root Directory: frontend
Build Command: export NPM_CONFIG_WORKSPACES=false && rm -rf node_modules package-lock.json && npm cache clean --force && npm install @rollup/rollup-linux-x64-gnu terser --save-dev --no-workspaces && npm install --no-workspaces && NODE_ENV=production npm run build:prod
Start Command: npm run serve
Health Check Path: /health
```

### 步骤3: 配置前端环境变量

```bash
# 基础配置
NODE_ENV=production
PORT=3000

# API配置（使用实际的后端URL）
VITE_API_BASE_URL=https://inventory-ai-backend.onrender.com/api
VITE_SOCKET_URL=https://inventory-ai-backend.onrender.com

# 应用配置
VITE_APP_TITLE=库存管理系统 | HubGoodFood
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=production

# 功能开关
VITE_ENABLE_AI_CHAT=true
VITE_ENABLE_BARCODE_SCANNER=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_PWA=true

# UI配置
VITE_PRIMARY_COLOR=#2C7AFF
VITE_ENABLE_DARK_MODE=true
VITE_DEFAULT_LOCALE=zh-CN

# 调试配置（生产环境关闭）
VITE_DEBUG=false
VITE_SHOW_DEV_TOOLS=false
VITE_ENABLE_PERFORMANCE_MONITORING=true

# 安全配置
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true

# 性能配置
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_IMAGE_OPTIMIZATION=true
VITE_CACHE_STRATEGY=aggressive
```

### 步骤4: 部署前端服务

点击 "Create Web Service" 开始部署。

## 📊 部署验证

### 1. 检查服务状态

确认两个服务都显示为 "Live" 状态：
- `https://inventory-ai-backend.onrender.com`
- `https://inventory-ai-frontend.onrender.com`

### 2. 健康检查验证

```bash
# 检查后端健康状态
curl https://inventory-ai-backend.onrender.com/health

# 检查前端健康状态
curl https://inventory-ai-frontend.onrender.com/health
```

期望返回：
```json
{
  "status": "ok",
  "service": "inventory-ai-backend",
  "timestamp": "2025-06-15T...",
  "uptime": 123.456,
  "environment": "production"
}
```

### 3. 功能验证清单

#### 前端验证
- [ ] 页面正常加载
- [ ] 登录功能正常
- [ ] 路由导航正常
- [ ] API调用正常

#### 后端验证
- [ ] API端点响应正常
- [ ] 数据库连接正常
- [ ] WebSocket连接正常
- [ ] AI服务响应正常

#### 集成验证
- [ ] 前后端通信正常
- [ ] 实时聊天功能正常
- [ ] 文件上传功能正常（如果配置）
- [ ] 所有业务功能正常

## 🔍 故障排除

### 常见问题

#### 1. 前端构建失败
**常见错误**: `npm error 'target_platform' is not a valid npm option`
**原因**: 使用了过时的npm配置选项
**解决方案**:
```bash
# 本地测试构建
cd frontend
npm ci
npm run build:prod

# 如果遇到npm配置错误，使用简化的构建命令：
export NPM_CONFIG_WORKSPACES=false
rm -rf node_modules package-lock.json
npm cache clean --force
npm install @rollup/rollup-linux-x64-gnu terser --save-dev --no-workspaces
npm install --no-workspaces
NODE_ENV=production npm run build:prod
```

#### 2. Vite构建失败 - terser not found
**常见错误**: `[vite:terser] terser not found. Since Vite v3, terser has become an optional dependency.`
**原因**: Vite v3+ 将 terser 作为可选依赖，生产构建需要手动安装
**解决方案**:
```bash
# 安装 terser 依赖
npm install terser --save-dev

# 或者在构建命令中包含 terser 安装
npm install @rollup/rollup-linux-x64-gnu terser --save-dev --no-workspaces
```

#### 3. 后端启动失败
**检查项目**:
- 环境变量配置是否完整
- MongoDB连接字符串是否正确
- 端口配置是否正确

#### 3. 前后端通信失败
**检查项目**:
- CORS配置是否正确
- API URL配置是否正确
- 网络连接是否正常

### 调试工具

#### 查看日志
在Render Dashboard中查看实时日志：
1. 选择对应的服务
2. 点击 "Logs" 标签
3. 查看实时日志输出

#### 测试API连接
```bash
# 测试后端API
curl -X POST https://inventory-ai-backend.onrender.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'

# 测试前端页面
curl -I https://inventory-ai-frontend.onrender.com
```

## 🔧 部署后配置

### 1. 更新域名配置（可选）

如果您有自定义域名：
1. 在Render Dashboard中配置自定义域名
2. 更新DNS记录
3. 更新环境变量中的URL配置

### 2. 监控设置

- 启用Render的监控功能
- 设置告警通知
- 配置性能监控

### 3. 备份策略

- 确认MongoDB Atlas自动备份已启用
- 定期备份重要配置
- 制定灾难恢复计划

## 📈 性能优化

### 1. 服务扩展

根据需要升级Render服务计划：
- Starter: $7/月，适合开发和小型应用
- Standard: $25/月，适合生产环境
- Pro: $85/月，适合高流量应用

### 2. 缓存策略

- 前端静态资源缓存已配置
- 考虑添加Redis缓存（可选）
- 优化数据库查询

### 3. CDN配置

考虑使用CDN加速静态资源：
- Cloudflare
- AWS CloudFront
- 其他CDN服务

## 📞 获取帮助

### 官方支持
- [Render文档](https://render.com/docs)
- [Render社区](https://community.render.com)

### 项目支持
- 查看项目文档
- 检查故障排除指南
- 联系技术团队

---

**恭喜！** 您已成功在Render平台上部署了Chat-AI-3.0全栈应用。
