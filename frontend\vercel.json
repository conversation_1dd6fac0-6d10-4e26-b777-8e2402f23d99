{"version": 2, "name": "inventory-ai-frontend", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/assets/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "env": {"VITE_APP_VERSION": "1.0.0"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}, "regions": ["hkg1", "sin1"], "github": {"autoAlias": false}}