const express = require('express');
const chatController = require('../controllers/chatController');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

// @desc    创建聊天会话
// @route   POST /api/chat/sessions
// @access  Public
router.post('/sessions', chatController.createChatSession);

// @desc    发送聊天消息
// @route   POST /api/chat/sessions/:session_id/messages
// @access  Public
router.post('/sessions/:session_id/messages', chatController.sendMessage);

// @desc    获取聊天历史
// @route   GET /api/chat/sessions/:session_id/history
// @access  Public
router.get('/sessions/:session_id/history', chatController.getChatHistory);

// @desc    升级到人工客服
// @route   POST /api/chat/sessions/:session_id/escalate
// @access  Private (需要客服权限)
router.post('/sessions/:session_id/escalate', authenticateToken, chatController.escalateToAgent);

// 兼容旧版API
// @desc    AI聊天接口 (兼容性)
// @route   POST /api/chat
// @access  Public
router.post('/', chatController.sendMessage);

module.exports = router;
