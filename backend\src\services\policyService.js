const fs = require('fs').promises;
const path = require('path');
const { Policy } = require('../models');
const logger = require('../utils/logger');

/**
 * 政策管理服务类
 * 处理政策的增删改查、版本管理、导入导出等功能
 */
class PolicyService {
  constructor() {
    this.policyFilePath = path.join(process.cwd(), 'policy.json');
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 从policy.json文件导入政策
   * @param {string} userId - 操作用户ID
   * @returns {Promise<Object>} 导入结果
   */
  async importFromFile(userId) {
    try {
      logger.info('开始从policy.json导入政策');

      // 读取政策文件
      const fileContent = await fs.readFile(this.policyFilePath, 'utf8');
      const policyData = JSON.parse(fileContent);

      const results = {
        imported: 0,
        updated: 0,
        errors: []
      };

      // 处理每个政策分类
      for (const [category, items] of Object.entries(policyData.sections)) {
        try {
          await this.importPolicySection(category, items, userId, policyData.version);
          results.imported++;
        } catch (error) {
          logger.error(`导入政策分类 ${category} 失败`, error);
          results.errors.push({
            category,
            error: error.message
          });
        }
      }

      // 清除缓存
      this.clearCache();

      logger.info('政策导入完成', results);
      return {
        success: true,
        ...results
      };

    } catch (error) {
      logger.error('政策文件导入失败', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 导入单个政策分类
   * @param {string} category - 政策类别
   * @param {Array} items - 政策项目
   * @param {string} userId - 用户ID
   * @param {string} version - 版本号
   */
  async importPolicySection(category, items, userId, version) {
    // 查找现有政策
    let policy = await Policy.findOne({ category, name: this.getCategoryName(category) });

    const policyData = {
      name: this.getCategoryName(category),
      category,
      description: this.getCategoryDescription(category),
      current_version: version || '2.0.0',
      current_content: items,
      tags: this.getCategoryTags(category),
      keywords: this.extractKeywords(items),
      priority: this.getCategoryPriority(category),
      metadata: {
        source: 'import',
        import_file: 'policy.json'
      }
    };

    if (policy) {
      // 更新现有政策
      const hasChanges = JSON.stringify(policy.current_content) !== JSON.stringify(items);
      
      if (hasChanges) {
        policy.addVersion(items, userId, `从policy.json更新 - 版本${version}`);
        Object.assign(policy, policyData);
        await policy.save();
        logger.info(`政策已更新: ${category}`);
      } else {
        logger.info(`政策无变化: ${category}`);
      }
    } else {
      // 创建新政策
      policy = new Policy({
        ...policyData,
        created_by: userId
      });
      await policy.save();
      logger.info(`政策已创建: ${category}`);
    }

    return policy;
  }

  /**
   * 获取所有政策
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 政策列表
   */
  async getAllPolicies(options = {}) {
    try {
      const {
        category = null,
        activeOnly = true,
        includeVersions = false,
        limit = 50,
        page = 1
      } = options;

      const cacheKey = `policies_${JSON.stringify(options)}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const query = {};
      if (category) query.category = category;
      if (activeOnly) {
        query.is_active = true;
        query.$or = [
          { expiry_date: { $exists: false } },
          { expiry_date: null },
          { expiry_date: { $gt: new Date() } }
        ];
      }

      const skip = (page - 1) * limit;
      let queryBuilder = Policy.find(query)
        .sort({ priority: -1, updatedAt: -1 })
        .skip(skip)
        .limit(limit);

      if (!includeVersions) {
        queryBuilder = queryBuilder.select('-versions');
      }

      const policies = await queryBuilder.exec();
      const total = await Policy.countDocuments(query);

      const result = {
        policies,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

      this.setCache(cacheKey, result);
      return result;

    } catch (error) {
      logger.error('获取政策列表失败', error);
      throw error;
    }
  }

  /**
   * 根据ID获取政策
   * @param {string} policyId - 政策ID
   * @param {boolean} includeVersions - 是否包含版本历史
   * @returns {Promise<Object>} 政策详情
   */
  async getPolicyById(policyId, includeVersions = false) {
    try {
      const cacheKey = `policy_${policyId}_${includeVersions}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      let query = Policy.findOne({ policy_id: policyId });
      
      if (!includeVersions) {
        query = query.select('-versions');
      }

      const policy = await query.exec();
      
      if (policy) {
        this.setCache(cacheKey, policy);
      }

      return policy;

    } catch (error) {
      logger.error('获取政策详情失败', error);
      throw error;
    }
  }

  /**
   * 搜索政策
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async searchPolicies(keyword, options = {}) {
    try {
      const cacheKey = `search_${keyword}_${JSON.stringify(options)}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const policies = await Policy.search(keyword, options);
      
      this.setCache(cacheKey, policies);
      return policies;

    } catch (error) {
      logger.error('搜索政策失败', error);
      throw error;
    }
  }

  /**
   * 创建新政策
   * @param {Object} policyData - 政策数据
   * @param {string} userId - 创建者ID
   * @returns {Promise<Object>} 创建的政策
   */
  async createPolicy(policyData, userId) {
    try {
      const policy = new Policy({
        ...policyData,
        created_by: userId
      });

      await policy.save();
      this.clearCache();

      logger.info('政策创建成功', { policy_id: policy.policy_id });
      return policy;

    } catch (error) {
      logger.error('创建政策失败', error);
      throw error;
    }
  }

  /**
   * 更新政策
   * @param {string} policyId - 政策ID
   * @param {Object} updateData - 更新数据
   * @param {string} userId - 更新者ID
   * @param {string} changeSummary - 变更摘要
   * @returns {Promise<Object>} 更新的政策
   */
  async updatePolicy(policyId, updateData, userId, changeSummary = '') {
    try {
      const policy = await Policy.findOne({ policy_id: policyId });
      
      if (!policy) {
        throw new Error('政策不存在');
      }

      // 如果内容有变化，创建新版本
      if (updateData.current_content && 
          JSON.stringify(policy.current_content) !== JSON.stringify(updateData.current_content)) {
        policy.addVersion(updateData.current_content, userId, changeSummary);
      }

      // 更新其他字段
      Object.assign(policy, updateData, { updated_by: userId });
      await policy.save();

      this.clearCache();

      logger.info('政策更新成功', { policy_id: policyId });
      return policy;

    } catch (error) {
      logger.error('更新政策失败', error);
      throw error;
    }
  }

  /**
   * 删除政策
   * @param {string} policyId - 政策ID
   * @returns {Promise<boolean>} 删除结果
   */
  async deletePolicy(policyId) {
    try {
      const result = await Policy.deleteOne({ policy_id: policyId });
      
      if (result.deletedCount > 0) {
        this.clearCache();
        logger.info('政策删除成功', { policy_id: policyId });
        return true;
      }

      return false;

    } catch (error) {
      logger.error('删除政策失败', error);
      throw error;
    }
  }

  /**
   * 增加政策使用计数
   * @param {string} policyId - 政策ID
   */
  async incrementUsage(policyId) {
    try {
      await Policy.incrementUsage(policyId);
    } catch (error) {
      logger.error('增加使用计数失败', error);
    }
  }

  /**
   * 获取政策统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStatistics() {
    try {
      const stats = await Policy.aggregate([
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            active_count: {
              $sum: { $cond: ['$is_active', 1, 0] }
            },
            total_usage: { $sum: '$usage_count' }
          }
        }
      ]);

      const total = await Policy.countDocuments();
      const active = await Policy.countDocuments({ is_active: true });
      const needingReview = await Policy.countDocuments({
        is_active: true,
        $or: [
          { last_reviewed_at: { $exists: false } },
          { last_reviewed_at: null },
          { 
            last_reviewed_at: { 
              $lt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) 
            } 
          }
        ]
      });

      return {
        total,
        active,
        needingReview,
        byCategory: stats
      };

    } catch (error) {
      logger.error('获取政策统计失败', error);
      throw error;
    }
  }

  // 辅助方法
  getCategoryName(category) {
    const names = {
      mission: '使命理念',
      group_rules: '群规管理',
      product_quality: '产品质量',
      delivery: '配送服务',
      payment: '付款方式',
      pickup: '取货点',
      after_sale: '售后服务',
      community: '社区文化'
    };
    return names[category] || category;
  }

  getCategoryDescription(category) {
    const descriptions = {
      mission: '平台的使命、理念和核心价值观',
      group_rules: '社区群组的行为规范和管理规则',
      product_quality: '产品质量保证和相关政策',
      delivery: '配送服务的时间、范围和费用政策',
      payment: '付款方式和流程规范',
      pickup: '取货点信息和取货规则',
      after_sale: '售后服务和质量问题处理流程',
      community: '社区文化和互助精神'
    };
    return descriptions[category] || '';
  }

  getCategoryTags(category) {
    const tags = {
      mission: ['使命', '理念', '价值观'],
      group_rules: ['群规', '规范', '管理'],
      product_quality: ['质量', '保证', '退换'],
      delivery: ['配送', '运费', '时间'],
      payment: ['付款', 'venmo', '备注'],
      pickup: ['取货', '地点', '时间'],
      after_sale: ['售后', '质量', '反馈'],
      community: ['社区', '互助', '文化']
    };
    return tags[category] || [];
  }

  getCategoryPriority(category) {
    const priorities = {
      mission: 10,
      group_rules: 9,
      product_quality: 8,
      delivery: 7,
      payment: 7,
      pickup: 6,
      after_sale: 8,
      community: 5
    };
    return priorities[category] || 5;
  }

  extractKeywords(items) {
    if (!Array.isArray(items)) return [];
    
    const keywords = [];
    items.forEach(item => {
      if (typeof item === 'string') {
        // 提取关键词（简单实现）
        const words = item.match(/[\u4e00-\u9fa5]+/g) || [];
        keywords.push(...words.filter(word => word.length >= 2));
      }
    });
    
    return [...new Set(keywords)].slice(0, 20); // 去重并限制数量
  }

  // 缓存管理
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearCache() {
    this.cache.clear();
  }
}

module.exports = new PolicyService();
