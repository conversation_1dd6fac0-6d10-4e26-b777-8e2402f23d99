import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production'

  return {
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@services': path.resolve(__dirname, './src/services'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@styles': path.resolve(__dirname, './src/styles')
    }
  },
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        secure: false
      },
      '/socket.io': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        ws: true
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: !isProduction,
    minify: isProduction ? 'terser' : false,
    target: 'es2015',
    cssCodeSplit: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd', '@ant-design/icons'],
          utils: ['axios', 'dayjs', 'socket.io-client'],
          charts: ['recharts'],
          forms: ['react-hook-form', '@hookform/resolvers', 'yup']
        },
        chunkFileNames: isProduction ? 'assets/js/[name]-[hash].js' : 'assets/js/[name].js',
        entryFileNames: isProduction ? 'assets/js/[name]-[hash].js' : 'assets/js/[name].js',
        assetFileNames: isProduction ? 'assets/[ext]/[name]-[hash].[ext]' : 'assets/[ext]/[name].[ext]'
      }
    },
    terserOptions: isProduction ? {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    } : {}
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          // Ant Design 主题定制
          '@primary-color': '#2C7AFF',
          '@success-color': '#22C55E',
          '@error-color': '#EF4444',
          '@warning-color': '#FFAA2C',
          '@border-radius-base': '4px',
          '@border-radius-sm': '2px',
          '@font-family': 'Inter, "思源黑体", sans-serif'
        },
        javascriptEnabled: true
      }
    }
  }
  }
})
