/**
 * Products组件单元测试
 * 测试产品列表渲染、CRUD操作、搜索过滤、表单验证等功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Products from '../pages/Products';
import { productsService } from '../services/products';

// 模拟产品服务
jest.mock('../services/products', () => ({
  productsService: {
    getProducts: jest.fn(),
    getCategories: jest.fn(),
    getProductStats: jest.fn(),
    createProduct: jest.fn(),
    updateProduct: jest.fn(),
    deleteProduct: jest.fn(),
  },
}));

// 模拟Ant Design的message组件
jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    message: {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
      info: jest.fn(),
    },
  };
});

// 测试数据
const mockProducts = [
  {
    id: '1',
    name: '苹果',
    category: 'produce',
    barcode: 'P123456789',
    unit_price: 5.99,
    supplier: '水果供应商',
    is_active: true,
    createdAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: '2',
    name: '香蕉',
    category: 'produce',
    barcode: 'P987654321',
    unit_price: 3.99,
    supplier: '水果供应商',
    is_active: false,
    createdAt: '2024-01-02T00:00:00.000Z',
  },
];

const mockCategories = [
  '生鲜',
  '乳制品',
  '冷冻食品',
];

const mockStats = {
  total: 100,
  active: 85,
  inactive: 15,
};

// 测试组件包装器
const renderProducts = () => {
  return render(
    <ConfigProvider locale={zhCN}>
      <Products />
    </ConfigProvider>
  );
};

describe('Products组件测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // 设置默认的API响应
    productsService.getProducts.mockResolvedValue({
      success: true,
      data: {
        products: mockProducts,
        total: mockProducts.length,
      },
    });
    
    productsService.getCategories.mockResolvedValue({
      success: true,
      data: mockCategories,
    });
    
    productsService.getProductStats.mockResolvedValue({
      success: true,
      data: mockStats,
    });
  });

  describe('组件渲染测试', () => {
    test('应该正确渲染页面标题和描述', async () => {
      renderProducts();

      expect(screen.getByText('商品管理')).toBeInTheDocument();
      expect(screen.getByText('管理商品信息、条码录入和商品分类')).toBeInTheDocument();
    });

    test('应该正确渲染统计卡片', async () => {
      renderProducts();

      await waitFor(() => {
        expect(screen.getByText('商品总数')).toBeInTheDocument();
        expect(screen.getByText('启用商品')).toBeInTheDocument();
        expect(screen.getByText('禁用商品')).toBeInTheDocument();
        expect(screen.getByText('商品分类')).toBeInTheDocument();
      });
    });

    test('应该正确渲染操作按钮', async () => {
      renderProducts();

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /新增商品/ })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /扫码录入/ })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /刷新/ })).toBeInTheDocument();
      });
    });

    test('应该正确渲染搜索和筛选控件', async () => {
      renderProducts();

      await waitFor(() => {
        expect(screen.getByPlaceholderText('搜索商品名称、条码')).toBeInTheDocument();
        // 使用更可靠的方式查找Select组件
        const selects = screen.getAllByRole('combobox');
        expect(selects.length).toBeGreaterThanOrEqual(2); // 至少有分类和状态两个选择器
      });
    });
  });

  describe('数据加载测试', () => {
    test('应该在组件挂载时加载数据', async () => {
      renderProducts();

      await waitFor(() => {
        expect(productsService.getProducts).toHaveBeenCalledWith({
          page: 1,
          limit: 10,
          search: '',
          category: '',
          status: '',
        });
        expect(productsService.getCategories).toHaveBeenCalled();
        expect(productsService.getProductStats).toHaveBeenCalled();
      });
    });

    test('应该正确显示产品列表', async () => {
      renderProducts();

      await waitFor(() => {
        expect(screen.getByText('苹果')).toBeInTheDocument();
        expect(screen.getByText('香蕉')).toBeInTheDocument();
        expect(screen.getByText('P123456789')).toBeInTheDocument();
        expect(screen.getByText('P987654321')).toBeInTheDocument();
      });
    });

    test('应该处理数据加载失败的情况', async () => {
      productsService.getProducts.mockRejectedValue(new Error('网络错误'));
      
      renderProducts();

      await waitFor(() => {
        expect(productsService.getProducts).toHaveBeenCalled();
      });
    });
  });

  describe('搜索和筛选测试', () => {
    test('应该支持商品名称搜索', async () => {
      const user = userEvent.setup();
      renderProducts();

      await waitFor(() => {
        expect(screen.getByPlaceholderText('搜索商品名称、条码')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('搜索商品名称、条码');
      await user.type(searchInput, '苹果');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(productsService.getProducts).toHaveBeenCalledWith({
          page: 1,
          limit: 10,
          search: '苹果',
          category: '',
          status: '',
        });
      });
    });

    test('应该支持分类筛选', async () => {
      const user = userEvent.setup();
      renderProducts();

      await waitFor(() => {
        const selects = screen.getAllByRole('combobox');
        expect(selects.length).toBeGreaterThanOrEqual(2);
      });

      // 点击第一个分类选择器（通常是分类筛选）
      const selects = screen.getAllByRole('combobox');
      const categorySelect = selects[0]; // 假设第一个是分类选择器
      await user.click(categorySelect);

      // 等待选项出现并选择
      await waitFor(() => {
        const produceOptions = screen.getAllByText('生鲜');
        expect(produceOptions.length).toBeGreaterThan(0);
      });

      // 选择下拉选项中的"生鲜"（通常是最后一个）
      const produceOptions = screen.getAllByText('生鲜');
      const produceOption = produceOptions[produceOptions.length - 1]; // 选择最后一个（下拉选项）
      await user.click(produceOption);

      await waitFor(() => {
        expect(productsService.getProducts).toHaveBeenCalledWith({
          page: 1,
          limit: 10,
          search: '',
          category: '生鲜',
          status: '',
        });
      });
    });

    test('应该支持状态筛选', async () => {
      const user = userEvent.setup();
      renderProducts();

      await waitFor(() => {
        const selects = screen.getAllByRole('combobox');
        expect(selects.length).toBeGreaterThanOrEqual(2);
      });

      // 点击第二个状态选择器（通常是状态筛选）
      const selects = screen.getAllByRole('combobox');
      const statusSelect = selects[1]; // 假设第二个是状态选择器
      await user.click(statusSelect);

      // 等待选项出现并选择
      await waitFor(() => {
        const activeOption = screen.getByText('启用');
        expect(activeOption).toBeInTheDocument();
      });

      const activeOption = screen.getByText('启用');
      await user.click(activeOption);

      await waitFor(() => {
        expect(productsService.getProducts).toHaveBeenCalledWith({
          page: 1,
          limit: 10,
          search: '',
          category: '',
          status: 'active',
        });
      });
    });
  });

  describe('CRUD操作测试', () => {
    test('应该能够打开新增商品模态框', async () => {
      const user = userEvent.setup();
      renderProducts();

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /新增商品/ })).toBeInTheDocument();
      });

      const addButton = screen.getByRole('button', { name: /新增商品/ });
      await user.click(addButton);

      await waitFor(() => {
        expect(screen.getAllByText('新增商品')).toHaveLength(2); // 按钮和模态框标题都有
      });
    });

    test('应该能够刷新产品列表', async () => {
      const user = userEvent.setup();
      renderProducts();

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /刷新/ })).toBeInTheDocument();
      });

      // 清除之前的调用记录
      jest.clearAllMocks();

      const refreshButton = screen.getByRole('button', { name: /刷新/ });
      await user.click(refreshButton);

      await waitFor(() => {
        expect(productsService.getProducts).toHaveBeenCalled();
      });
    });
  });

  describe('表单验证测试', () => {
    test('应该显示新增商品表单', async () => {
      const user = userEvent.setup();
      renderProducts();

      // 打开新增模态框
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /新增商品/ })).toBeInTheDocument();
      });

      const addButton = screen.getByRole('button', { name: /新增商品/ });
      await user.click(addButton);

      // 验证表单元素存在
      await waitFor(() => {
        expect(screen.getAllByText('新增商品')).toHaveLength(2); // 按钮和模态框标题都有
      });
    });
  });

  describe('用户交互测试', () => {
    test('应该处理扫码录入功能', async () => {
      const user = userEvent.setup();
      renderProducts();

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /扫码录入/ })).toBeInTheDocument();
      });

      const scanButton = screen.getByRole('button', { name: /扫码录入/ });
      await user.click(scanButton);

      // 验证按钮可点击
      expect(scanButton).toBeInTheDocument();
    });
  });

  describe('错误处理测试', () => {
    test('应该处理数据加载失败', async () => {
      productsService.getProducts.mockRejectedValue(new Error('网络错误'));

      renderProducts();

      await waitFor(() => {
        expect(productsService.getProducts).toHaveBeenCalled();
      });
    });
  });

  describe('边界情况测试', () => {
    test('应该处理空数据列表', async () => {
      productsService.getProducts.mockResolvedValue({
        success: true,
        data: {
          products: [],
          total: 0,
        },
      });

      renderProducts();

      await waitFor(() => {
        expect(productsService.getProducts).toHaveBeenCalled();
      });
    });
  });
});
