const mongoose = require('mongoose');

const reportDataSchema = new mongoose.Schema({
  section: {
    type: String,
    required: [true, '报表章节是必需的'],
    enum: ['summary', 'inventory', 'stock_movement', 'returns', 'alerts', 'performance']
  },
  title: {
    type: String,
    required: [true, '章节标题是必需的'],
    trim: true
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, '报表数据是必需的']
  },
  charts: [{
    chart_type: {
      type: String,
      enum: ['bar', 'line', 'pie', 'table', 'metric']
    },
    title: String,
    data: mongoose.Schema.Types.Mixed,
    config: mongoose.Schema.Types.Mixed
  }],
  order: {
    type: Number,
    default: 0
  }
}, { _id: false });

const reportSchema = new mongoose.Schema({
  report_id: {
    type: String,
    unique: true,
    index: true
  },
  report_type: {
    type: String,
    required: [true, '报表类型是必需的'],
    enum: ['weekly', 'monthly', 'quarterly', 'annual', 'custom', 'daily'],
    index: true
  },
  title: {
    type: String,
    required: [true, '报表标题是必需的'],
    trim: true,
    maxlength: [200, '报表标题不能超过200个字符']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, '报表描述不能超过500个字符']
  },
  period: {
    start_date: {
      type: Date,
      required: [true, '开始日期是必需的'],
      index: true
    },
    end_date: {
      type: Date,
      required: [true, '结束日期是必需的'],
      index: true
    },
    year: {
      type: Number,
      index: true
    },
    month: {
      type: Number,
      min: 1,
      max: 12,
      index: true
    },
    week: {
      type: Number,
      min: 1,
      max: 53,
      index: true
    }
  },
  status: {
    type: String,
    enum: ['generating', 'completed', 'failed', 'archived'],
    default: 'generating',
    index: true
  },
  data_sections: {
    type: [reportDataSchema],
    default: []
  },
  summary: {
    total_products: {
      type: Number,
      default: 0
    },
    total_stock_value: {
      type: Number,
      default: 0
    },
    stock_in_count: {
      type: Number,
      default: 0
    },
    stock_out_count: {
      type: Number,
      default: 0
    },
    return_requests: {
      type: Number,
      default: 0
    },
    low_stock_alerts: {
      type: Number,
      default: 0
    },
    expiry_alerts: {
      type: Number,
      default: 0
    },
    chat_sessions: {
      type: Number,
      default: 0
    },
    avg_resolution_time: {
      type: Number,
      default: 0
    },
    customer_satisfaction: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    }
  },
  filters: {
    categories: [String],
    locations: [String],
    suppliers: [String],
    date_range: {
      start: Date,
      end: Date
    }
  },
  generated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '生成人是必需的']
  },
  generated_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  generation_time_ms: {
    type: Number,
    min: 0
  },
  file_exports: [{
    format: {
      type: String,
      enum: ['pdf', 'excel', 'csv', 'json'],
      required: true
    },
    filename: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    file_size: {
      type: Number,
      min: 0
    },
    generated_at: {
      type: Date,
      default: Date.now
    }
  }],
  recipients: [{
    user_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    sent_at: {
      type: Date
    },
    delivery_status: {
      type: String,
      enum: ['pending', 'sent', 'delivered', 'failed'],
      default: 'pending'
    }
  }],
  schedule: {
    is_scheduled: {
      type: Boolean,
      default: false
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly']
    },
    day_of_week: {
      type: Number,
      min: 0,
      max: 6 // 0=Sunday, 6=Saturday
    },
    day_of_month: {
      type: Number,
      min: 1,
      max: 31
    },
    time: {
      type: String,
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    next_run: {
      type: Date,
      index: true
    }
  },
  metadata: {
    version: {
      type: String,
      default: '1.0'
    },
    template_id: {
      type: String
    },
    data_sources: [String],
    processing_notes: {
      type: String,
      trim: true
    }
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, '标签不能超过30个字符']
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 生成报表ID
reportSchema.statics.generateReportId = function(reportType) {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const timestamp = now.getTime().toString().slice(-6);
  
  const typePrefix = {
    'weekly': 'WR',
    'monthly': 'MR',
    'quarterly': 'QR',
    'annual': 'AR',
    'daily': 'DR',
    'custom': 'CR'
  };
  
  const prefix = typePrefix[reportType] || 'RP';
  return `${prefix}${year}${month}${day}${timestamp}`;
};

// 添加数据章节
reportSchema.methods.addDataSection = function(section, title, data, charts = []) {
  const existingIndex = this.data_sections.findIndex(s => s.section === section);
  
  const sectionData = {
    section: section,
    title: title,
    data: data,
    charts: charts,
    order: this.data_sections.length
  };
  
  if (existingIndex >= 0) {
    this.data_sections[existingIndex] = sectionData;
  } else {
    this.data_sections.push(sectionData);
  }
  
  return this;
};

// 更新摘要数据
reportSchema.methods.updateSummary = function(summaryData) {
  Object.keys(summaryData).forEach(key => {
    if (this.summary.hasOwnProperty(key)) {
      this.summary[key] = summaryData[key];
    }
  });
  
  return this;
};

// 标记为完成
reportSchema.methods.markCompleted = function(generationTimeMs) {
  this.status = 'completed';
  this.generation_time_ms = generationTimeMs;
  
  return this;
};

// 标记为失败
reportSchema.methods.markFailed = function(error) {
  this.status = 'failed';
  this.metadata.processing_notes = error.message || '报表生成失败';
  
  return this;
};

// 添加文件导出
reportSchema.methods.addFileExport = function(format, filename, url, fileSize) {
  this.file_exports.push({
    format: format,
    filename: filename,
    url: url,
    file_size: fileSize,
    generated_at: new Date()
  });
  
  return this;
};

// 添加收件人
reportSchema.methods.addRecipient = function(userIdOrEmail, isUserId = true) {
  const recipient = {
    delivery_status: 'pending'
  };
  
  if (isUserId) {
    recipient.user_id = userIdOrEmail;
  } else {
    recipient.email = userIdOrEmail;
  }
  
  this.recipients.push(recipient);
  
  return this;
};

// 更新发送状态
reportSchema.methods.updateDeliveryStatus = function(recipientIndex, status, sentAt = null) {
  if (this.recipients[recipientIndex]) {
    this.recipients[recipientIndex].delivery_status = status;
    if (sentAt) {
      this.recipients[recipientIndex].sent_at = sentAt;
    }
  }
  
  return this;
};

// 设置定时任务
reportSchema.methods.setSchedule = function(frequency, options = {}) {
  this.schedule.is_scheduled = true;
  this.schedule.frequency = frequency;
  
  if (options.dayOfWeek !== undefined) {
    this.schedule.day_of_week = options.dayOfWeek;
  }
  
  if (options.dayOfMonth !== undefined) {
    this.schedule.day_of_month = options.dayOfMonth;
  }
  
  if (options.time) {
    this.schedule.time = options.time;
  }
  
  // 计算下次运行时间
  this.calculateNextRun();
  
  return this;
};

// 计算下次运行时间
reportSchema.methods.calculateNextRun = function() {
  if (!this.schedule.is_scheduled) return;
  
  const now = new Date();
  let nextRun = new Date(now);
  
  switch (this.schedule.frequency) {
    case 'daily':
      nextRun.setDate(nextRun.getDate() + 1);
      break;
    case 'weekly':
      const daysUntilTarget = (this.schedule.day_of_week - now.getDay() + 7) % 7;
      nextRun.setDate(nextRun.getDate() + (daysUntilTarget || 7));
      break;
    case 'monthly':
      nextRun.setMonth(nextRun.getMonth() + 1);
      if (this.schedule.day_of_month) {
        nextRun.setDate(this.schedule.day_of_month);
      }
      break;
    case 'quarterly':
      nextRun.setMonth(nextRun.getMonth() + 3);
      break;
  }
  
  // 设置时间
  if (this.schedule.time) {
    const [hours, minutes] = this.schedule.time.split(':');
    nextRun.setHours(parseInt(hours), parseInt(minutes), 0, 0);
  }
  
  this.schedule.next_run = nextRun;
  
  return this;
};

// 复合索引
reportSchema.index({ report_type: 1, 'period.start_date': -1 });
reportSchema.index({ status: 1, generated_at: -1 });
reportSchema.index({ generated_by: 1, generated_at: -1 });
reportSchema.index({ 'period.year': 1, 'period.month': 1 });
reportSchema.index({ 'schedule.is_scheduled': 1, 'schedule.next_run': 1 });

// 中间件：保存前生成报表ID和计算周期信息
reportSchema.pre('save', function(next) {
  if (this.isNew && !this.report_id) {
    this.report_id = this.constructor.generateReportId(this.report_type);
  }

  // 计算年月周信息
  if (this.period && this.period.start_date) {
    this.period.year = this.period.start_date.getFullYear();
    this.period.month = this.period.start_date.getMonth() + 1;

    // 计算周数
    const startOfYear = new Date(this.period.year, 0, 1);
    const weekNumber = Math.ceil(((this.period.start_date - startOfYear) / 86400000 + startOfYear.getDay() + 1) / 7);
    this.period.week = weekNumber;
  }

  next();
});

module.exports = mongoose.model('Report', reportSchema);
