const { ChatSession, User } = require('../models');
const aiService = require('./aiService');
const ragService = require('./ragService');
const policyService = require('./policyService');
const logger = require('../utils/logger');

/**
 * 聊天服务类
 * 处理实时聊天功能，集成AI服务和RAG检索
 */
class ChatService {
  constructor() {
    this.activeSessions = new Map(); // 活跃会话缓存
    this.userSockets = new Map(); // 用户Socket映射
    this.messageQueue = new Map(); // 离线消息队列
  }

  /**
   * 用户连接处理
   * @param {Object} socket - Socket.io socket对象
   * @param {string} userId - 用户ID
   * @param {string} sessionId - 会话ID
   */
  async handleUserConnect(socket, userId, sessionId) {
    try {
      logger.info('用户连接聊天服务', { userId, sessionId, socketId: socket.id });

      // 记录用户Socket映射
      this.userSockets.set(userId, {
        socket,
        sessionId,
        connectedAt: new Date(),
        lastActivity: new Date()
      });

      // 加入会话房间
      socket.join(`session-${sessionId}`);
      socket.join(`user-${userId}`);

      // 获取或创建聊天会话
      let chatSession = await this.getOrCreateSession(sessionId, userId);
      
      // 缓存活跃会话
      this.activeSessions.set(sessionId, {
        session: chatSession,
        lastActivity: new Date(),
        participants: [userId]
      });

      // 发送连接成功消息
      socket.emit('chat-connected', {
        sessionId,
        status: 'connected',
        session: {
          session_id: chatSession.session_id,
          status: chatSession.status,
          context: chatSession.context
        }
      });

      // 发送离线消息（如果有）
      await this.sendOfflineMessages(socket, userId, sessionId);

      // 更新会话状态为活跃
      await this.updateSessionStatus(sessionId, 'active');

      return chatSession;

    } catch (error) {
      logger.error('用户连接处理失败', error);
      socket.emit('chat-error', {
        type: 'connection_error',
        message: '连接聊天服务失败'
      });
      throw error;
    }
  }

  /**
   * 处理聊天消息
   * @param {Object} socket - Socket.io socket对象
   * @param {Object} messageData - 消息数据
   */
  async handleChatMessage(socket, messageData) {
    const startTime = Date.now();
    
    try {
      const {
        sessionId,
        message,
        messageType = 'text',
        userId
      } = messageData;

      logger.info('处理聊天消息', {
        sessionId,
        userId,
        messageType,
        messageLength: message?.length || 0
      });

      // 验证消息数据
      if (!sessionId || !message || !userId) {
        socket.emit('chat-error', {
          type: 'invalid_message',
          message: '消息数据不完整'
        });
        return;
      }

      // 获取聊天会话
      const chatSession = await this.getSessionFromCache(sessionId);
      if (!chatSession) {
        socket.emit('chat-error', {
          type: 'session_not_found',
          message: '聊天会话不存在'
        });
        return;
      }

      // 更新用户活动时间
      this.updateUserActivity(userId);

      // 保存用户消息
      const userMessage = await this.saveMessage(chatSession.session, {
        sender: 'user',
        content: message,
        message_type: messageType,
        metadata: {
          socket_id: socket.id,
          ip_address: socket.handshake.address
        }
      });

      // 广播用户消息到会话房间
      socket.to(`session-${sessionId}`).emit('user-message', {
        messageId: userMessage._id,
        sessionId,
        sender: 'user',
        content: message,
        messageType,
        timestamp: userMessage.timestamp,
        userId
      });

      // 显示AI正在输入状态
      socket.to(`session-${sessionId}`).emit('ai-typing', {
        sessionId,
        isTyping: true
      });

      // 处理AI响应
      const aiResponse = await this.processAIResponse(chatSession.session, message, messageType);

      // 保存AI响应
      const aiMessage = await this.saveMessage(chatSession.session, {
        sender: 'ai',
        content: aiResponse.content,
        message_type: 'text',
        metadata: {
          intent: aiResponse.intent,
          confidence: aiResponse.confidence,
          entities: aiResponse.entities,
          action_taken: aiResponse.action_taken,
          referenced_policies: aiResponse.referenced_policies,
          ai_usage: aiResponse.ai_usage,
          retrieval_score: aiResponse.retrieval_score
        }
      });

      // 停止AI输入状态
      socket.to(`session-${sessionId}`).emit('ai-typing', {
        sessionId,
        isTyping: false
      });

      // 发送AI响应
      const responseData = {
        messageId: aiMessage._id,
        sessionId,
        sender: 'ai',
        content: aiResponse.content,
        messageType: 'text',
        timestamp: aiMessage.timestamp,
        metadata: {
          intent: aiResponse.intent,
          confidence: aiResponse.confidence,
          referencedPolicies: aiResponse.referenced_policies
        }
      };

      // 发送给当前用户
      socket.emit('ai-response', responseData);
      
      // 广播给会话中的其他用户
      socket.to(`session-${sessionId}`).emit('ai-response', responseData);

      // 更新会话上下文
      await this.updateSessionContext(chatSession.session, {
        last_intent: aiResponse.intent,
        last_confidence: aiResponse.confidence,
        message_count: chatSession.session.messages.length + 2,
        last_activity: new Date()
      });

      const responseTime = Date.now() - startTime;
      logger.info('聊天消息处理完成', {
        sessionId,
        responseTime,
        intent: aiResponse.intent,
        confidence: aiResponse.confidence
      });

    } catch (error) {
      logger.error('聊天消息处理失败', error);
      
      socket.emit('chat-error', {
        type: 'message_processing_error',
        message: '消息处理失败，请稍后再试'
      });

      // 停止AI输入状态
      socket.to(`session-${messageData.sessionId}`).emit('ai-typing', {
        sessionId: messageData.sessionId,
        isTyping: false
      });
    }
  }

  /**
   * 处理用户断开连接
   * @param {Object} socket - Socket.io socket对象
   * @param {string} userId - 用户ID
   */
  async handleUserDisconnect(socket, userId) {
    try {
      logger.info('用户断开连接', { userId, socketId: socket.id });

      const userConnection = this.userSockets.get(userId);
      if (userConnection) {
        const { sessionId } = userConnection;
        
        // 更新会话状态
        await this.updateSessionStatus(sessionId, 'inactive');
        
        // 从缓存中移除
        this.userSockets.delete(userId);
        
        // 通知会话中的其他用户
        socket.to(`session-${sessionId}`).emit('user-disconnected', {
          userId,
          sessionId,
          timestamp: new Date()
        });
      }

    } catch (error) {
      logger.error('用户断开连接处理失败', error);
    }
  }

  /**
   * 获取或创建聊天会话
   */
  async getOrCreateSession(sessionId, userId) {
    try {
      let chatSession = await ChatSession.findOne({ session_id: sessionId });
      
      if (!chatSession) {
        chatSession = new ChatSession({
          session_id: sessionId,
          user_id: userId,
          status: 'active',
          context: {
            created_at: new Date(),
            user_agent: 'WebSocket',
            platform: 'web'
          }
        });
        await chatSession.save();
        logger.info('创建新聊天会话', { sessionId, userId });
      }

      return chatSession;

    } catch (error) {
      logger.error('获取或创建聊天会话失败', error);
      throw error;
    }
  }

  /**
   * 从缓存获取会话
   */
  async getSessionFromCache(sessionId) {
    let cachedSession = this.activeSessions.get(sessionId);
    
    if (!cachedSession) {
      const session = await ChatSession.findOne({ session_id: sessionId });
      if (session) {
        cachedSession = {
          session,
          lastActivity: new Date(),
          participants: [session.user_id]
        };
        this.activeSessions.set(sessionId, cachedSession);
      }
    }

    return cachedSession;
  }

  /**
   * 保存消息
   */
  async saveMessage(chatSession, messageData) {
    try {
      const message = {
        ...messageData,
        timestamp: new Date()
      };

      chatSession.messages.push(message);
      await chatSession.save();

      return chatSession.messages[chatSession.messages.length - 1];

    } catch (error) {
      logger.error('保存消息失败', error);
      throw error;
    }
  }

  /**
   * 处理AI响应（复用chatController的逻辑）
   */
  async processAIResponse(chatSession, userMessage, messageType) {
    try {
      // 1. 意图分析
      const intentResult = await aiService.analyzeIntent(userMessage, {
        session_context: chatSession.context
      });

      // 2. RAG检索相关信息
      const retrievalResults = await ragService.retrieve(userMessage, {
        includePolicy: true,
        includeProduct: intentResult.intent === 'inventory_query' || intentResult.intent === 'product_info',
        includeInventory: intentResult.intent === 'inventory_query',
        limit: 3
      });

      // 3. 生成上下文信息
      const context = ragService.generateContext(retrievalResults, userMessage);

      // 4. 构建对话历史
      const conversationHistory = this.buildConversationHistory(chatSession, 5);

      // 5. 构建系统提示词
      const systemPrompt = this.buildSystemPrompt(intentResult.intent, context);

      // 6. 调用AI服务生成响应
      const aiResponse = await aiService.chat(
        [
          ...conversationHistory,
          { role: 'user', content: userMessage }
        ],
        {
          system_prompt: systemPrompt,
          temperature: 0.7,
          max_tokens: 1000
        }
      );

      if (!aiResponse.success) {
        return this.getFallbackResponse(intentResult.intent);
      }

      // 7. 增加相关政策的使用计数
      if (retrievalResults.policies.length > 0) {
        for (const policy of retrievalResults.policies) {
          await policyService.incrementUsage(policy.policy_id);
        }
      }

      return {
        content: aiResponse.content,
        intent: intentResult.intent,
        confidence: intentResult.confidence,
        entities: intentResult.entities || [],
        action_taken: 'ai_response',
        referenced_policies: retrievalResults.policies.map(p => ({
          policy_id: p.policy_id,
          name: p.name,
          category: p.category
        })),
        ai_usage: aiResponse.usage,
        retrieval_score: retrievalResults.relevanceScore
      };

    } catch (error) {
      logger.error('处理AI响应失败', error);
      return this.getFallbackResponse('general_question');
    }
  }

  /**
   * 构建对话历史
   */
  buildConversationHistory(chatSession, limit = 5) {
    const recentMessages = chatSession.getRecentMessages(limit * 2);
    const history = [];

    for (const msg of recentMessages) {
      if (msg.sender === 'user') {
        history.push({ role: 'user', content: msg.content });
      } else if (msg.sender === 'ai') {
        history.push({ role: 'assistant', content: msg.content });
      }
    }

    return history.slice(-limit * 2);
  }

  /**
   * 构建系统提示词
   */
  buildSystemPrompt(intent, context) {
    return `你是一个专业的客服AI助手，为社区拼台（团购平台）提供服务。

你的职责包括：
1. 回答关于产品、配送、付款、取货等问题
2. 协助处理退货和售后问题
3. 解释平台政策和规则
4. 查询库存信息
5. 提供友好、专业的客户服务

回答要求：
- 使用简体中文
- 语气友好、专业
- 回答准确、简洁
- 优先引用相关政策条款来支持回答
- 如果不确定，请说明并建议联系人工客服

当前用户意图：${intent}

相关信息：
${context}

请基于以上信息回答用户问题。如果有相关政策，请在回答中引用具体的政策条款。`;
  }

  /**
   * 获取备用响应
   */
  getFallbackResponse(intent) {
    const fallbackResponses = {
      'inventory_query': {
        content: '抱歉，我暂时无法查询库存信息。请稍后再试或联系人工客服。',
        intent: 'inventory_query',
        confidence: 0.1,
        entities: [],
        action_taken: 'fallback_response'
      },
      'return_request': {
        content: '抱歉，我暂时无法处理退货申请。请联系人工客服获得帮助。',
        intent: 'return_request',
        confidence: 0.1,
        entities: [],
        action_taken: 'fallback_response'
      },
      'general_question': {
        content: '抱歉，我暂时无法理解您的问题。请稍后再试或联系人工客服。',
        intent: 'general_question',
        confidence: 0.1,
        entities: [],
        action_taken: 'fallback_response'
      }
    };

    return fallbackResponses[intent] || fallbackResponses['general_question'];
  }

  /**
   * 发送离线消息
   */
  async sendOfflineMessages(socket, userId, sessionId) {
    try {
      const offlineMessages = this.messageQueue.get(userId) || [];
      
      for (const message of offlineMessages) {
        socket.emit('offline-message', message);
      }

      // 清除离线消息
      this.messageQueue.delete(userId);

      if (offlineMessages.length > 0) {
        logger.info(`发送 ${offlineMessages.length} 条离线消息`, { userId, sessionId });
      }

    } catch (error) {
      logger.error('发送离线消息失败', error);
    }
  }

  /**
   * 更新用户活动时间
   */
  updateUserActivity(userId) {
    const userConnection = this.userSockets.get(userId);
    if (userConnection) {
      userConnection.lastActivity = new Date();
    }
  }

  /**
   * 更新会话状态
   */
  async updateSessionStatus(sessionId, status) {
    try {
      await ChatSession.updateOne(
        { session_id: sessionId },
        { 
          status,
          'context.last_activity': new Date()
        }
      );

      // 更新缓存
      const cachedSession = this.activeSessions.get(sessionId);
      if (cachedSession) {
        cachedSession.lastActivity = new Date();
      }

    } catch (error) {
      logger.error('更新会话状态失败', error);
    }
  }

  /**
   * 更新会话上下文
   */
  async updateSessionContext(chatSession, contextUpdate) {
    try {
      Object.assign(chatSession.context, contextUpdate);
      await chatSession.save();

    } catch (error) {
      logger.error('更新会话上下文失败', error);
    }
  }

  /**
   * 获取活跃会话统计
   */
  getActiveSessionStats() {
    return {
      activeSessions: this.activeSessions.size,
      connectedUsers: this.userSockets.size,
      totalOfflineMessages: Array.from(this.messageQueue.values()).reduce((sum, messages) => sum + messages.length, 0)
    };
  }
}

module.exports = new ChatService();
