const { Inventory, Product } = require('../models');
const logger = require('../utils/logger');

/**
 * 预警服务类
 * 处理库存预警、临期预警、过期预警等功能
 */
class AlertService {
  constructor() {
    // 预警类型定义
    this.alertTypes = {
      LOW_STOCK: {
        code: 'LOW_STOCK',
        name: '低库存预警',
        description: '库存数量低于安全库存',
        priority: 'medium'
      },
      OUT_OF_STOCK: {
        code: 'OUT_OF_STOCK',
        name: '零库存预警',
        description: '库存数量为零',
        priority: 'high'
      },
      EXPIRING_SOON: {
        code: 'EXPIRING_SOON',
        name: '临期商品预警',
        description: '商品即将过期',
        priority: 'medium'
      },
      EXPIRED: {
        code: 'EXPIRED',
        name: '过期商品预警',
        description: '商品已过期',
        priority: 'high'
      },
      NEGATIVE_STOCK: {
        code: 'NEGATIVE_STOCK',
        name: '负库存预警',
        description: '库存数量为负数',
        priority: 'critical'
      }
    };

    // 预警配置
    this.alertConfig = {
      expiringDays: 7, // 临期预警天数
      lowStockMultiplier: 1.0, // 低库存倍数（相对于安全库存）
      enabledAlerts: Object.keys(this.alertTypes) // 启用的预警类型
    };

    logger.info('AlertService初始化完成', {
      alertTypes: Object.keys(this.alertTypes).length,
      enabledAlerts: this.alertConfig.enabledAlerts.length
    });
  }

  /**
   * 检查所有预警
   * @returns {Promise<Object>} 预警结果
   */
  async checkAllAlerts() {
    try {
      logger.info('开始检查所有预警');

      const alerts = [];

      // 并行检查各种预警
      const [
        lowStockAlerts,
        outOfStockAlerts,
        expiringAlerts,
        expiredAlerts,
        negativeStockAlerts
      ] = await Promise.all([
        this.checkLowStockAlerts(),
        this.checkOutOfStockAlerts(),
        this.checkExpiringAlerts(),
        this.checkExpiredAlerts(),
        this.checkNegativeStockAlerts()
      ]);

      alerts.push(...lowStockAlerts);
      alerts.push(...outOfStockAlerts);
      alerts.push(...expiringAlerts);
      alerts.push(...expiredAlerts);
      alerts.push(...negativeStockAlerts);

      // 按优先级排序
      alerts.sort((a, b) => {
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });

      const summary = this.generateAlertSummary(alerts);

      logger.info('预警检查完成', {
        totalAlerts: alerts.length,
        critical: summary.critical,
        high: summary.high,
        medium: summary.medium,
        low: summary.low
      });

      return {
        success: true,
        data: {
          alerts,
          summary,
          checkTime: new Date().toISOString()
        }
      };

    } catch (error) {
      logger.error('预警检查失败', {
        error: error.message,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查低库存预警
   * @returns {Promise<Array>} 低库存预警列表
   */
  async checkLowStockAlerts() {
    try {
      const lowStockItems = await Inventory.aggregate([
        {
          $match: {
            is_active: true,
            $expr: {
              $and: [
                { $gt: ['$current_stock', 0] }, // 库存大于0
                { $lte: ['$current_stock', { $multiply: ['$reorder_point', this.alertConfig.lowStockMultiplier] }] }
              ]
            }
          }
        },
        {
          $lookup: {
            from: 'products',
            localField: 'product_id',
            foreignField: '_id',
            as: 'product'
          }
        },
        {
          $unwind: '$product'
        },
        {
          $match: {
            'product.is_active': true
          }
        },
        {
          $project: {
            product_id: 1,
            current_stock: 1,
            reorder_point: 1,
            location: 1,
            product: {
              name: 1,
              category: 1,
              barcode: 1,
              unit_price: 1
            }
          }
        }
      ]);

      return lowStockItems.map(item => ({
        type: this.alertTypes.LOW_STOCK.code,
        priority: this.alertTypes.LOW_STOCK.priority,
        title: this.alertTypes.LOW_STOCK.name,
        message: `${item.product.name} 库存不足`,
        details: {
          product_id: item.product_id,
          product_name: item.product.name,
          category: item.product.category,
          barcode: item.product.barcode,
          current_stock: item.current_stock,
          reorder_point: item.reorder_point,
          location: item.location,
          shortage: item.reorder_point - item.current_stock
        },
        created_at: new Date().toISOString()
      }));

    } catch (error) {
      logger.error('检查低库存预警失败', { error: error.message });
      return [];
    }
  }

  /**
   * 检查零库存预警
   * @returns {Promise<Array>} 零库存预警列表
   */
  async checkOutOfStockAlerts() {
    try {
      const outOfStockItems = await Inventory.aggregate([
        {
          $match: {
            is_active: true,
            current_stock: 0
          }
        },
        {
          $lookup: {
            from: 'products',
            localField: 'product_id',
            foreignField: '_id',
            as: 'product'
          }
        },
        {
          $unwind: '$product'
        },
        {
          $match: {
            'product.is_active': true
          }
        },
        {
          $project: {
            product_id: 1,
            current_stock: 1,
            reorder_point: 1,
            location: 1,
            product: {
              name: 1,
              category: 1,
              barcode: 1,
              unit_price: 1
            }
          }
        }
      ]);

      return outOfStockItems.map(item => ({
        type: this.alertTypes.OUT_OF_STOCK.code,
        priority: this.alertTypes.OUT_OF_STOCK.priority,
        title: this.alertTypes.OUT_OF_STOCK.name,
        message: `${item.product.name} 已缺货`,
        details: {
          product_id: item.product_id,
          product_name: item.product.name,
          category: item.product.category,
          barcode: item.product.barcode,
          current_stock: item.current_stock,
          reorder_point: item.reorder_point,
          location: item.location
        },
        created_at: new Date().toISOString()
      }));

    } catch (error) {
      logger.error('检查零库存预警失败', { error: error.message });
      return [];
    }
  }

  /**
   * 检查临期商品预警
   * @returns {Promise<Array>} 临期商品预警列表
   */
  async checkExpiringAlerts() {
    try {
      const expiringDate = new Date();
      expiringDate.setDate(expiringDate.getDate() + this.alertConfig.expiringDays);

      const expiringProducts = await Product.aggregate([
        {
          $match: {
            is_active: true,
            expiry_date: {
              $gte: new Date(),
              $lte: expiringDate
            }
          }
        },
        {
          $lookup: {
            from: 'inventories',
            localField: '_id',
            foreignField: 'product_id',
            as: 'inventory'
          }
        },
        {
          $unwind: '$inventory'
        },
        {
          $match: {
            'inventory.is_active': true,
            'inventory.current_stock': { $gt: 0 }
          }
        },
        {
          $project: {
            name: 1,
            category: 1,
            barcode: 1,
            expiry_date: 1,
            unit_price: 1,
            inventory: {
              current_stock: 1,
              location: 1
            }
          }
        }
      ]);

      return expiringProducts.map(product => {
        const daysToExpiry = Math.ceil((product.expiry_date - new Date()) / (1000 * 60 * 60 * 24));
        
        return {
          type: this.alertTypes.EXPIRING_SOON.code,
          priority: this.alertTypes.EXPIRING_SOON.priority,
          title: this.alertTypes.EXPIRING_SOON.name,
          message: `${product.name} 将在 ${daysToExpiry} 天后过期`,
          details: {
            product_id: product._id,
            product_name: product.name,
            category: product.category,
            barcode: product.barcode,
            expiry_date: product.expiry_date,
            days_to_expiry: daysToExpiry,
            current_stock: product.inventory.current_stock,
            location: product.inventory.location
          },
          created_at: new Date().toISOString()
        };
      });

    } catch (error) {
      logger.error('检查临期商品预警失败', { error: error.message });
      return [];
    }
  }

  /**
   * 检查过期商品预警
   * @returns {Promise<Array>} 过期商品预警列表
   */
  async checkExpiredAlerts() {
    try {
      const expiredProducts = await Product.aggregate([
        {
          $match: {
            is_active: true,
            expiry_date: { $lt: new Date() }
          }
        },
        {
          $lookup: {
            from: 'inventories',
            localField: '_id',
            foreignField: 'product_id',
            as: 'inventory'
          }
        },
        {
          $unwind: '$inventory'
        },
        {
          $match: {
            'inventory.is_active': true,
            'inventory.current_stock': { $gt: 0 }
          }
        },
        {
          $project: {
            name: 1,
            category: 1,
            barcode: 1,
            expiry_date: 1,
            unit_price: 1,
            inventory: {
              current_stock: 1,
              location: 1
            }
          }
        }
      ]);

      return expiredProducts.map(product => {
        const daysExpired = Math.ceil((new Date() - product.expiry_date) / (1000 * 60 * 60 * 24));
        
        return {
          type: this.alertTypes.EXPIRED.code,
          priority: this.alertTypes.EXPIRED.priority,
          title: this.alertTypes.EXPIRED.name,
          message: `${product.name} 已过期 ${daysExpired} 天`,
          details: {
            product_id: product._id,
            product_name: product.name,
            category: product.category,
            barcode: product.barcode,
            expiry_date: product.expiry_date,
            days_expired: daysExpired,
            current_stock: product.inventory.current_stock,
            location: product.inventory.location
          },
          created_at: new Date().toISOString()
        };
      });

    } catch (error) {
      logger.error('检查过期商品预警失败', { error: error.message });
      return [];
    }
  }

  /**
   * 检查负库存预警
   * @returns {Promise<Array>} 负库存预警列表
   */
  async checkNegativeStockAlerts() {
    try {
      const negativeStockItems = await Inventory.aggregate([
        {
          $match: {
            is_active: true,
            current_stock: { $lt: 0 }
          }
        },
        {
          $lookup: {
            from: 'products',
            localField: 'product_id',
            foreignField: '_id',
            as: 'product'
          }
        },
        {
          $unwind: '$product'
        },
        {
          $match: {
            'product.is_active': true
          }
        },
        {
          $project: {
            product_id: 1,
            current_stock: 1,
            location: 1,
            product: {
              name: 1,
              category: 1,
              barcode: 1
            }
          }
        }
      ]);

      return negativeStockItems.map(item => ({
        type: this.alertTypes.NEGATIVE_STOCK.code,
        priority: this.alertTypes.NEGATIVE_STOCK.priority,
        title: this.alertTypes.NEGATIVE_STOCK.name,
        message: `${item.product.name} 库存为负数`,
        details: {
          product_id: item.product_id,
          product_name: item.product.name,
          category: item.product.category,
          barcode: item.product.barcode,
          current_stock: item.current_stock,
          location: item.location
        },
        created_at: new Date().toISOString()
      }));

    } catch (error) {
      logger.error('检查负库存预警失败', { error: error.message });
      return [];
    }
  }

  /**
   * 生成预警摘要
   * @param {Array} alerts - 预警列表
   * @returns {Object} 预警摘要
   */
  generateAlertSummary(alerts) {
    const summary = {
      total: alerts.length,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      byType: {}
    };

    alerts.forEach(alert => {
      // 按优先级统计
      summary[alert.priority]++;

      // 按类型统计
      if (!summary.byType[alert.type]) {
        summary.byType[alert.type] = 0;
      }
      summary.byType[alert.type]++;
    });

    return summary;
  }

  /**
   * 获取预警配置
   * @returns {Object} 预警配置
   */
  getAlertConfig() {
    return {
      ...this.alertConfig,
      alertTypes: this.alertTypes
    };
  }

  /**
   * 更新预警配置
   * @param {Object} config - 新配置
   * @returns {Object} 更新结果
   */
  updateAlertConfig(config) {
    try {
      if (config.expiringDays !== undefined) {
        this.alertConfig.expiringDays = Math.max(1, Math.min(30, config.expiringDays));
      }

      if (config.lowStockMultiplier !== undefined) {
        this.alertConfig.lowStockMultiplier = Math.max(0.1, Math.min(5.0, config.lowStockMultiplier));
      }

      if (config.enabledAlerts !== undefined && Array.isArray(config.enabledAlerts)) {
        this.alertConfig.enabledAlerts = config.enabledAlerts.filter(
          type => Object.keys(this.alertTypes).includes(type)
        );
      }

      logger.info('预警配置已更新', this.alertConfig);

      return {
        success: true,
        config: this.alertConfig
      };

    } catch (error) {
      logger.error('更新预警配置失败', { error: error.message });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 智能预警分析
   * 基于历史数据进行趋势预测和智能预警
   * @param {Object} options - 分析选项
   * @returns {Promise<Object>} 智能预警结果
   */
  async performIntelligentAnalysis(options = {}) {
    try {
      const {
        days_back = 30,
        prediction_days = 7,
        include_trends = true,
        include_recommendations = true
      } = options;

      logger.info('开始智能预警分析', { days_back, prediction_days });

      const analysis = {
        trends: {},
        predictions: {},
        recommendations: [],
        risk_assessment: {},
        generated_at: new Date().toISOString()
      };

      // 并行执行各种分析
      const [
        stockTrends,
        consumptionPatterns,
        seasonalAnalysis,
        riskAssessment
      ] = await Promise.all([
        include_trends ? this.analyzeStockTrends(days_back) : null,
        this.analyzeConsumptionPatterns(days_back),
        this.analyzeSeasonalPatterns(days_back),
        this.assessInventoryRisks()
      ]);

      if (include_trends) {
        analysis.trends = stockTrends;
      }

      analysis.consumption_patterns = consumptionPatterns;
      analysis.seasonal_analysis = seasonalAnalysis;
      analysis.risk_assessment = riskAssessment;

      // 生成预测
      if (prediction_days > 0) {
        analysis.predictions = await this.generateStockPredictions(
          consumptionPatterns,
          prediction_days
        );
      }

      // 生成智能建议
      if (include_recommendations) {
        analysis.recommendations = await this.generateIntelligentRecommendations(
          analysis
        );
      }

      logger.info('智能预警分析完成', {
        trends_analyzed: Object.keys(analysis.trends).length,
        predictions_generated: Object.keys(analysis.predictions).length,
        recommendations: analysis.recommendations.length
      });

      return {
        success: true,
        data: analysis
      };

    } catch (error) {
      logger.error('智能预警分析失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 分析库存趋势
   * @param {number} daysBack - 回溯天数
   * @returns {Promise<Object>} 趋势分析结果
   */
  async analyzeStockTrends(daysBack) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - daysBack);

      // 获取库存变化数据
      const { StockInRecord } = require('../models');

      const stockMovements = await StockInRecord.aggregate([
        {
          $match: {
            created_at: { $gte: startDate },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } },
              product_id: '$product_id'
            },
            total_in: { $sum: '$quantity' },
            total_cost: { $sum: { $multiply: ['$quantity', '$unit_cost'] } }
          }
        },
        {
          $group: {
            _id: '$_id.product_id',
            daily_movements: {
              $push: {
                date: '$_id.date',
                quantity: '$total_in',
                cost: '$total_cost'
              }
            },
            total_quantity: { $sum: '$total_in' },
            avg_daily_quantity: { $avg: '$total_in' }
          }
        }
      ]);

      const trends = {};

      for (const movement of stockMovements) {
        const productId = movement._id.toString();
        const dailyData = movement.daily_movements;

        // 计算趋势
        const trend = this.calculateTrend(dailyData);

        trends[productId] = {
          total_quantity: movement.total_quantity,
          avg_daily_quantity: movement.avg_daily_quantity,
          trend_direction: trend.direction,
          trend_strength: trend.strength,
          volatility: trend.volatility,
          daily_data: dailyData
        };
      }

      return trends;

    } catch (error) {
      logger.error('分析库存趋势失败:', error);
      return {};
    }
  }

  /**
   * 分析消费模式
   * @param {number} daysBack - 回溯天数
   * @returns {Promise<Object>} 消费模式分析结果
   */
  async analyzeConsumptionPatterns(daysBack) {
    try {
      // 这里简化处理，实际应该分析出库记录
      // 由于当前系统主要关注入库，我们基于库存变化来推断消费

      const patterns = await Inventory.aggregate([
        {
          $match: { is_active: true }
        },
        {
          $lookup: {
            from: 'products',
            localField: 'product_id',
            foreignField: '_id',
            as: 'product'
          }
        },
        {
          $unwind: '$product'
        },
        {
          $group: {
            _id: '$product.category',
            total_stock: { $sum: '$current_stock' },
            avg_stock: { $avg: '$current_stock' },
            products_count: { $sum: 1 },
            low_stock_count: {
              $sum: {
                $cond: [
                  { $lte: ['$current_stock', '$reorder_point'] },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);

      const consumptionPatterns = {};

      patterns.forEach(pattern => {
        const category = pattern._id;
        const lowStockRatio = pattern.low_stock_count / pattern.products_count;

        consumptionPatterns[category] = {
          total_stock: pattern.total_stock,
          avg_stock: pattern.avg_stock,
          products_count: pattern.products_count,
          low_stock_ratio: lowStockRatio,
          consumption_intensity: this.calculateConsumptionIntensity(lowStockRatio),
          risk_level: this.assessCategoryRisk(lowStockRatio)
        };
      });

      return consumptionPatterns;

    } catch (error) {
      logger.error('分析消费模式失败:', error);
      return {};
    }
  }

  /**
   * 分析季节性模式
   * @param {number} daysBack - 回溯天数
   * @returns {Promise<Object>} 季节性分析结果
   */
  async analyzeSeasonalPatterns(daysBack) {
    try {
      const now = new Date();
      const currentMonth = now.getMonth() + 1;
      const currentSeason = this.getSeason(currentMonth);

      // 简化的季节性分析
      const seasonalFactors = {
        spring: { factor: 1.0, description: '春季正常消费' },
        summer: { factor: 1.2, description: '夏季消费增加' },
        autumn: { factor: 1.1, description: '秋季消费略增' },
        winter: { factor: 0.9, description: '冬季消费减少' }
      };

      return {
        current_season: currentSeason,
        current_factor: seasonalFactors[currentSeason].factor,
        seasonal_factors: seasonalFactors,
        recommendations: [
          `当前为${currentSeason}季，${seasonalFactors[currentSeason].description}`,
          '建议根据季节性因子调整库存预警阈值'
        ]
      };

    } catch (error) {
      logger.error('分析季节性模式失败:', error);
      return {};
    }
  }

  /**
   * 评估库存风险
   * @returns {Promise<Object>} 风险评估结果
   */
  async assessInventoryRisks() {
    try {
      const riskFactors = await Inventory.aggregate([
        {
          $match: { is_active: true }
        },
        {
          $lookup: {
            from: 'products',
            localField: 'product_id',
            foreignField: '_id',
            as: 'product'
          }
        },
        {
          $unwind: '$product'
        },
        {
          $project: {
            product_id: 1,
            current_stock: 1,
            reorder_point: 1,
            total_value: 1,
            product: {
              name: 1,
              category: 1,
              expiry_date: 1
            },
            stock_ratio: {
              $cond: [
                { $gt: ['$reorder_point', 0] },
                { $divide: ['$current_stock', '$reorder_point'] },
                0
              ]
            },
            days_to_expiry: {
              $cond: [
                { $ne: ['$product.expiry_date', null] },
                {
                  $divide: [
                    { $subtract: ['$product.expiry_date', new Date()] },
                    1000 * 60 * 60 * 24
                  ]
                },
                999
              ]
            }
          }
        }
      ]);

      let totalRisk = 0;
      let highRiskItems = 0;
      let mediumRiskItems = 0;
      let lowRiskItems = 0;

      const riskDetails = riskFactors.map(item => {
        let riskScore = 0;
        const riskFactors = [];

        // 库存比例风险
        if (item.stock_ratio < 0.5) {
          riskScore += 30;
          riskFactors.push('库存严重不足');
        } else if (item.stock_ratio < 1.0) {
          riskScore += 15;
          riskFactors.push('库存不足');
        }

        // 过期风险
        if (item.days_to_expiry < 7 && item.days_to_expiry > 0) {
          riskScore += 25;
          riskFactors.push('即将过期');
        } else if (item.days_to_expiry < 0) {
          riskScore += 40;
          riskFactors.push('已过期');
        }

        // 价值风险
        if (item.total_value > 1000) {
          riskScore += 10;
          riskFactors.push('高价值商品');
        }

        totalRisk += riskScore;

        let riskLevel = 'low';
        if (riskScore >= 50) {
          riskLevel = 'high';
          highRiskItems++;
        } else if (riskScore >= 25) {
          riskLevel = 'medium';
          mediumRiskItems++;
        } else {
          lowRiskItems++;
        }

        return {
          product_id: item.product_id,
          product_name: item.product.name,
          category: item.product.category,
          risk_score: riskScore,
          risk_level: riskLevel,
          risk_factors: riskFactors,
          current_stock: item.current_stock,
          total_value: item.total_value
        };
      });

      const avgRisk = riskFactors.length > 0 ? totalRisk / riskFactors.length : 0;

      return {
        overall_risk_score: avgRisk,
        overall_risk_level: this.getRiskLevel(avgRisk),
        high_risk_items: highRiskItems,
        medium_risk_items: mediumRiskItems,
        low_risk_items: lowRiskItems,
        risk_details: riskDetails.filter(item => item.risk_level !== 'low'),
        recommendations: this.generateRiskRecommendations(avgRisk, highRiskItems)
      };

    } catch (error) {
      logger.error('评估库存风险失败:', error);
      return {};
    }
  }

  /**
   * 生成库存预测
   * @param {Object} consumptionPatterns - 消费模式
   * @param {number} predictionDays - 预测天数
   * @returns {Promise<Object>} 预测结果
   */
  async generateStockPredictions(consumptionPatterns, predictionDays) {
    try {
      const predictions = {};

      // 获取当前库存
      const currentInventory = await Inventory.find({ is_active: true })
        .populate('product_id', 'name category');

      for (const inventory of currentInventory) {
        const category = inventory.product_id.category;
        const pattern = consumptionPatterns[category];

        if (pattern) {
          // 简化的预测算法
          const dailyConsumption = this.estimateDailyConsumption(
            inventory.current_stock,
            pattern.consumption_intensity
          );

          const predictedStock = Math.max(
            0,
            inventory.current_stock - (dailyConsumption * predictionDays)
          );

          const stockoutRisk = predictedStock <= inventory.reorder_point ? 'high' :
                              predictedStock <= inventory.reorder_point * 1.5 ? 'medium' : 'low';

          predictions[inventory.product_id._id.toString()] = {
            product_name: inventory.product_id.name,
            category: category,
            current_stock: inventory.current_stock,
            predicted_stock: Math.round(predictedStock),
            daily_consumption: dailyConsumption,
            days_until_reorder: Math.ceil(
              (inventory.current_stock - inventory.reorder_point) / dailyConsumption
            ),
            stockout_risk: stockoutRisk,
            recommended_action: this.getRecommendedAction(stockoutRisk, predictedStock, inventory.reorder_point)
          };
        }
      }

      return predictions;

    } catch (error) {
      logger.error('生成库存预测失败:', error);
      return {};
    }
  }

  /**
   * 生成智能建议
   * @param {Object} analysis - 分析结果
   * @returns {Promise<Array>} 建议列表
   */
  async generateIntelligentRecommendations(analysis) {
    try {
      const recommendations = [];

      // 基于风险评估的建议
      if (analysis.risk_assessment.overall_risk_level === 'high') {
        recommendations.push({
          type: 'urgent',
          priority: 'high',
          title: '高风险库存预警',
          description: '检测到多个高风险库存项目，建议立即采取行动',
          actions: [
            '优先处理高风险商品',
            '加快补货流程',
            '考虑调整安全库存水平'
          ]
        });
      }

      // 基于预测的建议
      if (analysis.predictions) {
        const highRiskPredictions = Object.values(analysis.predictions)
          .filter(p => p.stockout_risk === 'high');

        if (highRiskPredictions.length > 0) {
          recommendations.push({
            type: 'prediction',
            priority: 'medium',
            title: '库存短缺预警',
            description: `预测${highRiskPredictions.length}个商品可能在未来几天内缺货`,
            actions: [
              '提前安排补货',
              '联系供应商确认交货时间',
              '考虑临时调货'
            ]
          });
        }
      }

      // 基于季节性的建议
      if (analysis.seasonal_analysis) {
        const currentFactor = analysis.seasonal_analysis.current_factor;
        if (currentFactor > 1.1) {
          recommendations.push({
            type: 'seasonal',
            priority: 'medium',
            title: '季节性需求增加',
            description: '当前季节消费需求较高，建议调整库存策略',
            actions: [
              '增加热门商品的安全库存',
              '提高补货频率',
              '关注快速消费品类'
            ]
          });
        }
      }

      return recommendations;

    } catch (error) {
      logger.error('生成智能建议失败:', error);
      return [];
    }
  }

  // 辅助方法
  calculateTrend(dailyData) {
    if (dailyData.length < 2) {
      return { direction: 'stable', strength: 0, volatility: 0 };
    }

    const quantities = dailyData.map(d => d.quantity);
    const n = quantities.length;

    // 简单线性回归计算趋势
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    for (let i = 0; i < n; i++) {
      sumX += i;
      sumY += quantities[i];
      sumXY += i * quantities[i];
      sumXX += i * i;
    }

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const direction = slope > 0.1 ? 'increasing' : slope < -0.1 ? 'decreasing' : 'stable';
    const strength = Math.abs(slope);

    // 计算波动性
    const mean = sumY / n;
    const variance = quantities.reduce((sum, q) => sum + Math.pow(q - mean, 2), 0) / n;
    const volatility = Math.sqrt(variance) / mean;

    return { direction, strength, volatility };
  }

  calculateConsumptionIntensity(lowStockRatio) {
    if (lowStockRatio > 0.5) return 'high';
    if (lowStockRatio > 0.2) return 'medium';
    return 'low';
  }

  assessCategoryRisk(lowStockRatio) {
    if (lowStockRatio > 0.3) return 'high';
    if (lowStockRatio > 0.1) return 'medium';
    return 'low';
  }

  getSeason(month) {
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  getRiskLevel(score) {
    if (score >= 40) return 'high';
    if (score >= 20) return 'medium';
    return 'low';
  }

  generateRiskRecommendations(avgRisk, highRiskItems) {
    const recommendations = [];

    if (avgRisk >= 40) {
      recommendations.push('立即检查高风险商品库存');
      recommendations.push('加快补货流程');
    }

    if (highRiskItems > 5) {
      recommendations.push('考虑调整整体库存策略');
      recommendations.push('增加安全库存水平');
    }

    return recommendations;
  }

  estimateDailyConsumption(currentStock, intensity) {
    const baseConsumption = currentStock * 0.05; // 基础消费率5%

    const intensityMultiplier = {
      'high': 1.5,
      'medium': 1.0,
      'low': 0.5
    };

    return baseConsumption * (intensityMultiplier[intensity] || 1.0);
  }

  getRecommendedAction(risk, predictedStock, reorderPoint) {
    if (risk === 'high') {
      return '立即补货';
    } else if (risk === 'medium') {
      return '准备补货';
    } else if (predictedStock > reorderPoint * 2) {
      return '库存充足';
    } else {
      return '正常监控';
    }
  }
}

module.exports = new AlertService();
