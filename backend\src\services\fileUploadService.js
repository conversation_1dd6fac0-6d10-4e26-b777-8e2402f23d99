const s3Service = require('./s3Service');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

// 尝试加载Sharp，如果失败则禁用图片压缩功能
let sharp = null;
let sharpAvailable = false;

try {
  sharp = require('sharp');
  sharpAvailable = true;
  logger.info('Sharp模块加载成功，图片压缩功能可用');
} catch (error) {
  logger.warn('Sharp模块加载失败，图片压缩功能将被禁用', { error: error.message });
}

/**
 * 文件上传业务逻辑服务
 * 处理文件验证、压缩、上传等业务逻辑
 */
class FileUploadService {
  constructor() {
    // 支持的图片格式
    this.supportedImageTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/webp'
    ];

    // 文件大小限制（字节）
    this.maxFileSize = parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024; // 5MB

    // 图片压缩配置
    this.imageCompression = {
      quality: 85,
      maxWidth: 1920,
      maxHeight: 1080
    };

    logger.info('FileUploadService初始化完成', {
      maxFileSize: this.maxFileSize,
      supportedTypes: this.supportedImageTypes.length
    });
  }

  /**
   * 验证文件
   * @param {Object} file - 文件对象
   * @returns {Object} 验证结果
   */
  validateFile(file) {
    const errors = [];

    // 检查文件是否存在
    if (!file) {
      errors.push('未提供文件');
      return { isValid: false, errors };
    }

    // 检查文件大小
    if (file.size > this.maxFileSize) {
      errors.push(`文件大小超过限制 (${this.maxFileSize / 1024 / 1024}MB)`);
    }

    // 检查文件类型
    if (!this.supportedImageTypes.includes(file.mimetype)) {
      errors.push(`不支持的文件类型: ${file.mimetype}`);
    }

    // 检查文件名
    if (!file.originalname || file.originalname.trim() === '') {
      errors.push('文件名不能为空');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 压缩图片
   * @param {Buffer} imageBuffer - 图片缓冲区
   * @param {string} mimetype - 图片类型
   * @returns {Promise<Buffer>} 压缩后的图片缓冲区
   */
  async compressImage(imageBuffer, mimetype) {
    if (!sharpAvailable) {
      logger.warn('Sharp不可用，跳过图片压缩');
      return imageBuffer;
    }

    try {
      let sharpInstance = sharp(imageBuffer);

      // 获取图片信息
      const metadata = await sharpInstance.metadata();
      
      logger.info('开始压缩图片', {
        originalSize: imageBuffer.length,
        width: metadata.width,
        height: metadata.height,
        format: metadata.format
      });

      // 调整尺寸（如果超过最大尺寸）
      if (metadata.width > this.imageCompression.maxWidth || 
          metadata.height > this.imageCompression.maxHeight) {
        sharpInstance = sharpInstance.resize(
          this.imageCompression.maxWidth,
          this.imageCompression.maxHeight,
          {
            fit: 'inside',
            withoutEnlargement: true
          }
        );
      }

      // 根据文件类型进行压缩
      let compressedBuffer;
      
      switch (mimetype) {
        case 'image/jpeg':
        case 'image/jpg':
          compressedBuffer = await sharpInstance
            .jpeg({ quality: this.imageCompression.quality })
            .toBuffer();
          break;
          
        case 'image/png':
          compressedBuffer = await sharpInstance
            .png({ quality: this.imageCompression.quality })
            .toBuffer();
          break;
          
        case 'image/webp':
          compressedBuffer = await sharpInstance
            .webp({ quality: this.imageCompression.quality })
            .toBuffer();
          break;
          
        default:
          // 对于其他格式，转换为JPEG
          compressedBuffer = await sharpInstance
            .jpeg({ quality: this.imageCompression.quality })
            .toBuffer();
          break;
      }

      const compressionRatio = ((imageBuffer.length - compressedBuffer.length) / imageBuffer.length * 100).toFixed(2);

      logger.info('图片压缩完成', {
        originalSize: imageBuffer.length,
        compressedSize: compressedBuffer.length,
        compressionRatio: `${compressionRatio}%`
      });

      return compressedBuffer;

    } catch (error) {
      logger.error('图片压缩失败', {
        error: error.message,
        mimetype
      });

      // 如果压缩失败，返回原始图片
      return imageBuffer;
    }
  }

  /**
   * 上传单个文件
   * @param {Object} file - 文件对象
   * @param {string} folder - 存储文件夹
   * @param {Object} options - 上传选项
   * @returns {Promise<Object>} 上传结果
   */
  async uploadSingleFile(file, folder = 'uploads', options = {}) {
    try {
      // 验证文件
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        return {
          success: false,
          errors: validation.errors
        };
      }

      let fileBuffer = file.buffer;
      let contentType = file.mimetype;

      // 如果是图片且启用压缩且Sharp可用
      if (this.supportedImageTypes.includes(file.mimetype) &&
          options.compress !== false && sharpAvailable) {
        fileBuffer = await this.compressImage(file.buffer, file.mimetype);
      }

      // 上传到S3
      const uploadResult = await s3Service.uploadFile(
        fileBuffer,
        file.originalname,
        contentType,
        folder
      );

      if (uploadResult.success) {
        logger.info('文件上传成功', {
          originalName: file.originalname,
          key: uploadResult.data.key,
          size: uploadResult.data.size
        });

        return {
          success: true,
          data: {
            id: uuidv4(),
            key: uploadResult.data.key,
            originalName: file.originalname,
            contentType,
            size: uploadResult.data.size,
            location: uploadResult.data.location,
            uploadTime: new Date().toISOString()
          }
        };
      }

      return uploadResult;

    } catch (error) {
      logger.error('文件上传失败', {
        fileName: file?.originalname,
        error: error.message
      });

      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 批量上传文件
   * @param {Array} files - 文件数组
   * @param {string} folder - 存储文件夹
   * @param {Object} options - 上传选项
   * @returns {Promise<Object>} 批量上传结果
   */
  async uploadMultipleFiles(files, folder = 'uploads', options = {}) {
    try {
      if (!files || files.length === 0) {
        return {
          success: false,
          message: '未提供文件'
        };
      }

      const results = [];
      const errors = [];

      // 并发上传文件
      const uploadPromises = files.map(async (file, index) => {
        try {
          const result = await this.uploadSingleFile(file, folder, options);
          
          if (result.success) {
            results.push(result.data);
          } else {
            errors.push({
              index,
              fileName: file.originalname,
              errors: result.errors || [result.message]
            });
          }
        } catch (error) {
          errors.push({
            index,
            fileName: file.originalname,
            errors: [error.message]
          });
        }
      });

      await Promise.all(uploadPromises);

      logger.info('批量文件上传完成', {
        totalFiles: files.length,
        successCount: results.length,
        errorCount: errors.length
      });

      return {
        success: errors.length === 0,
        data: {
          uploaded: results,
          errors: errors,
          summary: {
            total: files.length,
            success: results.length,
            failed: errors.length
          }
        }
      };

    } catch (error) {
      logger.error('批量文件上传失败', {
        error: error.message
      });

      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 获取文件访问URL
   * @param {string} key - 文件键
   * @param {number} expiresIn - 过期时间（秒）
   * @returns {Promise<string>} 文件访问URL
   */
  async getFileUrl(key, expiresIn = 3600) {
    try {
      return await s3Service.getSignedUrl(key, expiresIn);
    } catch (error) {
      logger.error('获取文件URL失败', {
        key,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 删除文件
   * @param {string} key - 文件键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteFile(key) {
    try {
      return await s3Service.deleteFile(key);
    } catch (error) {
      logger.error('删除文件失败', {
        key,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = new FileUploadService();
