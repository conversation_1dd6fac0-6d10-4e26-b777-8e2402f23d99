#!/usr/bin/env node

/**
 * 安全审计脚本
 * 检查项目中的安全漏洞和最佳实践
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查依赖包安全漏洞
function checkDependencyVulnerabilities() {
  log('\n🔍 检查依赖包安全漏洞...', 'blue');
  
  try {
    // 检查后端依赖
    log('检查后端依赖...', 'blue');
    process.chdir('backend');
    const backendAudit = execSync('npm audit --json', { encoding: 'utf8' });
    const backendResult = JSON.parse(backendAudit);
    
    if (backendResult.vulnerabilities && Object.keys(backendResult.vulnerabilities).length > 0) {
      log(`❌ 后端发现 ${Object.keys(backendResult.vulnerabilities).length} 个安全漏洞`, 'red');
      
      // 显示高危漏洞
      Object.entries(backendResult.vulnerabilities).forEach(([pkg, vuln]) => {
        if (vuln.severity === 'high' || vuln.severity === 'critical') {
          log(`  - ${pkg}: ${vuln.severity} (${vuln.title})`, 'red');
        }
      });
    } else {
      log('✅ 后端依赖安全检查通过', 'green');
    }
    
    process.chdir('..');
    
    // 检查前端依赖
    log('检查前端依赖...', 'blue');
    process.chdir('frontend');
    const frontendAudit = execSync('npm audit --json', { encoding: 'utf8' });
    const frontendResult = JSON.parse(frontendAudit);
    
    if (frontendResult.vulnerabilities && Object.keys(frontendResult.vulnerabilities).length > 0) {
      log(`❌ 前端发现 ${Object.keys(frontendResult.vulnerabilities).length} 个安全漏洞`, 'red');
    } else {
      log('✅ 前端依赖安全检查通过', 'green');
    }
    
    process.chdir('..');
    
  } catch (error) {
    log(`⚠️  依赖检查失败: ${error.message}`, 'yellow');
  }
}

// 检查环境变量安全
function checkEnvironmentSecurity() {
  log('\n🔒 检查环境变量安全...', 'blue');
  
  const envSamplePath = 'backend/.env.sample';
  const envProdPath = 'backend/.env.production';
  
  const securityIssues = [];
  
  // 检查.env.sample
  if (fs.existsSync(envSamplePath)) {
    const envContent = fs.readFileSync(envSamplePath, 'utf8');
    
    // 检查默认密码
    if (envContent.includes('your_super_secret_jwt_key_here')) {
      securityIssues.push('JWT_SECRET 使用默认值');
    }
    
    if (envContent.includes('your_aws_access_key')) {
      securityIssues.push('AWS 凭证使用默认值');
    }
    
    if (envContent.includes('your_deepseek_api_key_here')) {
      securityIssues.push('DeepSeek API密钥使用默认值');
    }
  }
  
  // 检查生产环境配置
  if (fs.existsSync(envProdPath)) {
    const prodContent = fs.readFileSync(envProdPath, 'utf8');
    
    if (prodContent.includes('REPLACE_WITH_')) {
      securityIssues.push('生产环境配置包含占位符');
    }
  }
  
  if (securityIssues.length > 0) {
    log('❌ 发现环境变量安全问题:', 'red');
    securityIssues.forEach(issue => log(`  - ${issue}`, 'red'));
  } else {
    log('✅ 环境变量安全检查通过', 'green');
  }
}

// 检查代码中的安全问题
function checkCodeSecurity() {
  log('\n🛡️  检查代码安全问题...', 'blue');
  
  const securityPatterns = [
    {
      pattern: /console\.log\(.*password.*\)/gi,
      message: '代码中可能泄露密码信息',
      severity: 'high'
    },
    {
      pattern: /eval\(/gi,
      message: '使用了危险的 eval() 函数',
      severity: 'critical'
    },
    {
      pattern: /innerHTML\s*=/gi,
      message: '使用 innerHTML 可能导致XSS攻击',
      severity: 'medium'
    },
    {
      pattern: /process\.env\.[A-Z_]+/gi,
      message: '直接使用环境变量（需要验证是否安全）',
      severity: 'low'
    }
  ];
  
  const issues = [];
  
  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        scanDirectory(filePath);
      } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        securityPatterns.forEach(({ pattern, message, severity }) => {
          const matches = content.match(pattern);
          if (matches) {
            issues.push({
              file: filePath,
              message,
              severity,
              matches: matches.length
            });
          }
        });
      }
    });
  }
  
  // 扫描后端代码
  if (fs.existsSync('backend/src')) {
    scanDirectory('backend/src');
  }
  
  // 扫描前端代码
  if (fs.existsSync('frontend/src')) {
    scanDirectory('frontend/src');
  }
  
  if (issues.length > 0) {
    log(`❌ 发现 ${issues.length} 个潜在安全问题:`, 'red');
    issues.forEach(issue => {
      const color = issue.severity === 'critical' ? 'red' : 
                   issue.severity === 'high' ? 'red' : 
                   issue.severity === 'medium' ? 'yellow' : 'blue';
      log(`  - ${issue.file}: ${issue.message} (${issue.severity})`, color);
    });
  } else {
    log('✅ 代码安全检查通过', 'green');
  }
}

// 检查文件权限
function checkFilePermissions() {
  log('\n📁 检查文件权限...', 'blue');
  
  const sensitiveFiles = [
    'backend/.env',
    'backend/.env.production',
    'backend/ecosystem.config.js'
  ];
  
  const issues = [];
  
  sensitiveFiles.forEach(file => {
    if (fs.existsSync(file)) {
      try {
        const stats = fs.statSync(file);
        const mode = stats.mode.toString(8);
        
        // 检查是否对其他用户可读
        if (mode.endsWith('4') || mode.endsWith('6') || mode.endsWith('7')) {
          issues.push(`${file} 对其他用户可读 (权限: ${mode})`);
        }
      } catch (error) {
        log(`⚠️  无法检查 ${file} 的权限: ${error.message}`, 'yellow');
      }
    }
  });
  
  if (issues.length > 0) {
    log('❌ 发现文件权限问题:', 'red');
    issues.forEach(issue => log(`  - ${issue}`, 'red'));
  } else {
    log('✅ 文件权限检查通过', 'green');
  }
}

// 生成安全报告
function generateSecurityReport() {
  log('\n📊 生成安全审计报告...', 'blue');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total_checks: 4,
      passed: 0,
      failed: 0,
      warnings: 0
    },
    recommendations: [
      '定期更新依赖包到最新版本',
      '使用强密码和密钥',
      '启用HTTPS和安全头',
      '实施输入验证和输出编码',
      '定期进行安全审计',
      '使用专业的密钥管理服务'
    ]
  };
  
  fs.writeFileSync('security-audit-report.json', JSON.stringify(report, null, 2));
  log('✅ 安全报告已生成: security-audit-report.json', 'green');
}

// 主函数
function main() {
  log('🔐 开始安全审计...', 'blue');
  log('=' * 50, 'blue');
  
  checkDependencyVulnerabilities();
  checkEnvironmentSecurity();
  checkCodeSecurity();
  checkFilePermissions();
  generateSecurityReport();
  
  log('\n🎯 安全审计完成！', 'green');
  log('请查看上述结果并及时修复发现的问题。', 'blue');
}

if (require.main === module) {
  main();
}

module.exports = {
  checkDependencyVulnerabilities,
  checkEnvironmentSecurity,
  checkCodeSecurity,
  checkFilePermissions
};
