const crypto = require('crypto');
const { TIME_CONSTANTS } = require('./constants');

/**
 * 辅助函数工具集
 */

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @param {string} charset - 字符集
 * @returns {string} 随机字符串
 */
const generateRandomString = (length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
};

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
const generateUUID = () => {
  return crypto.randomUUID();
};

/**
 * 生成哈希值
 * @param {string} data - 要哈希的数据
 * @param {string} algorithm - 哈希算法
 * @returns {string} 哈希值
 */
const generateHash = (data, algorithm = 'sha256') => {
  return crypto.createHash(algorithm).update(data).digest('hex');
};

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @param {string} format - 格式字符串
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

/**
 * 计算两个日期之间的差异
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @param {string} unit - 单位 ('days', 'hours', 'minutes', 'seconds')
 * @returns {number} 差异值
 */
const dateDiff = (startDate, endDate, unit = 'days') => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffMs = end - start;
  
  switch (unit) {
    case 'seconds':
      return Math.floor(diffMs / 1000);
    case 'minutes':
      return Math.floor(diffMs / TIME_CONSTANTS.MINUTE);
    case 'hours':
      return Math.floor(diffMs / TIME_CONSTANTS.HOUR);
    case 'days':
      return Math.floor(diffMs / TIME_CONSTANTS.DAY);
    default:
      return diffMs;
  }
};

/**
 * 检查日期是否在指定范围内
 * @param {Date} date - 要检查的日期
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {boolean} 是否在范围内
 */
const isDateInRange = (date, startDate, endDate) => {
  const d = new Date(date);
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  return d >= start && d <= end;
};

/**
 * 深度克隆对象
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

/**
 * 对象属性过滤
 * @param {Object} obj - 源对象
 * @param {Array} allowedFields - 允许的字段列表
 * @returns {Object} 过滤后的对象
 */
const filterObject = (obj, allowedFields) => {
  const filtered = {};
  allowedFields.forEach(field => {
    if (obj.hasOwnProperty(field)) {
      filtered[field] = obj[field];
    }
  });
  return filtered;
};

/**
 * 移除对象中的空值
 * @param {Object} obj - 源对象
 * @param {boolean} removeEmptyStrings - 是否移除空字符串
 * @returns {Object} 清理后的对象
 */
const removeEmptyValues = (obj, removeEmptyStrings = true) => {
  const cleaned = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      if (value !== null && value !== undefined) {
        if (removeEmptyStrings && value === '') {
          continue;
        }
        cleaned[key] = value;
      }
    }
  }
  return cleaned;
};

/**
 * 数组分页
 * @param {Array} array - 源数组
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @returns {Object} 分页结果
 */
const paginateArray = (array, page = 1, limit = 20) => {
  const offset = (page - 1) * limit;
  const paginatedItems = array.slice(offset, offset + limit);
  const totalPages = Math.ceil(array.length / limit);
  
  return {
    data: paginatedItems,
    pagination: {
      current_page: page,
      per_page: limit,
      total: array.length,
      total_pages: totalPages,
      has_next: page < totalPages,
      has_prev: page > 1
    }
  };
};

/**
 * 数字格式化
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数
 * @param {string} thousandsSep - 千位分隔符
 * @param {string} decimalSep - 小数分隔符
 * @returns {string} 格式化后的数字字符串
 */
const formatNumber = (num, decimals = 2, thousandsSep = ',', decimalSep = '.') => {
  if (isNaN(num)) return '0';
  
  const number = parseFloat(num).toFixed(decimals);
  const parts = number.split('.');
  
  // 添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSep);
  
  return parts.join(decimalSep);
};

/**
 * 货币格式化
 * @param {number} amount - 金额
 * @param {string} currency - 货币符号
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的货币字符串
 */
const formatCurrency = (amount, currency = '¥', decimals = 2) => {
  return currency + formatNumber(amount, decimals);
};

/**
 * 文件大小格式化
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 */
const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
};

/**
 * 字符串截断
 * @param {string} str - 源字符串
 * @param {number} length - 最大长度
 * @param {string} suffix - 后缀
 * @returns {string} 截断后的字符串
 */
const truncateString = (str, length = 100, suffix = '...') => {
  if (!str || str.length <= length) return str;
  return str.substring(0, length) + suffix;
};

/**
 * 手机号脱敏
 * @param {string} phone - 手机号
 * @returns {string} 脱敏后的手机号
 */
const maskPhone = (phone) => {
  if (!phone || phone.length < 7) return phone;
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

/**
 * 邮箱脱敏
 * @param {string} email - 邮箱
 * @returns {string} 脱敏后的邮箱
 */
const maskEmail = (email) => {
  if (!email || !email.includes('@')) return email;
  const [username, domain] = email.split('@');
  if (username.length <= 2) return email;
  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
  return maskedUsername + '@' + domain;
};

/**
 * 生成分页元数据
 * @param {number} total - 总数
 * @param {number} page - 当前页
 * @param {number} limit - 每页数量
 * @returns {Object} 分页元数据
 */
const generatePaginationMeta = (total, page, limit) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    current_page: parseInt(page),
    per_page: parseInt(limit),
    total: parseInt(total),
    total_pages: totalPages,
    has_next: page < totalPages,
    has_prev: page > 1,
    from: total > 0 ? (page - 1) * limit + 1 : 0,
    to: Math.min(page * limit, total)
  };
};

/**
 * 延迟执行
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} Promise对象
 */
const delay = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 重试执行函数
 * @param {Function} fn - 要执行的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delayMs - 重试间隔
 * @returns {Promise} Promise对象
 */
const retry = async (fn, maxRetries = 3, delayMs = 1000) => {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (i < maxRetries) {
        await delay(delayMs);
      }
    }
  }
  
  throw lastError;
};

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {any} defaultValue - 默认值
 * @returns {any} 解析结果
 */
const safeJsonParse = (jsonString, defaultValue = null) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    return defaultValue;
  }
};

/**
 * 检查是否为空值
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为空
 */
const isEmpty = (value) => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

module.exports = {
  generateRandomString,
  generateUUID,
  generateHash,
  formatDate,
  dateDiff,
  isDateInRange,
  deepClone,
  filterObject,
  removeEmptyValues,
  paginateArray,
  formatNumber,
  formatCurrency,
  formatFileSize,
  truncateString,
  maskPhone,
  maskEmail,
  generatePaginationMeta,
  delay,
  retry,
  safeJsonParse,
  isEmpty
};
