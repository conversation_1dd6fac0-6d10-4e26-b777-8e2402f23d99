version: '3.8'

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:7.0
    container_name: inventory-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: inventory_ai_db
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - inventory-network

  # Redis 缓存 (可选)
  redis:
    image: redis:7.2-alpine
    container_name: inventory-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - inventory-network

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: inventory-backend
    restart: unless-stopped
    ports:
      - "4000:4000"
    environment:
      NODE_ENV: development
      PORT: 4000
      MONGO_URI: **************************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev_jwt_secret_key_change_in_production
      FRONTEND_URL: http://localhost:5173
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./logs:/app/logs
    depends_on:
      - mongodb
      - redis
    networks:
      - inventory-network
    command: npm run dev

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: inventory-frontend
    restart: unless-stopped
    ports:
      - "5173:5173"
    environment:
      VITE_API_BASE_URL: http://localhost:4000/api
      VITE_SOCKET_URL: http://localhost:4000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - inventory-network
    command: npm run dev

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  inventory-network:
    driver: bridge
