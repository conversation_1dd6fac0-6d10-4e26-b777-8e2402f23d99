const mongoose = require('mongoose');

/**
 * 政策版本历史子文档
 */
const policyVersionSchema = new mongoose.Schema({
  version: {
    type: String,
    required: [true, '版本号是必需的'],
    trim: true
  },
  content: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, '政策内容是必需的']
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '创建者是必需的']
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  change_summary: {
    type: String,
    trim: true,
    maxlength: [500, '变更摘要不能超过500个字符']
  },
  is_active: {
    type: Boolean,
    default: false
  }
}, { _id: false });

/**
 * 政策主文档
 */
const policySchema = new mongoose.Schema({
  policy_id: {
    type: String,
    unique: true,
    index: true
  },
  name: {
    type: String,
    required: [true, '政策名称是必需的'],
    trim: true,
    maxlength: [100, '政策名称不能超过100个字符'],
    index: true
  },
  category: {
    type: String,
    required: [true, '政策类别是必需的'],
    enum: [
      'mission',        // 使命理念
      'group_rules',    // 群规管理
      'product_quality', // 产品质量
      'delivery',       // 配送服务
      'payment',        // 付款方式
      'pickup',         // 取货点
      'after_sale',     // 售后服务
      'community',      // 社区文化
      'other'           // 其他
    ],
    index: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, '政策描述不能超过500个字符']
  },
  current_version: {
    type: String,
    required: [true, '当前版本是必需的'],
    trim: true
  },
  current_content: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, '当前内容是必需的']
  },
  versions: {
    type: [policyVersionSchema],
    default: []
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  keywords: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  priority: {
    type: Number,
    min: 1,
    max: 10,
    default: 5
  },
  is_active: {
    type: Boolean,
    default: true,
    index: true
  },
  is_public: {
    type: Boolean,
    default: true
  },
  effective_date: {
    type: Date,
    default: Date.now,
    index: true
  },
  expiry_date: {
    type: Date,
    index: true
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '创建者是必需的']
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  last_reviewed_at: {
    type: Date,
    index: true
  },
  review_frequency_days: {
    type: Number,
    min: 1,
    default: 90 // 默认90天审核一次
  },
  usage_count: {
    type: Number,
    default: 0,
    min: 0
  },
  feedback_score: {
    type: Number,
    min: 1,
    max: 5
  },
  metadata: {
    source: {
      type: String,
      enum: ['manual', 'import', 'api', 'migration'],
      default: 'manual'
    },
    import_file: String,
    external_id: String,
    sync_status: {
      type: String,
      enum: ['synced', 'pending', 'failed'],
      default: 'synced'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 生成政策ID
policySchema.pre('save', function(next) {
  if (!this.policy_id) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.policy_id = `policy_${timestamp}_${random}`;
  }
  next();
});

// 更新版本历史
policySchema.methods.addVersion = function(content, userId, changeSummary = '') {
  // 将当前版本设为非活跃
  this.versions.forEach(version => {
    version.is_active = false;
  });

  // 生成新版本号
  const versionNumber = this.versions.length + 1;
  const newVersion = `${this.current_version.split('.')[0]}.${versionNumber}.0`;

  // 添加新版本
  this.versions.push({
    version: newVersion,
    content: content,
    created_by: userId,
    change_summary: changeSummary,
    is_active: true
  });

  // 更新当前版本和内容
  this.current_version = newVersion;
  this.current_content = content;
  this.updated_by = userId;

  return newVersion;
};

// 获取活跃版本
policySchema.methods.getActiveVersion = function() {
  return this.versions.find(version => version.is_active) || {
    version: this.current_version,
    content: this.current_content,
    created_at: this.updatedAt
  };
};

// 获取版本历史
policySchema.methods.getVersionHistory = function(limit = 10) {
  return this.versions
    .sort((a, b) => b.created_at - a.created_at)
    .slice(0, limit);
};

// 检查是否需要审核
policySchema.virtual('needs_review').get(function() {
  if (!this.last_reviewed_at) return true;
  
  const daysSinceReview = (Date.now() - this.last_reviewed_at) / (1000 * 60 * 60 * 24);
  return daysSinceReview >= this.review_frequency_days;
});

// 获取政策摘要
policySchema.virtual('summary').get(function() {
  return {
    policy_id: this.policy_id,
    name: this.name,
    category: this.category,
    current_version: this.current_version,
    is_active: this.is_active,
    effective_date: this.effective_date,
    needs_review: this.needs_review
  };
});

// 索引
policySchema.index({ category: 1, is_active: 1 });
policySchema.index({ tags: 1 });
policySchema.index({ keywords: 1 });
policySchema.index({ effective_date: 1, expiry_date: 1 });
policySchema.index({ 'versions.created_at': -1 });

// 静态方法：根据类别获取政策
policySchema.statics.getByCategory = function(category, activeOnly = true) {
  const query = { category };
  if (activeOnly) {
    query.is_active = true;
    query.$or = [
      { expiry_date: { $exists: false } },
      { expiry_date: null },
      { expiry_date: { $gt: new Date() } }
    ];
  }
  
  return this.find(query).sort({ priority: -1, updatedAt: -1 });
};

// 静态方法：搜索政策
policySchema.statics.search = function(keyword, options = {}) {
  const {
    category = null,
    activeOnly = true,
    limit = 20
  } = options;

  const query = {
    $or: [
      { name: { $regex: keyword, $options: 'i' } },
      { description: { $regex: keyword, $options: 'i' } },
      { tags: { $in: [new RegExp(keyword, 'i')] } },
      { keywords: { $in: [new RegExp(keyword, 'i')] } }
    ]
  };

  if (category) {
    query.category = category;
  }

  if (activeOnly) {
    query.is_active = true;
    query.$and = [
      query.$and || {},
      {
        $or: [
          { expiry_date: { $exists: false } },
          { expiry_date: null },
          { expiry_date: { $gt: new Date() } }
        ]
      }
    ];
  }

  return this.find(query)
    .sort({ priority: -1, usage_count: -1 })
    .limit(limit);
};

// 静态方法：获取需要审核的政策
policySchema.statics.getNeedingReview = function() {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - 90); // 默认90天

  return this.find({
    is_active: true,
    $or: [
      { last_reviewed_at: { $exists: false } },
      { last_reviewed_at: null },
      { last_reviewed_at: { $lt: cutoffDate } }
    ]
  }).sort({ priority: -1 });
};

// 静态方法：增加使用计数
policySchema.statics.incrementUsage = function(policyId) {
  return this.findOneAndUpdate(
    { policy_id: policyId },
    { $inc: { usage_count: 1 } },
    { new: true }
  );
};

module.exports = mongoose.model('Policy', policySchema);
