const { Inventory, Product, StockInRecord, OperationLog } = require('../models');
const stockInService = require('../services/stockInService');
const logger = require('../utils/logger');

/**
 * 库存控制器
 * 处理库存查询、入库、出库、盘点等操作
 */
class InventoryController {
  /**
   * 获取库存总览
   */
  async getInventoryOverview(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        page = 1,
        limit = 20,
        keyword,
        category,
        location,
        low_stock = false,
        sort_by = 'updatedAt',
        sort_order = 'desc'
      } = req.query;
      
      // 构建查询条件
      const query = { is_active: true };
      
      // 构建产品查询条件
      const productQuery = { is_active: true };
      
      if (category) {
        productQuery.category = category;
      }
      
      if (keyword) {
        productQuery.$or = [
          { name: { $regex: keyword, $options: 'i' } },
          { barcode: { $regex: keyword, $options: 'i' } },
          { supplier: { $regex: keyword, $options: 'i' } }
        ];
      }
      
      if (location) {
        query.location = { $regex: location, $options: 'i' };
      }
      
      // 获取符合条件的产品ID
      const products = await Product.find(productQuery).select('_id');
      const productIds = products.map(p => p._id);
      
      if (productIds.length === 0 && (keyword || category)) {
        return res.json({
          success: true,
          data: {
            inventory: [],
            pagination: {
              current_page: parseInt(page),
              per_page: parseInt(limit),
              total: 0,
              total_pages: 0
            }
          }
        });
      }
      
      if (productIds.length > 0) {
        query.product_id = { $in: productIds };
      }
      
      // 构建排序
      const sortOptions = {};
      sortOptions[sort_by] = sort_order === 'desc' ? -1 : 1;
      
      // 分页查询
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      let inventoryQuery = Inventory.find(query);
      
      // 低库存筛选
      if (low_stock === 'true') {
        inventoryQuery = inventoryQuery.where('current_stock').lte('$reorder_point');
      }
      
      const [inventory, total] = await Promise.all([
        inventoryQuery
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('product_id', 'name category barcode photo_url unit_price supplier safety_stock')
          .populate('updated_by', 'username name'),
        Inventory.countDocuments(query)
      ]);
      
      // 计算库存统计
      const stats = await this.calculateInventoryStats();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        req.user.userId,
        'inventory.view',
        'inventory',
        'overview',
        {
          action: 'read',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `查询库存总览: ${inventory.length}条记录`
        }
      );
      
      res.json({
        success: true,
        data: {
          inventory,
          stats,
          pagination: {
            current_page: parseInt(page),
            per_page: parseInt(limit),
            total,
            total_pages: Math.ceil(total / parseInt(limit))
          }
        }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'inventory.view',
        'inventory',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime
        }
      );
      
      logger.error('获取库存总览错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 创建入库单
   */
  async createStockInRecord(req, res) {
    const startTime = Date.now();
    
    try {
      const {
        supplier,
        supplier_invoice,
        items,
        warehouse_location,
        delivery_note,
        notes
      } = req.body;
      
      const userId = req.user.userId;
      
      // 验证必需字段
      if (!items || !Array.isArray(items) || items.length === 0) {
        return res.status(400).json({
          success: false,
          message: '入库商品列表是必需的'
        });
      }
      
      // 验证商品项目
      for (let item of items) {
        if (!item.product_id || !item.quantity || !item.unit_cost) {
          return res.status(400).json({
            success: false,
            message: '每个商品项目必须包含产品ID、数量和单位成本'
          });
        }
        
        // 验证产品是否存在
        const product = await Product.findById(item.product_id);
        if (!product) {
          return res.status(404).json({
            success: false,
            message: `产品 ${item.product_id} 不存在`
          });
        }
        
        // 计算总成本
        item.total_cost = item.quantity * item.unit_cost;
      }
      
      // 创建入库记录
      const stockInRecord = new StockInRecord({
        supplier,
        supplier_invoice,
        items,
        warehouse_location,
        delivery_note,
        notes,
        created_by: userId
      });
      
      await stockInRecord.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'inventory.stock_in',
        'inventory',
        stockInRecord._id.toString(),
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          after_data: {
            record_number: stockInRecord.record_number,
            total_quantity: stockInRecord.total_quantity,
            total_amount: stockInRecord.total_amount
          },
          description: `创建入库单: ${stockInRecord.record_number}`
        }
      );
      
      logger.info(`入库单创建: ${stockInRecord.record_number} by ${req.user.username}`);
      
      res.status(201).json({
        success: true,
        message: '入库单创建成功',
        data: { stockInRecord }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'inventory.stock_in',
        'inventory',
        error,
        {
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'medium'
        }
      );
      
      logger.error('创建入库单错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 处理入库单
   */
  async processStockInRecord(req, res) {
    const startTime = Date.now();
    
    try {
      const { id } = req.params;
      const userId = req.user.userId;
      
      const stockInRecord = await StockInRecord.findById(id)
        .populate('items.product_id', 'name category barcode');
      
      if (!stockInRecord) {
        return res.status(404).json({
          success: false,
          message: '入库单不存在'
        });
      }
      
      if (stockInRecord.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: '只能处理待处理状态的入库单'
        });
      }
      
      // 开始处理入库
      await stockInRecord.process(userId);
      
      // 更新库存
      for (let item of stockInRecord.items) {
        let inventory = await Inventory.findOne({ product_id: item.product_id });
        
        if (!inventory) {
          // 创建新的库存记录
          inventory = new Inventory({
            product_id: item.product_id,
            current_stock: 0,
            location: item.location || stockInRecord.warehouse_location,
            updated_by: userId
          });
        }
        
        // 执行入库操作
        inventory.stockIn(item.quantity, item.unit_cost, userId);
        await inventory.save();
      }
      
      // 标记入库单为完成
      await stockInRecord.complete();
      await stockInRecord.save();
      
      const responseTime = Date.now() - startTime;
      
      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'inventory.stock_in',
        'inventory',
        id,
        {
          action: 'process',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          description: `处理入库单: ${stockInRecord.record_number}`
        }
      );
      
      logger.info(`入库单处理完成: ${stockInRecord.record_number} by ${req.user.username}`);
      
      res.json({
        success: true,
        message: '入库处理成功',
        data: { stockInRecord }
      });
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await OperationLog.logFailure(
        req.user.userId,
        'inventory.stock_in',
        'inventory',
        error,
        {
          resource_id: req.params.id,
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 500,
          response_time_ms: responseTime,
          severity: 'high'
        }
      );
      
      logger.error('处理入库单错误:', error);
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
  
  /**
   * 获取入库记录列表
   */
  async getStockInRecords(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        supplier,
        start_date,
        end_date,
        sort_by = 'createdAt',
        sort_order = 'desc'
      } = req.query;

      // 构建查询条件
      const query = {};

      if (status) {
        query.status = status;
      }

      if (supplier) {
        query.supplier = { $regex: supplier, $options: 'i' };
      }

      if (start_date || end_date) {
        query.stock_in_date = {};
        if (start_date) {
          query.stock_in_date.$gte = new Date(start_date);
        }
        if (end_date) {
          query.stock_in_date.$lte = new Date(end_date);
        }
      }

      // 构建排序
      const sortOptions = {};
      sortOptions[sort_by] = sort_order === 'desc' ? -1 : 1;

      // 分页查询
      const skip = (parseInt(page) - 1) * parseInt(limit);

      const [records, total] = await Promise.all([
        StockInRecord.find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('created_by', 'username name')
          .populate('processed_by', 'username name')
          .populate('items.product_id', 'name category barcode'),
        StockInRecord.countDocuments(query)
      ]);

      res.json({
        success: true,
        data: {
          records,
          pagination: {
            current_page: parseInt(page),
            per_page: parseInt(limit),
            total,
            total_pages: Math.ceil(total / parseInt(limit))
          }
        }
      });

    } catch (error) {
      logger.error('获取入库记录错误:', error);

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取库存预警
   */
  async getInventoryAlerts(req, res) {
    try {
      const { type = 'all' } = req.query;

      const alerts = [];

      if (type === 'all' || type === 'low_stock') {
        // 低库存预警
        const lowStockItems = await Inventory.find({
          is_active: true,
          $expr: { $lte: ['$current_stock', '$reorder_point'] }
        })
        .populate('product_id', 'name category barcode safety_stock')
        .sort({ current_stock: 1 });

        lowStockItems.forEach(item => {
          alerts.push({
            type: 'low_stock',
            severity: item.current_stock === 0 ? 'critical' : 'warning',
            product_id: item.product_id._id,
            product_name: item.product_id.name,
            category: item.product_id.category,
            barcode: item.product_id.barcode,
            current_stock: item.current_stock,
            reorder_point: item.reorder_point,
            message: item.current_stock === 0 ? '库存为零' : '库存不足',
            created_at: item.updatedAt
          });
        });
      }

      if (type === 'all' || type === 'expiry') {
        // 临期商品预警
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

        const expiringProducts = await Product.find({
          is_active: true,
          expiry_date: {
            $lte: thirtyDaysFromNow,
            $gte: new Date()
          }
        }).sort({ expiry_date: 1 });

        for (let product of expiringProducts) {
          const inventory = await Inventory.findOne({ product_id: product._id });
          if (inventory && inventory.current_stock > 0) {
            const daysUntilExpiry = Math.ceil((product.expiry_date - new Date()) / (1000 * 60 * 60 * 24));

            alerts.push({
              type: 'expiry',
              severity: daysUntilExpiry <= 7 ? 'critical' : 'warning',
              product_id: product._id,
              product_name: product.name,
              category: product.category,
              barcode: product.barcode,
              current_stock: inventory.current_stock,
              expiry_date: product.expiry_date,
              days_until_expiry: daysUntilExpiry,
              message: `${daysUntilExpiry}天后过期`,
              created_at: new Date()
            });
          }
        }
      }

      // 按严重程度和时间排序
      alerts.sort((a, b) => {
        const severityOrder = { critical: 3, warning: 2, info: 1 };
        if (severityOrder[a.severity] !== severityOrder[b.severity]) {
          return severityOrder[b.severity] - severityOrder[a.severity];
        }
        return new Date(b.created_at) - new Date(a.created_at);
      });

      res.json({
        success: true,
        data: {
          alerts,
          summary: {
            total: alerts.length,
            critical: alerts.filter(a => a.severity === 'critical').length,
            warning: alerts.filter(a => a.severity === 'warning').length
          }
        }
      });

    } catch (error) {
      logger.error('获取库存预警错误:', error);

      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 计算库存统计
   */
  async calculateInventoryStats() {
    try {
      const [
        totalProducts,
        totalValue,
        lowStockCount,
        outOfStockCount
      ] = await Promise.all([
        Inventory.countDocuments({ is_active: true }),
        Inventory.aggregate([
          { $match: { is_active: true } },
          { $group: { _id: null, total: { $sum: '$total_value' } } }
        ]),
        Inventory.countDocuments({
          is_active: true,
          $expr: { $lte: ['$current_stock', '$reorder_point'] }
        }),
        Inventory.countDocuments({
          is_active: true,
          current_stock: 0
        })
      ]);

      return {
        total_products: totalProducts,
        total_value: totalValue[0]?.total || 0,
        low_stock_count: lowStockCount,
        out_of_stock_count: outOfStockCount
      };

    } catch (error) {
      logger.error('计算库存统计错误:', error);
      return {
        total_products: 0,
        total_value: 0,
        low_stock_count: 0,
        out_of_stock_count: 0
      };
    }
  }

  /**
   * 批量创建入库记录
   */
  async createBatchStockIn(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;
      const { stockInData } = req.body;

      if (!stockInData || !Array.isArray(stockInData)) {
        return res.status(400).json({
          success: false,
          message: '入库数据格式错误'
        });
      }

      logger.info('批量创建入库记录', {
        userId,
        batchSize: stockInData.length
      });

      const result = await stockInService.createBatchStockIn(stockInData, userId);

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'inventory.batch_stock_in',
        'inventory',
        'batch',
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: result.success ? 201 : 207,
          response_time_ms: responseTime,
          after_data: {
            batchSize: stockInData.length,
            successCount: result.data?.summary?.success || 0,
            failedCount: result.data?.summary?.failed || 0
          },
          description: `批量创建入库记录: ${result.data?.summary?.success || 0}/${stockInData.length} 成功`
        }
      );

      if (result.success) {
        res.status(201).json({
          success: true,
          data: result.data,
          message: '批量入库记录创建成功'
        });
      } else {
        res.status(result.data ? 207 : 400).json({
          success: false,
          data: result.data,
          message: result.error || '批量入库记录创建失败'
        });
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('批量创建入库记录失败', {
        userId: req.user?.userId,
        error: error.message,
        stack: error.stack
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'inventory.batch_stock_in',
          'inventory',
          'batch',
          {
            action: 'create',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '批量创建入库记录失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '批量创建入库记录失败',
        error: error.message
      });
    }
  }

  /**
   * 批量处理入库记录
   */
  async processBatchStockIn(req, res) {
    const startTime = Date.now();

    try {
      const userId = req.user.userId;
      const { recordIds } = req.body;

      if (!recordIds || !Array.isArray(recordIds)) {
        return res.status(400).json({
          success: false,
          message: '入库记录ID列表格式错误'
        });
      }

      logger.info('批量处理入库记录', {
        userId,
        recordCount: recordIds.length
      });

      const result = await stockInService.processBatchStockIn(recordIds, userId);

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'inventory.process_batch_stock_in',
        'inventory',
        'batch',
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: result.success ? 200 : 207,
          response_time_ms: responseTime,
          after_data: {
            recordCount: recordIds.length,
            successCount: result.data?.summary?.success || 0,
            failedCount: result.data?.summary?.failed || 0
          },
          description: `批量处理入库记录: ${result.data?.summary?.success || 0}/${recordIds.length} 成功`
        }
      );

      res.status(result.success ? 200 : 207).json({
        success: result.success,
        data: result.data,
        message: result.success ? '批量处理入库记录成功' : '部分入库记录处理失败'
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('批量处理入库记录失败', {
        userId: req.user?.userId,
        error: error.message
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'inventory.process_batch_stock_in',
          'inventory',
          'batch',
          {
            action: 'update',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '批量处理入库记录失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '批量处理入库记录失败',
        error: error.message
      });
    }
  }

  /**
   * 取消入库记录
   */
  async cancelStockIn(req, res) {
    const startTime = Date.now();

    try {
      const { id } = req.params;
      const { reason = '' } = req.body;
      const userId = req.user.userId;

      logger.info('取消入库记录', {
        recordId: id,
        userId,
        reason
      });

      const result = await stockInService.cancelStockIn(id, userId, reason);

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'inventory.cancel_stock_in',
        'inventory',
        id,
        {
          action: 'update',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: result.success ? 200 : 400,
          response_time_ms: responseTime,
          after_data: result.success ? {
            status: 'cancelled',
            reason
          } : null,
          error_message: result.success ? null : result.error,
          description: `取消入库记录: ${reason || '无原因'}`
        }
      );

      if (result.success) {
        res.json({
          success: true,
          data: result.data,
          message: '入库记录已取消'
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.error
        });
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;

      logger.error('取消入库记录失败', {
        recordId: req.params?.id,
        userId: req.user?.userId,
        error: error.message
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'inventory.cancel_stock_in',
          'inventory',
          req.params?.id,
          {
            action: 'update',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: '取消入库记录失败'
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '取消入库记录失败',
        error: error.message
      });
    }
  }

  /**
   * 获取入库统计信息
   */
  async getStockInStatistics(req, res) {
    try {
      const userId = req.user.userId;
      const filters = req.query;

      logger.info('获取入库统计信息', {
        userId,
        filters
      });

      const result = await stockInService.getStockInStatistics(filters);

      if (result.success) {
        res.json({
          success: true,
          data: result.data
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.error
        });
      }

    } catch (error) {
      logger.error('获取入库统计信息失败', {
        userId: req.user?.userId,
        error: error.message
      });

      res.status(500).json({
        success: false,
        message: '获取入库统计信息失败',
        error: error.message
      });
    }
  }
}

module.exports = new InventoryController();
