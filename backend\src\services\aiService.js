const axios = require('axios');
const logger = require('../utils/logger');

/**
 * AI服务类
 * 集成DeepSeek AI API，提供智能对话功能
 */
class AIService {
  constructor() {
    this.mockMode = process.env.AI_MOCK_MODE === 'true';
    this.apiKey = process.env.DEEPSEEK_API_KEY;
    this.baseURL = process.env.DEEPSEEK_BASE_URL || 'https://llm.chutes.ai/v1';
    this.model = process.env.DEEPSEEK_MODEL || 'deepseek-ai/DeepSeek-V3-0324';

    // 验证配置（除非是模拟模式）
    if (!this.mockMode && !this.apiKey) {
      logger.error('DEEPSEEK_API_KEY 环境变量未设置');
      throw new Error('AI服务配置错误：缺少API密钥');
    }

    // 创建axios实例（仅在非模拟模式下）
    if (!this.mockMode) {
      this.client = axios.create({
        baseURL: this.baseURL,
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30秒超时
      });
    }

    // 默认系统提示词
    this.defaultSystemPrompt = `你是一个专业的客服AI助手，为社区拼台（团购平台）提供服务。

你的职责包括：
1. 回答关于产品、配送、付款、取货等问题
2. 协助处理退货和售后问题
3. 解释平台政策和规则
4. 查询库存信息
5. 提供友好、专业的客户服务

回答要求：
- 使用简体中文
- 语气友好、专业
- 回答准确、简洁
- 如果不确定，请说明并建议联系人工客服
- 优先引用相关政策条款来支持回答`;

    logger.info('AI服务初始化完成', {
      mockMode: this.mockMode,
      baseURL: this.baseURL,
      model: this.model
    });
  }

  /**
   * 发送聊天请求到AI
   * @param {Array} messages - 消息历史
   * @param {Object} options - 配置选项
   * @returns {Promise<Object>} AI响应
   */
  async chat(messages, options = {}) {
    const startTime = Date.now();

    // 模拟模式
    if (this.mockMode) {
      return this.mockChatResponse(messages, options, startTime);
    }

    try {
      const {
        temperature = 0.7,
        max_tokens = 1000,
        stream = false,
        system_prompt = this.defaultSystemPrompt
      } = options;

      // 构建请求消息
      const requestMessages = [
        {
          role: 'system',
          content: system_prompt
        },
        ...messages
      ];

      const requestData = {
        model: this.model,
        messages: requestMessages,
        temperature,
        max_tokens,
        stream
      };

      logger.info('发送AI请求', {
        messageCount: requestMessages.length,
        temperature,
        max_tokens
      });

      const response = await this.client.post('/chat/completions', requestData);
      
      const responseTime = Date.now() - startTime;
      const aiResponse = response.data.choices[0].message;

      logger.info('AI响应成功', {
        responseTime,
        usage: response.data.usage
      });

      return {
        success: true,
        content: aiResponse.content,
        role: aiResponse.role,
        usage: response.data.usage,
        response_time_ms: responseTime,
        model: this.model
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('AI请求失败', {
        error: error.message,
        responseTime,
        status: error.response?.status,
        statusText: error.response?.statusText
      });

      // 处理不同类型的错误
      if (error.response) {
        const status = error.response.status;
        let errorMessage = 'AI服务暂时不可用';

        if (status === 401) {
          errorMessage = 'AI服务认证失败';
        } else if (status === 429) {
          errorMessage = 'AI服务请求过于频繁，请稍后再试';
        } else if (status >= 500) {
          errorMessage = 'AI服务器内部错误';
        }

        return {
          success: false,
          error: errorMessage,
          error_code: `AI_ERROR_${status}`,
          response_time_ms: responseTime
        };
      }

      return {
        success: false,
        error: '网络连接错误，请检查网络设置',
        error_code: 'NETWORK_ERROR',
        response_time_ms: responseTime
      };
    }
  }

  /**
   * 流式聊天（用于实时响应）
   * @param {Array} messages - 消息历史
   * @param {Function} onChunk - 接收数据块的回调
   * @param {Object} options - 配置选项
   * @returns {Promise<Object>} 完整响应
   */
  async streamChat(messages, onChunk, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        temperature = 0.7,
        max_tokens = 1000,
        system_prompt = this.defaultSystemPrompt
      } = options;

      const requestMessages = [
        {
          role: 'system',
          content: system_prompt
        },
        ...messages
      ];

      const requestData = {
        model: this.model,
        messages: requestMessages,
        temperature,
        max_tokens,
        stream: true
      };

      const response = await this.client.post('/chat/completions', requestData, {
        responseType: 'stream'
      });

      let fullContent = '';
      let usage = null;

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              
              if (data === '[DONE]') {
                const responseTime = Date.now() - startTime;
                resolve({
                  success: true,
                  content: fullContent,
                  usage,
                  response_time_ms: responseTime,
                  model: this.model
                });
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const delta = parsed.choices[0]?.delta;
                
                if (delta?.content) {
                  fullContent += delta.content;
                  onChunk(delta.content);
                }

                if (parsed.usage) {
                  usage = parsed.usage;
                }
              } catch (parseError) {
                // 忽略解析错误，继续处理下一行
              }
            }
          }
        });

        response.data.on('error', (error) => {
          logger.error('流式AI请求错误', error);
          reject(error);
        });
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('流式AI请求失败', {
        error: error.message,
        responseTime
      });

      return {
        success: false,
        error: '流式AI服务暂时不可用',
        error_code: 'STREAM_ERROR',
        response_time_ms: responseTime
      };
    }
  }

  /**
   * 分析用户意图
   * @param {string} message - 用户消息
   * @param {Object} context - 上下文信息
   * @returns {Promise<Object>} 意图分析结果
   */
  async analyzeIntent(message, context = {}) {
    try {
      // 模拟模式
      if (this.mockMode) {
        return this.mockAnalyzeIntent(message);
      }
      const intentPrompt = `请分析以下用户消息的意图，并返回JSON格式的结果：

用户消息："${message}"

请返回以下格式的JSON：
{
  "intent": "意图类型",
  "confidence": 0.8,
  "entities": [
    {
      "type": "实体类型",
      "value": "实体值",
      "confidence": 0.9
    }
  ],
  "suggested_action": "建议的处理动作"
}

意图类型包括：
- greeting: 问候
- inventory_query: 库存查询
- product_info: 产品信息
- delivery_info: 配送信息
- payment_info: 付款信息
- pickup_info: 取货信息
- return_request: 退货申请
- complaint: 投诉
- policy_question: 政策问题
- general_question: 一般问题

只返回JSON，不要其他内容。`;

      const response = await this.chat([
        { role: 'user', content: intentPrompt }
      ], {
        temperature: 0.3,
        max_tokens: 500
      });

      if (response.success) {
        try {
          const intentResult = JSON.parse(response.content);
          return {
            success: true,
            ...intentResult
          };
        } catch (parseError) {
          logger.error('意图分析结果解析失败', parseError);
          return {
            success: false,
            intent: 'general_question',
            confidence: 0.5,
            entities: [],
            suggested_action: 'general_response'
          };
        }
      }

      return {
        success: false,
        intent: 'general_question',
        confidence: 0.1,
        entities: [],
        suggested_action: 'general_response'
      };

    } catch (error) {
      logger.error('意图分析失败', error);
      return {
        success: false,
        intent: 'general_question',
        confidence: 0.1,
        entities: [],
        suggested_action: 'general_response'
      };
    }
  }

  /**
   * 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    try {
      const response = await this.chat([
        { role: 'user', content: '你好' }
      ], {
        max_tokens: 50
      });

      return {
        success: response.success,
        status: response.success ? 'healthy' : 'unhealthy',
        response_time_ms: response.response_time_ms,
        model: this.model
      };

    } catch (error) {
      return {
        success: false,
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * 模拟聊天响应
   */
  mockChatResponse(messages, options, startTime) {
    const userMessage = messages[messages.length - 1]?.content || '';
    const responseTime = Date.now() - startTime;

    // 根据用户消息生成模拟响应
    let content = '';

    if (userMessage.includes('你好') || userMessage.includes('hello')) {
      content = '您好！我是智能客服助手，可以帮您查询库存信息和处理退货申请。请问有什么可以帮助您的吗？';
    } else if (userMessage.includes('配送') || userMessage.includes('delivery')) {
      content = '我们的配送政策如下：\n- 配送时间：每周三截单，周五送货\n- 起送标准：三只鸡或同等金额起可送到家\n- 配送范围：以波士顿为中心，Quincy、Waltham、Newton以内\n- 配送费用：上述区域内运费$5/次\n- 免费配送：10只鸡或同等金额以上免费送到家';
    } else if (userMessage.includes('退货') || userMessage.includes('return')) {
      content = '关于退货政策：\n- 质量问题请在24小时内通过照片私信反馈\n- 超时反馈恕不受理\n- 质量问题经核实后可选择退款或更换\n- 退款可作为下次拼单的credit，也可直接退款';
    } else if (userMessage.includes('库存') || userMessage.includes('inventory')) {
      content = '我可以帮您查询库存信息。请告诉我您想查询哪个产品的库存？我们有新鲜果蔬、走地鸡禽和优质干货等产品。';
    } else if (userMessage.includes('测试')) {
      content = '测试成功！AI服务模拟模式运行正常。';
    } else {
      content = '感谢您的咨询！我是社区拼台的智能客服助手。我可以帮您了解我们的产品、配送政策、退货流程等信息。请问您需要什么帮助？';
    }

    return {
      success: true,
      content,
      role: 'assistant',
      usage: {
        prompt_tokens: userMessage.length,
        completion_tokens: content.length,
        total_tokens: userMessage.length + content.length
      },
      response_time_ms: responseTime,
      model: this.model + ' (模拟模式)'
    };
  }

  /**
   * 模拟意图分析
   */
  mockAnalyzeIntent(message) {
    const msg = message.toLowerCase();
    let intent = 'general_question';
    let confidence = 0.8;
    let entities = [];
    let suggested_action = 'general_response';

    if (msg.includes('你好') || msg.includes('hello')) {
      intent = 'greeting';
      confidence = 0.9;
      suggested_action = 'greeting_response';
    } else if (msg.includes('配送') || msg.includes('delivery')) {
      intent = 'delivery_info';
      confidence = 0.9;
      suggested_action = 'provide_delivery_info';
    } else if (msg.includes('退货') || msg.includes('return')) {
      intent = 'return_request';
      confidence = 0.9;
      suggested_action = 'handle_return_request';
    } else if (msg.includes('库存') || msg.includes('inventory')) {
      intent = 'inventory_query';
      confidence = 0.9;
      suggested_action = 'search_inventory';
    } else if (msg.includes('政策') || msg.includes('policy')) {
      intent = 'policy_question';
      confidence = 0.9;
      suggested_action = 'explain_policy';
    }

    return {
      success: true,
      intent,
      confidence,
      entities,
      suggested_action
    };
  }
}

module.exports = new AIService();
