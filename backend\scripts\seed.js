const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// 导入模型
const { User, Product, Inventory } = require('../src/models');
const logger = require('../src/utils/logger');

/**
 * 数据库种子数据脚本
 * 用于初始化系统基础数据
 */

// 用户种子数据
const seedUsers = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    name: '系统管理员',
    role: 'admin',
    phone: '13800138000',
    isActive: true
  },
  {
    username: 'manager',
    email: '<EMAIL>',
    password: 'manager123',
    name: '仓库经理',
    role: 'manager',
    phone: '13800138001',
    isActive: true
  },
  {
    username: 'staff1',
    email: '<EMAIL>',
    password: 'staff123',
    name: '仓库员工1',
    role: 'staff',
    phone: '13800138002',
    isActive: true
  },
  {
    username: 'staff2',
    email: '<EMAIL>',
    password: 'staff123',
    name: '仓库员工2',
    role: 'staff',
    phone: '13800138003',
    isActive: true
  }
];

// 产品种子数据
const seedProducts = [
  {
    name: '走地鸡',
    category: 'poultry',
    size: '约3-4lb',
    description: '新鲜走地鸡，肉质鲜美，营养丰富',
    barcode: 'A001',
    unit_price: 25.99,
    supplier: '本地农场',
    safety_stock: 10,
    storage_location: 'A-01-01',
    expiry_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
    is_active: true
  },
  {
    name: '日本贝贝南瓜',
    category: 'produce',
    size: '1-2lb',
    description: '日本进口贝贝南瓜，香甜可口，口感绵密',
    barcode: 'A002',
    unit_price: 8.99,
    supplier: '进口商',
    safety_stock: 20,
    storage_location: 'B-02-03',
    expiry_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14天后过期
    is_active: true
  },
  {
    name: '有机胡萝卜',
    category: 'produce',
    size: '1lb装',
    description: '有机种植胡萝卜，无农药残留',
    barcode: 'A003',
    unit_price: 4.99,
    supplier: '有机农场',
    safety_stock: 30,
    storage_location: 'B-01-02',
    expiry_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10天后过期
    is_active: true
  },
  {
    name: '新鲜三文鱼',
    category: 'frozen',
    size: '500g',
    description: '挪威进口三文鱼，新鲜冷冻',
    barcode: 'A004',
    unit_price: 35.99,
    supplier: '海鲜供应商',
    safety_stock: 15,
    storage_location: 'C-01-01',
    expiry_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
    is_active: true
  },
  {
    name: '有机大米',
    category: 'dry_goods',
    size: '5kg装',
    description: '东北有机大米，粒粒饱满',
    barcode: 'A005',
    unit_price: 28.99,
    supplier: '东北农场',
    safety_stock: 25,
    storage_location: 'D-01-01',
    expiry_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后过期
    is_active: true
  },
  {
    name: '新鲜牛奶',
    category: 'dairy',
    size: '1L装',
    description: '本地牧场新鲜牛奶，营养丰富',
    barcode: 'A006',
    unit_price: 6.99,
    supplier: '本地牧场',
    safety_stock: 50,
    storage_location: 'E-01-01',
    expiry_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5天后过期
    is_active: true
  },
  {
    name: '有机鸡蛋',
    category: 'dairy',
    size: '12个装',
    description: '有机散养鸡蛋，营养价值高',
    barcode: 'A007',
    unit_price: 12.99,
    supplier: '有机农场',
    safety_stock: 40,
    storage_location: 'E-02-01',
    expiry_date: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000), // 21天后过期
    is_active: true
  },
  {
    name: '进口橄榄油',
    category: 'other',
    size: '500ml',
    description: '意大利进口特级初榨橄榄油',
    barcode: 'A008',
    unit_price: 45.99,
    supplier: '进口商',
    safety_stock: 20,
    storage_location: 'F-01-01',
    expiry_date: new Date(Date.now() + 730 * 24 * 60 * 60 * 1000), // 2年后过期
    is_active: true
  }
];

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    logger.info('数据库连接成功');
  } catch (error) {
    logger.error('数据库连接失败:', error);
    process.exit(1);
  }
}

/**
 * 清空现有数据
 */
async function clearExistingData() {
  try {
    await User.deleteMany({});
    await Product.deleteMany({});
    await Inventory.deleteMany({});
    logger.info('现有数据清空完成');
  } catch (error) {
    logger.error('清空数据失败:', error);
    throw error;
  }
}

/**
 * 创建用户数据
 */
async function createUsers() {
  try {
    const users = [];
    
    for (const userData of seedUsers) {
      const user = new User(userData);
      user.setDefaultPermissions();
      await user.save();
      users.push(user);
    }
    
    logger.info(`创建了 ${users.length} 个用户`);
    return users;
  } catch (error) {
    logger.error('创建用户失败:', error);
    throw error;
  }
}

/**
 * 创建产品数据
 */
async function createProducts(adminUser) {
  try {
    const products = [];
    
    for (const productData of seedProducts) {
      const product = new Product({
        ...productData,
        created_by: adminUser._id,
        updated_by: adminUser._id
      });
      
      await product.save();
      products.push(product);
    }
    
    logger.info(`创建了 ${products.length} 个产品`);
    return products;
  } catch (error) {
    logger.error('创建产品失败:', error);
    throw error;
  }
}

/**
 * 创建库存数据
 */
async function createInventory(products, adminUser) {
  try {
    const inventoryRecords = [];
    
    for (const product of products) {
      // 生成随机库存数量
      const currentStock = Math.floor(Math.random() * 100) + 20; // 20-120之间
      const costPerUnit = product.unit_price * 0.7; // 成本为售价的70%
      
      const inventory = new Inventory({
        product_id: product._id,
        current_stock: currentStock,
        reserved_stock: 0,
        available_stock: currentStock,
        location: product.storage_location,
        reorder_point: product.safety_stock,
        cost_per_unit: costPerUnit,
        total_value: currentStock * costPerUnit,
        last_stock_in: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // 最近7天内的随机时间
        updated_by: adminUser._id
      });
      
      await inventory.save();
      inventoryRecords.push(inventory);
    }
    
    logger.info(`创建了 ${inventoryRecords.length} 条库存记录`);
    return inventoryRecords;
  } catch (error) {
    logger.error('创建库存失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function seedDatabase() {
  try {
    logger.info('开始初始化数据库种子数据...');
    
    // 连接数据库
    await connectDatabase();
    
    // 清空现有数据
    await clearExistingData();
    
    // 创建用户
    const users = await createUsers();
    const adminUser = users.find(user => user.role === 'admin');
    
    // 创建产品
    const products = await createProducts(adminUser);
    
    // 创建库存
    await createInventory(products, adminUser);
    
    logger.info('数据库种子数据初始化完成！');
    logger.info('默认用户账号:');
    logger.info('管理员: admin / admin123');
    logger.info('经理: manager / manager123');
    logger.info('员工: staff1 / staff123, staff2 / staff123');
    
  } catch (error) {
    logger.error('种子数据初始化失败:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    logger.info('数据库连接已关闭');
    process.exit(0);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedDatabase();
}

module.exports = {
  seedDatabase,
  connectDatabase,
  clearExistingData,
  createUsers,
  createProducts,
  createInventory
};
