# 第四阶段数据库模型文档

本文档详细描述了第四阶段新增和增强的数据库模型，包括StockCount模型和Report模型的增强功能。

## 📋 StockCount 盘点模型

### 模型概述
StockCount模型用于管理库存盘点任务，支持全盘、部分盘点、循环盘点和抽查盘点等多种盘点类型。

### 字段定义

#### 基础信息
```javascript
{
  count_id: {
    type: String,
    unique: true,
    index: true,
    // 自动生成格式: FC/PC/CC/SC + YYMMDD + 6位时间戳
    // 例: PC25061512345 (部分盘点-2025年6月15日)
  },
  title: {
    type: String,
    required: true,
    maxlength: 200,
    // 盘点任务标题
  },
  description: {
    type: String,
    maxlength: 1000,
    // 盘点任务描述
  },
  count_type: {
    type: String,
    enum: ['full', 'partial', 'cycle', 'spot'],
    required: true,
    index: true,
    // full: 全盘, partial: 部分盘点, cycle: 循环盘点, spot: 抽查盘点
  }
}
```

#### 状态管理
```javascript
{
  status: {
    type: String,
    enum: ['planned', 'in_progress', 'completed', 'cancelled', 'reviewing'],
    default: 'planned',
    index: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  }
}
```

#### 时间管理
```javascript
{
  scheduled_date: {
    type: Date,
    required: true,
    index: true,
    // 计划开始日期
  },
  start_date: {
    type: Date,
    index: true,
    // 实际开始日期
  },
  end_date: {
    type: Date,
    index: true,
    // 实际结束日期
  },
  deadline: {
    type: Date,
    index: true,
    // 截止日期
  }
}
```

#### 盘点项目
```javascript
{
  items: [{
    product_id: {
      type: ObjectId,
      ref: 'Product',
      required: true
    },
    expected_quantity: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
      // 预期数量
    },
    actual_quantity: {
      type: Number,
      min: 0,
      default: null,
      // 实际盘点数量
    },
    difference: {
      type: Number,
      default: null,
      // 差异数量 = actual_quantity - expected_quantity
    },
    difference_percentage: {
      type: Number,
      default: null,
      // 差异百分比
    },
    status: {
      type: String,
      enum: ['pending', 'counted', 'verified', 'adjusted'],
      default: 'pending'
    },
    notes: {
      type: String,
      maxlength: 500,
      // 备注信息
    },
    counted_by: {
      type: ObjectId,
      ref: 'User',
      // 盘点人员
    },
    counted_at: {
      type: Date,
      // 盘点时间
    },
    verified_by: {
      type: ObjectId,
      ref: 'User',
      // 验证人员
    },
    verified_at: {
      type: Date,
      // 验证时间
    }
  }]
}
```

#### 任务分配
```javascript
{
  assigned_to: [{
    user_id: {
      type: ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['counter', 'verifier', 'supervisor'],
      default: 'counter'
    },
    assigned_at: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ['assigned', 'accepted', 'in_progress', 'completed'],
      default: 'assigned'
    }
  }]
}
```

#### 统计摘要
```javascript
{
  summary: {
    total_items: {
      type: Number,
      default: 0,
      // 总项目数
    },
    counted_items: {
      type: Number,
      default: 0,
      // 已盘点项目数
    },
    verified_items: {
      type: Number,
      default: 0,
      // 已验证项目数
    },
    discrepancy_items: {
      type: Number,
      default: 0,
      // 有差异项目数
    },
    total_expected_value: {
      type: Number,
      default: 0,
      // 预期总价值
    },
    total_actual_value: {
      type: Number,
      default: 0,
      // 实际总价值
    },
    value_difference: {
      type: Number,
      default: 0,
      // 价值差异
    },
    accuracy_percentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
      // 准确率百分比
    }
  }
}
```

#### 设置选项
```javascript
{
  settings: {
    allow_negative_count: {
      type: Boolean,
      default: false,
      // 是否允许负数盘点
    },
    require_verification: {
      type: Boolean,
      default: true,
      // 是否需要验证
    },
    auto_adjust_inventory: {
      type: Boolean,
      default: false,
      // 是否自动调整库存
    },
    tolerance_percentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 5,
      // 容差百分比
    }
  }
}
```

#### 调整记录
```javascript
{
  adjustments: [{
    item_index: {
      type: Number,
      required: true,
      // 调整项目索引
    },
    adjustment_type: {
      type: String,
      enum: ['increase', 'decrease', 'correction'],
      required: true
    },
    quantity_change: {
      type: Number,
      required: true,
      // 数量变化
    },
    reason: {
      type: String,
      required: true,
      // 调整原因
    },
    adjusted_by: {
      type: ObjectId,
      ref: 'User',
      required: true
    },
    adjusted_at: {
      type: Date,
      default: Date.now
    },
    approved: {
      type: Boolean,
      default: false,
      // 是否已审批
    }
  }]
}
```

### 实例方法

#### 盘点管理方法
```javascript
// 添加盘点项目
addCountItem(productId, expectedQuantity, notes)

// 更新盘点项目
updateCountItem(itemIndex, actualQuantity, countedBy, notes)

// 验证盘点项目
verifyCountItem(itemIndex, verifiedBy, approved)

// 开始盘点
startCount()

// 完成盘点
completeCount()

// 取消盘点
cancelCount()
```

#### 用户管理方法
```javascript
// 分配用户
assignUser(userId, role)

// 移除用户分配
unassignUser(userId)
```

#### 状态查询方法
```javascript
// 检查是否过期
isOverdue()

// 获取进度百分比
getProgress()

// 更新摘要统计
updateSummary()
```

### 静态方法
```javascript
// 生成盘点ID
StockCount.generateCountId(countType)
```

### 索引设计
```javascript
// 主要索引
{ count_id: 1 }                    // 唯一索引
{ count_type: 1, status: 1 }       // 复合索引
{ scheduled_date: 1 }              // 时间索引
{ created_by: 1, createdAt: -1 }   // 创建者索引
{ 'assigned_to.user_id': 1 }       // 分配用户索引
{ location: 1 }                    // 位置索引
{ categories: 1 }                  // 类别索引
```

## 📊 Report 模型增强

### 新增字段

#### 定时任务支持
```javascript
{
  schedule: {
    is_scheduled: {
      type: Boolean,
      default: false,
      // 是否为定时报表
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      // 生成频率
    },
    next_run: {
      type: Date,
      // 下次运行时间
    },
    last_run: {
      type: Date,
      // 上次运行时间
    },
    enabled: {
      type: Boolean,
      default: true,
      // 是否启用定时任务
    }
  }
}
```

#### 增强的数据章节
```javascript
{
  data_sections: [{
    section: {
      type: String,
      required: true,
      // 章节标识: summary, inventory, stock_movement, returns, alerts, performance, trends
    },
    title: {
      type: String,
      required: true,
      // 章节标题
    },
    data: {
      type: Object,
      required: true,
      // 章节数据（JSON格式）
    },
    charts: [{
      chart_type: {
        type: String,
        enum: ['metric', 'pie', 'bar', 'line', 'table'],
        required: true
      },
      title: {
        type: String,
        required: true
      },
      data: {
        type: Object,
        required: true,
        // 图表数据（JSON格式）
      }
    }]
  }]
}
```

#### 增强的摘要数据
```javascript
{
  summary: {
    total_products: {
      type: Number,
      default: 0
    },
    total_stock_value: {
      type: Number,
      default: 0
    },
    stock_in_count: {
      type: Number,
      default: 0
    },
    return_requests: {
      type: Number,
      default: 0
    },
    low_stock_alerts: {
      type: Number,
      default: 0
    },
    expiry_alerts: {
      type: Number,
      default: 0
    },
    // 新增字段
    chat_sessions: {
      type: Number,
      default: 0
    },
    ai_responses: {
      type: Number,
      default: 0
    },
    policy_violations: {
      type: Number,
      default: 0
    }
  }
}
```

### 新增实例方法

#### 定时任务管理
```javascript
// 设置定时任务
setSchedule(frequency, nextRun)

// 计算下次运行时间
calculateNextRun()

// 禁用定时任务
disableSchedule()
```

#### 数据章节管理
```javascript
// 添加数据章节
addDataSection(section, title, data, charts)

// 更新数据章节
updateDataSection(section, data, charts)

// 移除数据章节
removeDataSection(section)
```

#### 报表状态管理
```javascript
// 标记为生成中
markGenerating()

// 标记为完成
markCompleted(generationTime)

// 标记为失败
markFailed(error)
```

## 🔗 数据库关系图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     User        │    │    Product      │    │   Inventory     │
│                 │    │                 │    │                 │
│ - _id           │    │ - _id           │    │ - _id           │
│ - username      │    │ - name          │    │ - product_id    │
│ - role          │    │ - category      │    │ - current_stock │
│ - permissions   │    │ - unit_price    │    │ - reorder_point │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   StockCount    │    │     Report      │    │ OperationLog    │
│                 │    │                 │    │                 │
│ - count_id      │    │ - report_id     │    │ - user_id       │
│ - title         │    │ - report_type   │    │ - action        │
│ - count_type    │    │ - title         │    │ - resource_type │
│ - status        │    │ - status        │    │ - resource_id   │
│ - items[]       │    │ - data_sections │    │ - details       │
│ - assigned_to[] │    │ - summary       │    │ - timestamp     │
│ - created_by    │    │ - schedule      │    └─────────────────┘
│ - summary       │    │ - generated_by  │
└─────────────────┘    └─────────────────┘
```

## 📈 数据流向

### 盘点数据流
```
创建盘点计划 → 分配任务 → 执行盘点 → 提交结果 → 差异分析 → 库存调整
     ↓              ↓           ↓           ↓           ↓           ↓
  StockCount    assigned_to   items[]   actual_qty  adjustments  Inventory
```

### 报表数据流
```
定时触发/手动生成 → 数据收集 → 统计分析 → 图表生成 → 报表保存
        ↓              ↓           ↓           ↓           ↓
   schedulerService  各种模型    reportService  charts[]   Report
```

## 🔧 维护建议

### 索引优化
1. **StockCount**: 根据查询频率优化复合索引
2. **Report**: 考虑按时间分区存储大量历史报表
3. **定期清理**: 设置TTL索引自动清理过期数据

### 性能优化
1. **分页查询**: 使用游标分页处理大量数据
2. **数据聚合**: 使用MongoDB聚合管道优化复杂查询
3. **缓存策略**: 对频繁访问的报表数据进行缓存

### 数据一致性
1. **事务支持**: 关键操作使用MongoDB事务
2. **数据校验**: 在应用层和数据库层都进行数据校验
3. **备份策略**: 定期备份重要的盘点和报表数据
