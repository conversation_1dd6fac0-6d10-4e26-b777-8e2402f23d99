# 仓库库存管理及 AI 客服系统 PRD（V1.1‑增强版）

> **编写人**：ChatGPT（依据用户需求）\
> **最近更新**：2025‑06‑14\
> **文档状态**：Draft ▶️ Ready for Review

---

## 目录

1. [项目背景与目标](#项目背景与目标)
2. [成功指标 (KPIs)](#成功指标-kpis)
3. [范围界定](#范围界定)
4. [用户画像与核心痛点](#用户画像与核心痛点)
5. [关键假设与依赖](#关键假设与依赖)
6. [功能需求 & 验收标准](#功能需求--验收标准)
7. [非功能需求](#非功能需求)
8. [系统架构](#系统架构)
9. [数据库设计](#数据库设计)
10. [迭代里程碑 & 时间线](#迭代里程碑--时间线)
11. [风险与缓解措施](#风险与缓解措施)
12. [监控与运维](#监控与运维)
13. [低保真线框图说明](#低保真线框图说明)
14. [附录](#附录)

---

## 项目背景与目标

- 电子商务与团购业务增长导致公司面临 **库存错漏、人工对账耗时、客服负荷大** 等问题。
- 目标：构建一体化 **网页端库存管理 + AI 客服** 平台，提升库存准确率、客户体验与运营效率。

## 成功指标 (KPIs)

| 指标                | 现状    | 目标 (6 个月) |
| ----------------- | ----- | --------- |
| 库存准确率             | \~90% | **≥ 99%** |
| 客服平均首次响应时间        | >30s  | **≤ 2 s** |
| 人工客服介入率           | 70%   | **≤ 20%** |
| 客户满意度 (CSAT, 1‑5) | 3.8   | **≥ 4.5** |
| 退货处理周期 (提交→退款)    | 7 天   | **≤ 3 天** |

## 范围界定

### In‑Scope

- **入库管理**、库存浏览、周报、盘点、预警
- **退货/投诉流程** 与 AI 智能客服对接
- **后台管理 UI**（桌面&移动浏览器）
- **AI 聊天窗口** 嵌入官网

### Out‑of‑Scope

- 前台 **电商销售功能**（购物车、支付）
- **物流配送系统** (TMS) 深度集成
- 原生 **iOS / Android App**

## 用户画像与核心痛点

| 角色         | 核心痛点              | 价值主张                 |
| ---------- | ----------------- | -------------------- |
| 仓库管理员      | 手动登记 & 盘点耗时，数据易出错 | 一键扫码入库 + 自动周报 + 盘点任务 |
| 客服专员       | 重复回答库存/退货问题，信息分散  | AI 自动答复 + 统一后台查看     |
| 终端客户 (65+) | 查询库存无门，退货流程复杂     | 聊天窗口一句话即可获得帮助        |

## 关键假设与依赖

1. LLM API (DeepSeek / Gemini) 稳定提供 < 2 s 延迟的接口。
2. 仓库配备可扫码设备或支持手机摄像头扫码。
3. 公司允许使用 Venmo 作为退款渠道并符合财务合规。
4. 图片文件存储使用 AWS S3 (或等价对象存储) 并授权访问。
5. IT 部门能配置 SSL 证书与 HTTPS 环境。

## 功能需求 & 验收标准

> **DoD = Definition of Done**

### 1. 产品入库管理

| 功能点  | 说明                             | DoD                      |
| ---- | ------------------------------ | ------------------------ |
| 条码录入 | 支持扫码枪 & 手机摄像头；无条码时系统自动生成并可下载标签 | 扫码成功录入时间 <5 s，重码提示率 100% |
| 信息字段 | 名称/照片/尺寸/数量/类别/库位/保质期/描述       | 任一必填项缺失前端阻止提交            |
| 入库单  | 系统生成入库单编号，可导出 PDF 打印           | 导出后条码可扫码打开该产品详情          |

### 2. 库存管理

| 功能点  | 说明                     | DoD               |
| ---- | ---------------------- | ----------------- |
| 库存总览 | 表格支持搜索、筛选、排序           | 1k SKU 加载 <1 s    |
| 周报   | 每周一 08:00 自动生成并邮件给仓库经理 | 周报涵盖进/出库、差异、预警    |
| 库存预警 | 低于安全库存或临期 30 天内        | 预警通知实时推送 & 周报摘要   |
| 盘点任务 | 支持生成任务 → 录入差异 → 自动调整   | 盘点差异 = 系统库存 - 实盘值 |

### 3. AI 客服模块

| 功能点  | 说明                | DoD                  |
| ---- | ----------------- | -------------------- |
| 智能问答 | 基于 RAG 检索库存 & FAQ | Top‑1 命中率 ≥90%（回归测试） |
| 退货判断 | 根据政策 YAML 规则自动匹配  | 误判率 ≤5%              |
| 退货表单 | 动态卡片式多字段输入，含图片上传  | 表单完整度校验 & 后端存储成功     |
| 投诉记录 | 对话中匹配情绪触发记录       | 记录字段完整性 100%         |

> *更多功能点请参见附录 A — 完整需求矩阵。*

## 非功能需求

| 类别   | 指标/目标                               |
| ---- | ----------------------------------- |
| 性能   | 100 并发客服会话 P95 延迟 ≤2 s              |
| 稳定性  | 7×24 可用，月故障时间 ≤1 h                  |
| 安全   | HTTPS；敏感字段 AES‑256 加密；OAuth2/JWT 认证 |
| 可扩展性 | 横向扩容至 10k SKU & 1k 并发对话无需停机         |
| 隐私合规 | 满足 GDPR/CCPA 就绪；日志保留 90 天可审计        |
| 备份策略 | DB 每日快照，S3 跨区复制；RPO ≤15 min         |

## 系统架构

> **技术栈：MERN + Socket.io + AWS S3**

```mermaid
flowchart LR
    subgraph Client
        A1[后台管理端 React] -- REST --> B((API Gateway))
        A2[AI 客服窗口 React] -- WS / REST --> B
    end
    subgraph Server
        B --> C[Node.js + Express 服务层]
        C --> D[Mongoose ORM]
        D --> E[(MongoDB Cluster)]
        C -- HTTP --> F[LLM API (DeepSeek / Gemini)]
        C -- S3 SDK --> G[(对象存储)]
        C -- Cron --> H[报表 & 计划任务]
    end
```

## 数据库设计

> 详见原 V1.0 表结构，新增日志表 `OperationLogs` 记录所有 API 调用与异常。

## 迭代里程碑 & 时间线

| 周期           | 交付物              | 描述                     |
| ------------ | ---------------- | ---------------------- |
| **Week 0–1** | 项目 Kick‑off & 原型 | 确认需求、线框图、技术选型          |
| **Week 2–3** | PoC & 基础数据模型     | 完成扫码入库 MVP、LLM 问答 Demo |
| **Week 4–6** | MVP Alpha        | 入库+库存总览+AI 问答闭环，内部测试   |
| **Week 7**   | Beta             | 退货流程、盘点、报表 & 权限模块      |
| **Week 8**   | GA 发布            | 上线生产、监控 & 培训           |

## 风险与缓解措施

| 风险          | 影响         | 缓解措施            |
| ----------- | ---------- | --------------- |
| LLM 费用 & 延迟 | 成本超支 / 体验差 | 缓存回答、限制上下文、批量结算 |
| 条码设备兼容性     | 扫码失败       | 提供摄像头扫码备选       |
| 数据丢失/泄露     | 法务 & 商誉风险  | 多活备份、加密、权限最小化   |
| 老年客户数字鸿沟    | 退货申请流失     | AI 简化语言，提供语音引导  |

## 监控与运维

- **APM**：Elastic APM 监控 API 延迟与错误率
- **日志采集**：Node + Winston → Elasticsearch  30 天滚动
- **告警**：Prometheus + Grafana，指标阈值推送 Slack/Email
- **CI/CD**：GitHub Actions → Docker → AWS ECS 蓝绿发布

## 低保真线框图说明

（已包含各关键页面线框，略）

---

## UI 视觉风格指南（Designer Handoff）

> **目标**：确保视觉统一、组件可复用、易于实现响应式，且对 65+ 用户友好。

### 1. Design Tokens

| Token             | 值                                      | 说明               |
| ----------------- | -------------------------------------- | ---------------- |
| **Primary/Brand** | `#2C7AFF`                              | 主按钮、链接、图标强调色     |
| **Primary‑Hover** | `#1E5EFF`                              | Brand 悬停色 (深 8%) |
| **Accent**        | `#FFAA2C`                              | 次级强调（警示、Badge）   |
| **Success**       | `#22C55E`                              | 成功状态 (盘点通过)      |
| **Error**         | `#EF4444`                              | 错误/删除警告          |
| **Grey‑0**        | `#FFFFFF`                              | 纯白背景             |
| **Grey‑50**       | `#F7F9FC`                              | 主要页面背景（弱分区）      |
| **Grey‑100**      | `#EDF1F7`                              | 卡片底色             |
| **Grey‑500**      | `#667085`                              | 正文色 (#333 替代黑)   |
| **Shadow‑Card**   | `0 2px 8px rgba(0,0,0,0.05)`           | 卡片柔影             |
| **Radius‑XS**     | `4px`                                  |                  |
| **Radius‑MD**     | `12px`                                 |                  |
| **Font‑Heading**  | `Inter, "思源黑体", sans‑serif`            |                  |
| **Font‑Body**     | Same as heading                        |                  |
| **Breakpoints**   | `SM: 640, MD: 960, LG: 1280, XL: 1536` |                  |

### 2. 版式与层级

- **顶栏**：高度 56px，Brand 蓝底/白字或反色，放左 Logo、中间空、右用户头像下拉。
- **侧边导航**：宽 220px，深色文字，当前页加 Brand 左侧 3px 竖条。
- **卡片/表格**：卡片圆角 MD、阴影 Shadow‑Card；表格行高 48px；Header Bold 600。
- **抽屉/Modal**：从右侧 30%‑40% 宽度滑入；背景 Grey‑0；底部固定操作栏。

### 3. 组件库选型 & 变体

| 组件               | 基于 Ant Design 变体            | 关键定制点                |
| ---------------- | --------------------------- | -------------------- |
| Button (Primary) | `@btn-primary-bg: #2C7AFF`  | 大号按钮高度 40px，字重 600   |
| Input / Select   | 角 4px，聚焦边框 Brand 蓝          | 支持 Clear & Search 图标 |
| Table            | Zebra 行色 Grey‑50            | 分页器放右侧               |
| Drawer / Modal   | 圆角 12px，阴影 12px             | Header 固定 Close 图标   |
| Tag / Badge      | 使用 Accent / Success / Error | 字号 12px              |
| Toast / Message  | 3 秒自动消失，允许无障碍朗读             | 支持自动聚焦               |

### 4. 无障碍 & 大字体

- 所有文字对比度遵守 WCAG AA：**比例 ≥4.5**。
- 可切换 **“大字模式”**：字体 +20%，按钮高度随之增大。
- 键盘导航：TabIndex 合理，焦点轮廓 Brand 蓝 2px 实线。

### 5. 动效与微交互

- 按钮点击：100ms Scale‑down 0.96 → 回弹。
- AI 聊天打字机：每字符 30ms，首 400ms Skeleton Bubble。（提升真实感）
- 抽屉打开：300ms ease‑out 滑入；关闭：200ms ease‑in 滑出。

### 6. Dark Mode（可选后续迭代）

- Token 替换：Primary 不变，背景 Grey‑0 → `#1B1D23`，文本 Grey‑500 → `#E5E7EB`。
- 自动跟随系统 `prefers‑color‑scheme`，用户侧边菜单可手动切换。

### 7. Iconography

- 采用 **Lucide‑React**：尺寸 20px，Stroke 1.5px，圆角。
- 自定义条码、库存、退货图标：以 Brand 线性图标风格统一。

### 8. 响应式细节

| Breakpoint | 侧边栏 | 表格 → 列显示 | 抽屉宽度       |
| ---------- | --- | -------- | ---------- |
| ≥LG        | 固定  | 全列       | 35%        |
| MD         | 可收缩 | 关键 4 列   | 45%        |
| SM         | 隐藏  | 卡片列表栅格   | 100% Modal |

---

## 更新记录

- **V1.3 (2025‑06‑14)**：新增完整《UI 视觉风格指南》，包含 Design Tokens、组件变体、无障碍与响应式规范。

## 附录

### 附录 A — 功能需求矩阵

> **说明**：供 Coding Agent 直接解析，按行生成接口、模型与前端路由。优先级 (P) 分为 `M`ust、`S`hould、`C`ould。

| 模块   | 功能 ID      | 功能描述    | API Endpoint          | 方法        | 请求体字段 (主)                         | 响应                   | P | DoD / 验收             | Owner   |
| ---- | ---------- | ------- | --------------------- | --------- | --------------------------------- | -------------------- | - | -------------------- | ------- |
| Auth | AUTH‑01    | 用户登录    | /auth/login           | POST      | username, password                | 200 + JWT            | M | 响应时间 ≤300 ms；错误码 401 | Backend |
| 产品   | PROD‑01    | 新增产品    | /products             | POST      | name, barcode, photo\_url         | 201 + product\_id    | M | 重复条码返回 409           | Backend |
| 产品   | PROD‑02    | 产品列表查询  | /products             | GET       | keyword, category                 | 200 + list           | M | 1k 条  <1 s           | Backend |
| 入库   | STOCKIN‑01 | 创建入库单   | /inventory/stockin    | POST      | product\_id[], qty[]              | 201 + record\_id     | M | 入库后库存更新正确            | Backend |
| 入库   | STOCKIN‑02 | 扫码搜索产品  | /products/{barcode}   | GET       | –                                 | 200 + product        | M | 条码不存在返回 404          | Backend |
| 库存   | INV‑01     | 库存总览    | /inventory            | GET       | page, size, filter                | 200 list             | M | 1k SKU <1 s          | Backend |
| 报表   | REPORT‑01  | 周报生成任务  | n/a (Cron)            | –         | –                                 | 写入 report collection | S | 周一 08:00 完成          | Backend |
| 盘点   | CHECK‑01   | 创建盘点任务  | /inventory/check      | POST      | date                              | 201 + task\_id       | S | 生成待盘点列表              | Backend |
| 盘点   | CHECK‑02   | 提交盘点结果  | /inventory/check/{id} | PATCH     | counted\_qty[]                    | 200                  | S | 差异写入 OperationLogs   | Backend |
| 客服   | CS‑01      | AI 问答   | /chat                 | WS / POST | message, session\_id              | stream text          | M | P95 2 s 内首 token     | AI      |
| 客服   | CS‑02      | 创建退货申请  | /returns              | POST      | purchase\_date, items[], photos[] | 201 + return\_id     | M | 填写完整度校验              | AI      |
| 退货   | RET‑01     | 管理员审核退货 | /returns/{id}/approve | POST      | approved, note                    | 200                  | M | 状态变更 + 通知            | Admin   |

> *提示：Coding Agent 可按 "模块\_功能ID" 自动生成 controller、service、model 文件夹结构。*

---

### 附录 B — 退货政策 YAML 示例 — 退货政策 YAML 示例

```yaml
version: 1.0
refund_policy:
  time_limit_hours: 24               # 提交质量问题反馈的时限
  verification_required: true        # 需经后台人工核实
  refundable_conditions:
    - quality_issue                  # 商品存在客观质量问题
  non_refundable_conditions:
    - non_quality_issue              # 非质量问题
    - improper_storage               # 保存不当导致
    - feedback_after_3_days_pickup   # 取货 3 天后才反馈
  refund_methods:
    - credit_next_order              # 作为下次拼单抵扣
    - direct_refund                  # 直接退款至 Venmo
notes: |
  质量问题需上传清晰照片；若核实为质量问题，客户可选择退款或换货。
```

### 附录 C — LLM Prompt 设计草案

```text
<system>
You are the official customer‑service AI for our community grocery platform.  
Use the structured rules in {refund_policy} to decide whether a refund request is eligible.  
Answer in polite Chinese, concise and friendly. If unsure, ask clarifying questions.
</system>
<assistant_tools>
  search_inventory(product_name)   # returns JSON with stock & spec
  create_return_request(json_payload)
</assistant_tools>
<examples>
User: "我要退这个鸡，肉不新鲜。昨天取的货。"  
Assistant: （判断符合 24h 条件且质量问题）→ 引导填写表单。
</examples>
```

### 附录 D — OpenAPI v3 接口纲要 (摘录)

```yaml
openapi: 3.1.0
info:
  title: Inventory & CS API
  version: 0.1.0
paths:
  /auth/login:
    post:
      summary: 用户登录
      requestBody:
        application/json:
          schema: { $ref: '#/components/schemas/LoginRequest' }
      responses:
        '200': { description: OK, content: { application/json: { schema: { $ref: '#/components/schemas/AuthToken' } } } }
  /products:
    get:
      summary: 获取产品列表
      parameters:
        - in: query
          name: keyword
          schema: { type: string }
    post:
      summary: 新增产品
      security: [ BearerAuth: [] ]
  /inventory/stockin:
    post:
      summary: 创建入库单
  /inventory/report/weekly:
    get:
      summary: 获取周报
  /returns:
    post:
      summary: 客户提交退货申请
  /returns/{id}/approve:
    post:
      summary: 管理员审核退货
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    LoginRequest: { type: object, properties: { username: { type: string }, password: { type: string } }, required: [username, password] }
    AuthToken: { type: object, properties: { access_token: { type: string }, expires_in: { type: integer } } }
```

### 附录 E — 数据库 DDL （MongoDB with Mongoose）

```js
// products.js
const ProductSchema = new Schema({
  name: { type: String, required: true, index: true },
  category: { type: String, enum: [ 'produce', 'poultry', 'dry_goods' ], index: true },
  size: { type: String },
  description: String,
  barcode: { type: String, unique: true },
  photo_url: String,
  expiry_date: Date,
  created_at: { type: Date, default: Date.now }
});
```

(其余 Inventory、ReturnRequests、OperationLogs 同理，详细字段见 ER 图)

### 附录 F — 环境变量清单 (.env.sample)

```
PORT=4000
MONGO_URI=*******************************
JWT_SECRET=replace_me
AWS_ACCESS_KEY=
AWS_SECRET_KEY=
S3_BUCKET=inventory-assets
LLM_PROVIDER=deepseek
LLM_API_KEY=replace_me
FRONTEND_URL=https://inventory.example.com
```

### 附录 G — 种子数据 seed.json 结构

```json
{
  "products": [
    { "name": "走地鸡", "category": "poultry", "size": "约3‑4lb", "barcode": "A001", "photo_url": "s3://.../chicken.jpg" },
    { "name": "日本贝贝南瓜", "category": "produce", "size": "1‑2lb", "barcode": "A002" }
  ],
  "users": [ { "username": "admin", "password_hash": "...", "role": "admin" } ]
}
```

---

> **版本记录**：V1.1 → V1.2 (2025‑06‑14) 新增退货政策 YAML、Prompt 草案、OpenAPI 摘要、DDL 片段、环境变量与种子数据格式。

### 附录 H — 一步一步部署向导（面向非技术用户）

> **目标**：不写代码，仅通过可视化操作把系统在 **Render + MongoDB Atlas + AWS S3** 上跑起来。

#### 前置账户准备

1. **GitHub 账号**（存放代码仓库，Coding Agent 生成后推送）。
2. **Render 账号**（你已拥有）。
3. **MongoDB Atlas 账号**（免费注册）。
4. **AWS 账号**（S3 用于图片）。
5. **域名托管**（InMotionHosting 已有）。

---

#### Step‑by‑Step 操作

| 步骤    | 在哪做                      | 详细操作                                                                                                                                                                                                                                                                               | 成果 / 复制内容                                                          |
| ----- | ------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------ |
| **1** | **GitHub**               | 让 Coding Agent 创建仓库 `inventory-ai-system` → Push 代码                                                                                                                                                                                                                                | 复制 **GitHub Repo URL**                                             |
| **2** | **MongoDB Atlas**        | a. Register → Organization → Create Projectb. Build Cluster → Free Shared (M0) → AWS us-east-1c. Database Access → Add User `render_user` + passwordd. Network Access → Allow Access from Anywhere (`0.0.0.0/0`)e. Clusters → Connect → Drivers → Copy **Connection String (SRV)** | `MONGO_URI=mongodb+srv://render_user:***@cluster0.mongodb.net/db`  |
| **3** | **AWS S3**               | a. Sign in AWS Console → S3 → Create bucket `hubgoodfood-assets`（美东）b. Block Public Access = **ON**c. IAM → Users → Add user `render-uploader` (Programmatic)d. Attach policy `AmazonS3FullAccess` (PoC阶段)e. 记下 **AccessKey / SecretKey**                                          | `AWS_ACCESS_KEY`, `AWS_SECRET_KEY`, `S3_BUCKET=hubgoodfood-assets` |
| **4** | **Render**               | a. Dashboard → New → Web Service → **Deploy from GitHub Repo**b. Environment = Nodec. Build Command: `yarn install && yarn build` (或 `npm ci`)d. Start Command: `node dist/server.js`e. Plan: `Starter`（\$7/mo）                                                                    | Render URL `https://inventory-xxxxx.onrender.com`                  |
| **5** | **Render → Environment** | Add VARIABLES:`MONGO_URI` (Step 2)`PORT=4000``JWT_SECRET=<random>``AWS_ACCESS_KEY`, `AWS_SECRET_KEY`, `S3_BUCKET` (Step3)`LLM_PROVIDER=deepseek``LLM_API_KEY=<your-llm-key>`                                                                                                       | 环境变量完成 ✔︎                                                          |
| **6** | **Render → Deploy**      | 點 “Manual Deploy” → “Clear Cache & Deploy”                                                                                                                                                                                                                                         | 日志出现 `Server started on port 4000`                                 |
| **7** | **浏览器**                  | 访问 `/health` 路由                                                                                                                                                                                                                                                                    | 返回 `{status:"ok"}`                                                 |
| **8** | **InMotion DNS**         | 添加 CNAME：`app` → `inventory-xxxxx.onrender.com`                                                                                                                                                                                                                                    | 10 分钟生效                                                            |
| **9** | **后台登录**                 | `https://app.hubgoodfood.com/admin`账号: `admin` / 初始密码                                                                                                                                                                                                                              | 成功登录后台                                                             |

---

> **版本记录**：V1.3 → V1.4 (2025‑06‑14) 新增《附录 H — 部署向导》。
