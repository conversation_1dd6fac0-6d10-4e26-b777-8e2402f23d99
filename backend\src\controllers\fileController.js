const fileUploadService = require('../services/fileUploadService');
const { OperationLog } = require('../models');
const logger = require('../utils/logger');

/**
 * 文件控制器
 * 处理文件上传、下载、删除等操作
 */
class FileController {
  /**
   * 单文件上传
   */
  async uploadSingleFile(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const file = req.file;
      const { folder = 'uploads', compress = true } = req.body;

      if (!file) {
        return res.status(400).json({
          success: false,
          message: '未提供文件'
        });
      }

      logger.info('开始单文件上传', {
        userId,
        fileName: file.originalname,
        fileSize: file.size,
        mimetype: file.mimetype,
        folder
      });

      // 上传文件
      const result = await fileUploadService.uploadSingleFile(file, folder, {
        compress: compress === 'true' || compress === true
      });

      if (!result.success) {
        return res.status(400).json(result);
      }

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'file.upload',
        'file',
        result.data.id,
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 201,
          response_time_ms: responseTime,
          after_data: {
            fileName: result.data.originalName,
            fileSize: result.data.size,
            contentType: result.data.contentType,
            key: result.data.key
          },
          description: `上传文件: ${result.data.originalName}`
        }
      );

      res.status(201).json({
        success: true,
        data: result.data,
        message: '文件上传成功'
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('单文件上传失败', {
        userId: req.user?.userId,
        fileName: req.file?.originalname,
        error: error.message,
        stack: error.stack
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'file.upload',
          'file',
          null,
          {
            action: 'create',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: `文件上传失败: ${req.file?.originalname || '未知文件'}`
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '文件上传失败',
        error: error.message
      });
    }
  }

  /**
   * 多文件上传
   */
  async uploadMultipleFiles(req, res) {
    const startTime = Date.now();
    
    try {
      const userId = req.user.userId;
      const files = req.files;
      const { folder = 'uploads', compress = true } = req.body;

      if (!files || files.length === 0) {
        return res.status(400).json({
          success: false,
          message: '未提供文件'
        });
      }

      logger.info('开始多文件上传', {
        userId,
        fileCount: files.length,
        totalSize: files.reduce((sum, file) => sum + file.size, 0),
        folder
      });

      // 批量上传文件
      const result = await fileUploadService.uploadMultipleFiles(files, folder, {
        compress: compress === 'true' || compress === true
      });

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'file.upload_multiple',
        'file',
        'batch',
        {
          action: 'create',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: result.success ? 201 : 207, // 207 Multi-Status
          response_time_ms: responseTime,
          after_data: {
            totalFiles: files.length,
            successCount: result.data.summary.success,
            failedCount: result.data.summary.failed,
            uploadedFiles: result.data.uploaded.map(file => ({
              fileName: file.originalName,
              key: file.key,
              size: file.size
            }))
          },
          description: `批量上传文件: ${result.data.summary.success}/${files.length} 成功`
        }
      );

      res.status(result.success ? 201 : 207).json({
        success: result.success,
        data: result.data,
        message: result.success ? '所有文件上传成功' : '部分文件上传失败'
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('多文件上传失败', {
        userId: req.user?.userId,
        fileCount: req.files?.length || 0,
        error: error.message,
        stack: error.stack
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'file.upload_multiple',
          'file',
          'batch',
          {
            action: 'create',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: `批量文件上传失败: ${req.files?.length || 0} 个文件`
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '批量文件上传失败',
        error: error.message
      });
    }
  }

  /**
   * 获取文件访问URL
   */
  async getFileUrl(req, res) {
    try {
      const { key } = req.params;
      const { expires = 3600 } = req.query;
      const userId = req.user.userId;

      if (!key) {
        return res.status(400).json({
          success: false,
          message: '文件键不能为空'
        });
      }

      logger.info('获取文件访问URL', {
        userId,
        key,
        expires
      });

      // 获取预签名URL
      const url = await fileUploadService.getFileUrl(key, parseInt(expires));

      res.json({
        success: true,
        data: {
          key,
          url,
          expires: parseInt(expires)
        },
        message: '获取文件URL成功'
      });

    } catch (error) {
      logger.error('获取文件URL失败', {
        userId: req.user?.userId,
        key: req.params?.key,
        error: error.message
      });

      res.status(500).json({
        success: false,
        message: '获取文件URL失败',
        error: error.message
      });
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(req, res) {
    const startTime = Date.now();
    
    try {
      const { key } = req.params;
      const userId = req.user.userId;

      if (!key) {
        return res.status(400).json({
          success: false,
          message: '文件键不能为空'
        });
      }

      logger.info('开始删除文件', {
        userId,
        key
      });

      // 删除文件
      const result = await fileUploadService.deleteFile(key);

      const responseTime = Date.now() - startTime;

      // 记录操作日志
      await OperationLog.logSuccess(
        userId,
        'file.delete',
        'file',
        key,
        {
          action: 'delete',
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_method: req.method,
          request_url: req.originalUrl,
          response_status: 200,
          response_time_ms: responseTime,
          before_data: { key },
          description: `删除文件: ${key}`
        }
      );

      res.json({
        success: true,
        message: '文件删除成功'
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      logger.error('删除文件失败', {
        userId: req.user?.userId,
        key: req.params?.key,
        error: error.message
      });

      // 记录错误日志
      if (req.user?.userId) {
        await OperationLog.logError(
          req.user.userId,
          'file.delete',
          'file',
          req.params?.key,
          {
            action: 'delete',
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_method: req.method,
            request_url: req.originalUrl,
            response_status: 500,
            response_time_ms: responseTime,
            error_message: error.message,
            description: `删除文件失败: ${req.params?.key || '未知文件'}`
          }
        );
      }

      res.status(500).json({
        success: false,
        message: '删除文件失败',
        error: error.message
      });
    }
  }
}

module.exports = new FileController();
