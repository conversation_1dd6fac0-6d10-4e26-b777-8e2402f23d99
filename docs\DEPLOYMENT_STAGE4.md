# 第四阶段部署和配置文档

本文档详细说明了第四阶段新增功能的部署要求、环境配置和运维指南。

## 📦 新增依赖安装

### Node.js 依赖

第四阶段新增了以下依赖包：

```bash
# 定时任务调度
npm install node-cron

# 验证安装
npm list node-cron
```

### 依赖说明

#### node-cron
- **版本要求**: ^3.0.0 或更高
- **用途**: 定时任务调度，支持报表自动生成
- **配置**: 支持标准Cron表达式
- **时区**: 默认使用 Asia/Shanghai 时区

## 🔧 环境变量配置

### 新增环境变量

在 `.env` 文件中添加以下配置：

```bash
# 报表生成配置
REPORT_GENERATION_TIMEOUT=300000          # 报表生成超时时间(毫秒)，默认5分钟
REPORT_MAX_ITEMS_PER_SECTION=1000         # 每个章节最大数据项数
REPORT_CLEANUP_DAYS=30                    # 报表清理天数，默认30天

# 盘点任务配置
STOCKCOUNT_MAX_ITEMS=10000                # 单次盘点最大项目数
STOCKCOUNT_DEFAULT_TOLERANCE=5            # 默认容差百分比
STOCKCOUNT_AUTO_CLEANUP_DAYS=90           # 盘点任务自动清理天数

# 智能预警配置
ALERT_ANALYSIS_CACHE_TTL=3600             # 分析结果缓存时间(秒)，默认1小时
ALERT_PREDICTION_DAYS=7                   # 默认预测天数
ALERT_TREND_ANALYSIS_DAYS=30              # 默认趋势分析天数

# 自动化审核配置
AUTOMATION_MAX_BATCH_SIZE=100             # 批量处理最大数量
AUTOMATION_CONFIDENCE_THRESHOLD=0.8       # 自动化决策置信度阈值
AUTOMATION_HIGH_VALUE_THRESHOLD=1000      # 高价值商品阈值

# 定时任务配置
SCHEDULER_ENABLED=true                    # 是否启用定时任务
SCHEDULER_TIMEZONE=Asia/Shanghai          # 定时任务时区
SCHEDULER_MAX_CONCURRENT_JOBS=5           # 最大并发任务数

# 性能优化配置
CACHE_ENABLED=true                        # 是否启用缓存
CACHE_TTL=1800                           # 缓存过期时间(秒)，默认30分钟
MAX_QUERY_LIMIT=1000                     # 查询结果最大限制
```

### 环境变量验证

创建验证脚本 `scripts/validate-env.js`：

```javascript
const requiredEnvVars = [
  'MONGO_URI',
  'JWT_SECRET',
  'PORT'
];

const optionalEnvVars = [
  'REPORT_GENERATION_TIMEOUT',
  'STOCKCOUNT_MAX_ITEMS',
  'ALERT_ANALYSIS_CACHE_TTL',
  'SCHEDULER_ENABLED'
];

console.log('🔍 验证环境变量配置...');

// 检查必需变量
const missingRequired = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingRequired.length > 0) {
  console.error('❌ 缺少必需的环境变量:', missingRequired);
  process.exit(1);
}

// 检查可选变量
const missingOptional = optionalEnvVars.filter(varName => !process.env[varName]);
if (missingOptional.length > 0) {
  console.warn('⚠️  使用默认值的环境变量:', missingOptional);
}

console.log('✅ 环境变量验证通过');
```

## 🚀 部署步骤

### 1. 代码部署

```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装新依赖
cd backend
npm install

# 3. 验证环境配置
node scripts/validate-env.js

# 4. 运行数据库迁移（如果需要）
npm run migrate

# 5. 重启服务
pm2 restart inventory-ai-backend
```

### 2. 数据库更新

#### MongoDB 索引创建

```javascript
// 连接到MongoDB并执行以下命令

// StockCount 模型索引
db.stockcounts.createIndex({ "count_id": 1 }, { unique: true });
db.stockcounts.createIndex({ "count_type": 1, "status": 1 });
db.stockcounts.createIndex({ "scheduled_date": 1 });
db.stockcounts.createIndex({ "created_by": 1, "createdAt": -1 });
db.stockcounts.createIndex({ "assigned_to.user_id": 1 });
db.stockcounts.createIndex({ "location": 1 });
db.stockcounts.createIndex({ "categories": 1 });

// Report 模型增强索引
db.reports.createIndex({ "report_id": 1 }, { unique: true });
db.reports.createIndex({ "report_type": 1, "status": 1 });
db.reports.createIndex({ "generated_at": -1 });
db.reports.createIndex({ "generated_by": 1, "generated_at": -1 });
db.reports.createIndex({ "schedule.is_scheduled": 1, "schedule.next_run": 1 });

// 性能优化索引
db.inventories.createIndex({ "last_count_date": 1 });
db.stockinrecords.createIndex({ "created_at": 1, "status": 1 });
```

#### 数据迁移脚本

创建 `scripts/migrate-stage4.js`：

```javascript
const mongoose = require('mongoose');
const { Report } = require('../src/models');

async function migrateReports() {
  console.log('🔄 开始迁移Report模型...');
  
  // 为现有报表添加新字段
  await Report.updateMany(
    { schedule: { $exists: false } },
    {
      $set: {
        schedule: {
          is_scheduled: false,
          enabled: false
        }
      }
    }
  );
  
  console.log('✅ Report模型迁移完成');
}

async function runMigration() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    await migrateReports();
    console.log('🎉 数据迁移完成');
  } catch (error) {
    console.error('❌ 数据迁移失败:', error);
  } finally {
    await mongoose.disconnect();
  }
}

if (require.main === module) {
  runMigration();
}
```

### 3. 定时任务配置

#### PM2 生态系统配置

更新 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [
    {
      name: 'inventory-ai-backend',
      script: './src/server.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        SCHEDULER_ENABLED: 'true'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        SCHEDULER_ENABLED: 'true'
      }
    }
  ]
};
```

#### 系统级定时任务（可选）

如果需要系统级备份，可以配置crontab：

```bash
# 编辑crontab
crontab -e

# 添加以下任务
# 每天凌晨2点备份数据库
0 2 * * * /usr/local/bin/mongodump --uri="$MONGO_URI" --out="/backup/$(date +\%Y\%m\%d)"

# 每周日凌晨3点清理旧备份
0 3 * * 0 find /backup -type d -mtime +30 -exec rm -rf {} \;
```

## 📊 监控和日志

### 1. 应用监控

#### 健康检查端点

在 `server.js` 中添加健康检查：

```javascript
// 健康检查端点
app.get('/health', (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {
      database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
      scheduler: schedulerService.isInitialized ? 'running' : 'stopped'
    },
    version: process.env.npm_package_version || '1.0.0'
  };
  
  res.json(health);
});
```

#### 性能监控

```javascript
// 性能指标收集
const performanceMetrics = {
  reportGeneration: {
    count: 0,
    totalTime: 0,
    avgTime: 0
  },
  stockCountCreation: {
    count: 0,
    totalTime: 0,
    avgTime: 0
  }
};

// 在相关服务中记录性能指标
function recordMetric(operation, duration) {
  const metric = performanceMetrics[operation];
  if (metric) {
    metric.count++;
    metric.totalTime += duration;
    metric.avgTime = metric.totalTime / metric.count;
  }
}
```

### 2. 日志配置

#### 日志级别配置

```javascript
// 生产环境日志配置
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

#### 日志轮转配置

```bash
# 安装日志轮转工具
sudo apt-get install logrotate

# 创建配置文件 /etc/logrotate.d/inventory-ai
/path/to/app/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 app app
    postrotate
        pm2 reload inventory-ai-backend
    endscript
}
```

## 🔒 安全配置

### 1. API限流

```javascript
const rateLimit = require('express-rate-limit');

// 报表生成限流
const reportLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 每用户每小时最多10次
  message: '报表生成请求过于频繁，请稍后再试'
});

app.use('/api/reports/generate', reportLimiter);

// 批量操作限流
const batchLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每用户每15分钟最多5次
  message: '批量操作请求过于频繁，请稍后再试'
});

app.use('/api/returns/batch-review', batchLimiter);
app.use('/api/returns/automation/execute', batchLimiter);
```

### 2. 权限验证

确保所有新增API端点都有适当的权限验证：

```javascript
// 权限配置示例
const permissions = {
  'reports.create': ['admin', 'manager'],
  'reports.read': ['admin', 'manager', 'staff'],
  'reports.delete': ['admin'],
  'stockcount.create': ['admin', 'manager'],
  'stockcount.assign': ['admin', 'manager'],
  'stockcount.execute': ['admin', 'manager', 'staff'],
  'returns.admin': ['admin'],
  'alerts.read': ['admin', 'manager', 'staff']
};
```

## 🚨 故障排除

### 常见问题

#### 1. 定时任务不执行
```bash
# 检查定时任务状态
curl http://localhost:3000/api/scheduler/status

# 检查日志
tail -f logs/combined.log | grep scheduler

# 重启定时任务服务
pm2 restart inventory-ai-backend
```

#### 2. 报表生成超时
```bash
# 检查数据库连接
mongo $MONGO_URI --eval "db.stats()"

# 检查内存使用
free -h

# 增加超时时间
export REPORT_GENERATION_TIMEOUT=600000
```

#### 3. 盘点任务创建失败
```bash
# 检查数据库索引
mongo $MONGO_URI --eval "db.stockcounts.getIndexes()"

# 检查权限配置
curl -H "Authorization: Bearer $TOKEN" http://localhost:3000/api/auth/verify
```

### 性能优化

#### 1. 数据库优化
```javascript
// 连接池配置
mongoose.connect(process.env.MONGO_URI, {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferCommands: false,
  bufferMaxEntries: 0
});
```

#### 2. 缓存配置
```javascript
const Redis = require('redis');
const client = Redis.createClient({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  ttl: process.env.CACHE_TTL || 1800
});
```

## 📋 部署检查清单

### 部署前检查
- [ ] 代码已合并到主分支
- [ ] 所有测试通过
- [ ] 环境变量已配置
- [ ] 数据库备份已完成
- [ ] 依赖包已更新

### 部署后验证
- [ ] 应用启动成功
- [ ] 健康检查通过
- [ ] 新API端点可访问
- [ ] 定时任务正常运行
- [ ] 日志记录正常
- [ ] 性能指标正常

### 回滚准备
- [ ] 备份当前版本
- [ ] 准备回滚脚本
- [ ] 确认回滚步骤
- [ ] 通知相关人员
