# 🚀 快速配置指南

根据配置验证结果，您的环境变量配置已经基本完成！以下是需要完善的几个关键配置项：

## ✅ 已完成的配置

- ✅ **MongoDB Atlas**: 已正确配置
- ✅ **AWS S3**: 访问密钥已配置
- ✅ **基础安全**: JWT和加密密钥已生成
- ✅ **服务器配置**: 端口和环境已设置
- ✅ **AI服务**: Chutes AI平台的DeepSeek API已配置并测试通过

## 🎉 AI功能测试成功！

您的AI客服功能已经可以正常工作：

**✅ 测试结果：**
- Chutes AI 平台连接正常
- DeepSeek-V3 模型响应正常
- 中文对话功能正常
- 库存查询场景测试通过

**🔧 当前AI配置：**
```bash
DEEPSEEK_API_KEY=cpk_134ee649c58945309caf13e806f1af56...
DEEPSEEK_BASE_URL=https://llm.chutes.ai/v1
DEEPSEEK_MODEL=deepseek-ai/DeepSeek-V3-0324
```

## ⚠️ 需要完善的配置

### 2. 📧 邮件服务配置 (推荐)

用于发送库存周报和系统通知：

**Gmail配置步骤：**
1. 启用Gmail的两步验证
2. 生成应用专用密码：
   - 访问 [Google账户设置](https://myaccount.google.com/)
   - 安全 → 两步验证 → 应用专用密码
   - 选择"邮件"和"其他"，生成密码

**更新配置：**
```bash
# 编辑 .env 文件，更新：
SMTP_USER=<EMAIL>
SMTP_PASS=your_16_character_app_password
```

## 🧪 测试配置

### 1. 验证环境变量配置
```bash
node verify-config.js
```

### 2. 测试AI API连接
```bash
node test-ai-api.js
```

**预期输出：**
- ✅ AI API 连接成功
- ✅ 中文对话功能正常
- ✅ 库存查询场景测试通过

## 🚀 启动开发环境

### 1. 创建基础项目结构

```bash
# 初始化Node.js项目
npm init -y

# 安装基础依赖
npm install express mongoose dotenv cors helmet
npm install --save-dev nodemon

# 安装AI相关依赖
npm install axios

# 安装AWS SDK
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
```

### 2. 创建基础服务器文件

```javascript
// server.js
require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');

const app = express();
const PORT = process.env.PORT || 4000;

// 中间件
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000']
}));
app.use(express.json({ limit: '10mb' }));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV 
  });
});

// 数据库连接
mongoose.connect(process.env.MONGO_URI)
  .then(() => console.log('✅ MongoDB 连接成功'))
  .catch(err => console.error('❌ MongoDB 连接失败:', err));

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器运行在端口 ${PORT}`);
  console.log(`🌍 环境: ${process.env.NODE_ENV}`);
  console.log(`🔗 健康检查: http://localhost:${PORT}/health`);
});
```

### 3. 更新package.json脚本

```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "verify": "node verify-config.js"
  }
}
```

## 📋 下一步开发计划

### 阶段1: 基础API (1-2天)
- [ ] 用户认证系统
- [ ] 产品管理API
- [ ] 基础库存操作

### 阶段2: AI集成 (2-3天)
- [ ] DeepSeek API集成
- [ ] 智能问答功能
- [ ] 退货判断逻辑

### 阶段3: 高级功能 (3-4天)
- [ ] 文件上传到S3
- [ ] 邮件通知系统
- [ ] 库存预警功能

## 🔧 常用命令

```bash
# 验证配置
node verify-config.js

# 启动开发服务器
npm run dev

# 检查健康状态
curl http://localhost:4000/health

# 查看日志
tail -f logs/app.log
```

## 🆘 故障排除

### MongoDB连接问题
```bash
# 测试MongoDB连接
node -e "
const mongoose = require('mongoose');
mongoose.connect(process.env.MONGO_URI || 'your_mongo_uri')
  .then(() => console.log('✅ 连接成功'))
  .catch(err => console.error('❌ 连接失败:', err));
"
```

### AWS S3连接问题
```bash
# 测试AWS S3连接
node -e "
const { S3Client, ListBucketsCommand } = require('@aws-sdk/client-s3');
const client = new S3Client({ region: 'us-east-1' });
client.send(new ListBucketsCommand({}))
  .then(data => console.log('✅ S3连接成功:', data.Buckets?.length, '个存储桶'))
  .catch(err => console.error('❌ S3连接失败:', err.message));
"
```

## 📞 技术支持

如果遇到问题：
1. 检查 `.env` 文件配置
2. 运行 `node verify-config.js` 验证
3. 查看应用日志
4. 参考 `ENV_SETUP_GUIDE.md` 详细说明

---

**🎉 恭喜！您的环境配置已基本完成，可以开始开发了！**
