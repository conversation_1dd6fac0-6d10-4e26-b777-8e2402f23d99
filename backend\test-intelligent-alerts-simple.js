/**
 * 智能预警功能简化测试
 * 测试智能预警算法的基本逻辑，不依赖数据库连接
 */

/**
 * 测试趋势计算算法
 */
function testTrendCalculation() {
  console.log('📈 测试趋势计算算法...');
  
  try {
    // 模拟趋势计算函数
    function calculateTrend(dailyData) {
      if (dailyData.length < 2) {
        return { direction: 'stable', strength: 0, volatility: 0 };
      }

      const quantities = dailyData.map(d => d.quantity);
      const n = quantities.length;
      
      // 简单线性回归计算趋势
      let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
      
      for (let i = 0; i < n; i++) {
        sumX += i;
        sumY += quantities[i];
        sumXY += i * quantities[i];
        sumXX += i * i;
      }
      
      const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
      const direction = slope > 1.0 ? 'increasing' : slope < -1.0 ? 'decreasing' : 'stable';
      const strength = Math.abs(slope);
      
      // 计算波动性
      const mean = sumY / n;
      const variance = quantities.reduce((sum, q) => sum + Math.pow(q - mean, 2), 0) / n;
      const volatility = Math.sqrt(variance) / mean;
      
      return { direction, strength, volatility };
    }
    
    // 测试各种趋势场景
    const testCases = [
      {
        name: '上升趋势',
        data: [
          { date: '2025-06-01', quantity: 10 },
          { date: '2025-06-02', quantity: 15 },
          { date: '2025-06-03', quantity: 20 },
          { date: '2025-06-04', quantity: 25 }
        ],
        expectedDirection: 'increasing'
      },
      {
        name: '下降趋势',
        data: [
          { date: '2025-06-01', quantity: 25 },
          { date: '2025-06-02', quantity: 20 },
          { date: '2025-06-03', quantity: 15 },
          { date: '2025-06-04', quantity: 10 }
        ],
        expectedDirection: 'decreasing'
      },
      {
        name: '稳定趋势',
        data: [
          { date: '2025-06-01', quantity: 20 },
          { date: '2025-06-02', quantity: 21 },
          { date: '2025-06-03', quantity: 19 },
          { date: '2025-06-04', quantity: 20 }
        ],
        expectedDirection: 'stable'
      },
      {
        name: '单点数据',
        data: [
          { date: '2025-06-01', quantity: 20 }
        ],
        expectedDirection: 'stable'
      }
    ];
    
    let allPassed = true;
    
    testCases.forEach((testCase, index) => {
      const result = calculateTrend(testCase.data);
      const passed = result.direction === testCase.expectedDirection;
      
      console.log(`   测试 ${index + 1} (${testCase.name}): ${result.direction} (强度: ${result.strength.toFixed(2)}, 波动: ${result.volatility.toFixed(2)}) ${passed ? '✅' : '❌'}`);
      
      if (!passed) {
        allPassed = false;
      }
    });
    
    if (allPassed) {
      console.log('✅ 趋势计算算法验证通过');
      return true;
    } else {
      console.log('❌ 趋势计算算法验证失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 趋势计算算法测试失败:', error.message);
    return false;
  }
}

/**
 * 测试消费强度评估
 */
function testConsumptionIntensityAssessment() {
  console.log('\n🔥 测试消费强度评估...');
  
  try {
    // 模拟消费强度评估函数
    function calculateConsumptionIntensity(lowStockRatio) {
      if (lowStockRatio > 0.5) return 'high';
      if (lowStockRatio > 0.2) return 'medium';
      return 'low';
    }
    
    function assessCategoryRisk(lowStockRatio) {
      if (lowStockRatio > 0.3) return 'high';
      if (lowStockRatio >= 0.1) return 'medium';
      return 'low';
    }
    
    // 测试消费强度场景
    const testCases = [
      { lowStockRatio: 0.8, expectedIntensity: 'high', expectedRisk: 'high' },
      { lowStockRatio: 0.4, expectedIntensity: 'medium', expectedRisk: 'high' },
      { lowStockRatio: 0.3, expectedIntensity: 'medium', expectedRisk: 'medium' },
      { lowStockRatio: 0.2, expectedIntensity: 'low', expectedRisk: 'medium' },
      { lowStockRatio: 0.1, expectedIntensity: 'low', expectedRisk: 'medium' },
      { lowStockRatio: 0.05, expectedIntensity: 'low', expectedRisk: 'low' }
    ];
    
    let allPassed = true;
    
    testCases.forEach((testCase, index) => {
      const intensity = calculateConsumptionIntensity(testCase.lowStockRatio);
      const risk = assessCategoryRisk(testCase.lowStockRatio);
      
      const intensityPassed = intensity === testCase.expectedIntensity;
      const riskPassed = risk === testCase.expectedRisk;
      const passed = intensityPassed && riskPassed;
      
      console.log(`   测试 ${index + 1}: 低库存比例${testCase.lowStockRatio} -> 强度:${intensity}, 风险:${risk} ${passed ? '✅' : '❌'}`);
      
      if (!passed) {
        allPassed = false;
      }
    });
    
    if (allPassed) {
      console.log('✅ 消费强度评估验证通过');
      return true;
    } else {
      console.log('❌ 消费强度评估验证失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 消费强度评估测试失败:', error.message);
    return false;
  }
}

/**
 * 测试风险评分算法
 */
function testRiskScoringAlgorithm() {
  console.log('\n⚠️  测试风险评分算法...');
  
  try {
    // 模拟风险评分函数
    function calculateRiskScore(item) {
      let riskScore = 0;
      const riskFactors = [];

      // 库存比例风险
      const stockRatio = item.reorder_point > 0 ? item.current_stock / item.reorder_point : 0;
      
      if (stockRatio < 0.5) {
        riskScore += 30;
        riskFactors.push('库存严重不足');
      } else if (stockRatio < 1.0) {
        riskScore += 15;
        riskFactors.push('库存不足');
      }

      // 过期风险
      if (item.days_to_expiry < 7 && item.days_to_expiry > 0) {
        riskScore += 25;
        riskFactors.push('即将过期');
      } else if (item.days_to_expiry < 0) {
        riskScore += 40;
        riskFactors.push('已过期');
      }

      // 价值风险
      if (item.total_value > 1000) {
        riskScore += 10;
        riskFactors.push('高价值商品');
      }

      let riskLevel = 'low';
      if (riskScore >= 50) {
        riskLevel = 'high';
      } else if (riskScore >= 25) {
        riskLevel = 'medium';
      }

      return { riskScore, riskLevel, riskFactors };
    }
    
    // 测试风险评分场景
    const testCases = [
      {
        name: '高风险商品',
        item: {
          current_stock: 5,
          reorder_point: 20,
          days_to_expiry: 3,
          total_value: 1500
        },
        expectedLevel: 'high'
      },
      {
        name: '中风险商品',
        item: {
          current_stock: 15,
          reorder_point: 20,
          days_to_expiry: 30,
          total_value: 1200
        },
        expectedLevel: 'medium'
      },
      {
        name: '低风险商品',
        item: {
          current_stock: 25,
          reorder_point: 20,
          days_to_expiry: 60,
          total_value: 500
        },
        expectedLevel: 'low'
      },
      {
        name: '过期高价值商品',
        item: {
          current_stock: 10,
          reorder_point: 20,
          days_to_expiry: -5,
          total_value: 2000
        },
        expectedLevel: 'high'
      }
    ];
    
    let allPassed = true;
    
    testCases.forEach((testCase, index) => {
      const result = calculateRiskScore(testCase.item);
      const passed = result.riskLevel === testCase.expectedLevel;
      
      console.log(`   测试 ${index + 1} (${testCase.name}): 评分${result.riskScore}, 等级${result.riskLevel}, 因子[${result.riskFactors.join(', ')}] ${passed ? '✅' : '❌'}`);
      
      if (!passed) {
        allPassed = false;
      }
    });
    
    if (allPassed) {
      console.log('✅ 风险评分算法验证通过');
      return true;
    } else {
      console.log('❌ 风险评分算法验证失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 风险评分算法测试失败:', error.message);
    return false;
  }
}

/**
 * 测试库存预测算法
 */
function testStockPredictionAlgorithm() {
  console.log('\n🔮 测试库存预测算法...');
  
  try {
    // 模拟库存预测函数
    function predictStock(currentStock, dailyConsumption, predictionDays, reorderPoint) {
      const predictedStock = Math.max(0, currentStock - (dailyConsumption * predictionDays));
      
      const stockoutRisk = predictedStock <= reorderPoint ? 'high' : 
                          predictedStock <= reorderPoint * 1.5 ? 'medium' : 'low';
      
      const daysUntilReorder = dailyConsumption > 0 ? 
        Math.ceil((currentStock - reorderPoint) / dailyConsumption) : 999;
      
      const recommendedAction = stockoutRisk === 'high' ? '立即补货' :
                               stockoutRisk === 'medium' ? '准备补货' :
                               predictedStock > reorderPoint * 2 ? '库存充足' : '正常监控';
      
      return {
        predictedStock: Math.round(predictedStock),
        stockoutRisk,
        daysUntilReorder,
        recommendedAction
      };
    }
    
    // 测试预测场景
    const testCases = [
      {
        name: '即将缺货',
        params: { currentStock: 50, dailyConsumption: 10, predictionDays: 7, reorderPoint: 20 },
        expectedRisk: 'high'
      },
      {
        name: '中等风险',
        params: { currentStock: 80, dailyConsumption: 8, predictionDays: 7, reorderPoint: 20 },
        expectedRisk: 'medium'
      },
      {
        name: '库存充足',
        params: { currentStock: 100, dailyConsumption: 5, predictionDays: 7, reorderPoint: 20 },
        expectedRisk: 'low'
      },
      {
        name: '零消费',
        params: { currentStock: 50, dailyConsumption: 0, predictionDays: 7, reorderPoint: 20 },
        expectedRisk: 'low'
      }
    ];
    
    let allPassed = true;
    
    testCases.forEach((testCase, index) => {
      const result = predictStock(
        testCase.params.currentStock,
        testCase.params.dailyConsumption,
        testCase.params.predictionDays,
        testCase.params.reorderPoint
      );
      
      const passed = result.stockoutRisk === testCase.expectedRisk;
      
      console.log(`   测试 ${index + 1} (${testCase.name}): 预测库存${result.predictedStock}, 风险${result.stockoutRisk}, ${result.daysUntilReorder}天后需补货, 建议:${result.recommendedAction} ${passed ? '✅' : '❌'}`);
      
      if (!passed) {
        allPassed = false;
      }
    });
    
    if (allPassed) {
      console.log('✅ 库存预测算法验证通过');
      return true;
    } else {
      console.log('❌ 库存预测算法验证失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 库存预测算法测试失败:', error.message);
    return false;
  }
}

/**
 * 测试季节性分析
 */
function testSeasonalAnalysis() {
  console.log('\n🌸 测试季节性分析...');
  
  try {
    // 模拟季节性分析函数
    function getSeason(month) {
      if (month >= 3 && month <= 5) return 'spring';
      if (month >= 6 && month <= 8) return 'summer';
      if (month >= 9 && month <= 11) return 'autumn';
      return 'winter';
    }
    
    function getSeasonalFactor(season) {
      const factors = {
        spring: 1.0,
        summer: 1.2,
        autumn: 1.1,
        winter: 0.9
      };
      return factors[season] || 1.0;
    }
    
    // 测试季节性分析
    const testCases = [
      { month: 1, expectedSeason: 'winter', expectedFactor: 0.9 },
      { month: 4, expectedSeason: 'spring', expectedFactor: 1.0 },
      { month: 7, expectedSeason: 'summer', expectedFactor: 1.2 },
      { month: 10, expectedSeason: 'autumn', expectedFactor: 1.1 },
      { month: 12, expectedSeason: 'winter', expectedFactor: 0.9 }
    ];
    
    let allPassed = true;
    
    testCases.forEach((testCase, index) => {
      const season = getSeason(testCase.month);
      const factor = getSeasonalFactor(season);
      
      const seasonPassed = season === testCase.expectedSeason;
      const factorPassed = factor === testCase.expectedFactor;
      const passed = seasonPassed && factorPassed;
      
      console.log(`   测试 ${index + 1}: ${testCase.month}月 -> ${season}季 (因子:${factor}) ${passed ? '✅' : '❌'}`);
      
      if (!passed) {
        allPassed = false;
      }
    });
    
    if (allPassed) {
      console.log('✅ 季节性分析验证通过');
      return true;
    } else {
      console.log('❌ 季节性分析验证失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 季节性分析测试失败:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始智能预警功能基础测试\n');
  
  const tests = [
    { name: '趋势计算算法', fn: testTrendCalculation },
    { name: '消费强度评估', fn: testConsumptionIntensityAssessment },
    { name: '风险评分算法', fn: testRiskScoringAlgorithm },
    { name: '库存预测算法', fn: testStockPredictionAlgorithm },
    { name: '季节性分析', fn: testSeasonalAnalysis }
  ];
  
  const results = [];
  
  tests.forEach(test => {
    try {
      const result = test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.error(`❌ ${test.name}测试执行失败:`, error.message);
      results.push({ name: test.name, passed: false });
    }
  });
  
  // 测试结果统计
  const passedCount = results.filter(r => r.passed).length;
  const totalCount = results.length;
  
  console.log('\n🎉 测试完成！');
  console.log(`\n📊 测试结果统计:`);
  console.log(`   通过测试: ${passedCount}/${totalCount}`);
  console.log(`   通过率: ${(passedCount / totalCount * 100).toFixed(1)}%`);
  
  console.log('\n📋 详细结果:');
  results.forEach(result => {
    console.log(`   ${result.passed ? '✅' : '❌'} ${result.name}`);
  });
  
  if (passedCount === totalCount) {
    console.log('\n🎉 所有基础测试通过！智能预警功能核心逻辑正常。');
  } else {
    console.log('\n⚠️  部分测试失败，请检查相关功能。');
  }
}

// 运行测试
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testTrendCalculation,
  testConsumptionIntensityAssessment,
  testRiskScoringAlgorithm,
  testStockPredictionAlgorithm,
  testSeasonalAnalysis
};
