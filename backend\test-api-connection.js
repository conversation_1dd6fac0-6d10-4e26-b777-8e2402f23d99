require('dotenv').config();
const axios = require('axios');

/**
 * API连接诊断测试
 */

async function testAPIConnection() {
  console.log('🔍 开始API连接诊断...\n');

  // 检查环境变量
  console.log('📋 环境变量检查:');
  console.log('- DEEPSEEK_API_KEY:', process.env.DEEPSEEK_API_KEY ? '已设置' : '未设置');
  console.log('- DEEPSEEK_BASE_URL:', process.env.DEEPSEEK_BASE_URL || '未设置');
  console.log('- DEEPSEEK_MODEL:', process.env.DEEPSEEK_MODEL || '未设置');

  if (!process.env.DEEPSEEK_API_KEY) {
    console.log('❌ API密钥未设置');
    return;
  }

  // 测试API连接
  console.log('\n📋 测试API连接:');
  
  try {
    const client = axios.create({
      baseURL: process.env.DEEPSEEK_BASE_URL || 'https://llm.chutes.ai/v1',
      headers: {
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('发送测试请求...');
    
    const response = await client.post('/chat/completions', {
      model: process.env.DEEPSEEK_MODEL || 'deepseek-ai/DeepSeek-V3-0324',
      messages: [
        {
          role: 'system',
          content: '你是一个测试助手。'
        },
        {
          role: 'user',
          content: '请回答：测试成功'
        }
      ],
      temperature: 0.7,
      max_tokens: 50
    });

    console.log('✅ API连接成功');
    console.log('响应状态:', response.status);
    console.log('响应内容:', response.data.choices[0].message.content);
    console.log('使用统计:', response.data.usage);

  } catch (error) {
    console.log('❌ API连接失败');
    console.log('错误类型:', error.constructor.name);
    console.log('错误消息:', error.message);
    
    if (error.response) {
      console.log('响应状态:', error.response.status);
      console.log('响应数据:', error.response.data);
    } else if (error.request) {
      console.log('请求超时或网络错误');
    }
  }

  // 测试不同的API端点
  console.log('\n📋 测试API端点可达性:');
  
  const endpoints = [
    'https://llm.chutes.ai/v1',
    'https://api.deepseek.com/v1'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`测试端点: ${endpoint}`);
      const response = await axios.get(endpoint, { timeout: 5000 });
      console.log(`✅ ${endpoint} 可达`);
    } catch (error) {
      console.log(`❌ ${endpoint} 不可达: ${error.message}`);
    }
  }
}

// 运行测试
if (require.main === module) {
  testAPIConnection().catch(console.error);
}

module.exports = testAPIConnection;
