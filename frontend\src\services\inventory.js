/**
 * 库存管理 API 服务
 */
import { apiRequest } from './api'

export const inventoryService = {
  // 获取库存总览
  getInventoryOverview: async () => {
    return apiRequest.get('/inventory/overview')
  },

  // 获取库存列表
  getInventoryList: async (params = {}) => {
    const { page = 1, limit = 10, search, category, status, sortBy, sortOrder } = params
    return apiRequest.get('/inventory', {
      page,
      limit,
      search,
      category,
      status,
      sortBy,
      sortOrder
    })
  },

  // 获取库存详情
  getInventoryDetail: async (id) => {
    return apiRequest.get(`/inventory/${id}`)
  },

  // 更新库存信息
  updateInventory: async (id, data) => {
    return apiRequest.put(`/inventory/${id}`, data)
  },

  // 获取入库记录列表
  getStockInRecords: async (params = {}) => {
    const { page = 1, limit = 10, status, startDate, endDate } = params
    return apiRequest.get('/inventory/stockin', {
      page,
      limit,
      status,
      startDate,
      endDate
    })
  },

  // 创建入库记录
  createStockInRecord: async (data) => {
    return apiRequest.post('/inventory/stockin', data)
  },

  // 批量创建入库记录
  batchCreateStockInRecords: async (records) => {
    return apiRequest.post('/inventory/stockin/batch', { records })
  },

  // 处理入库记录
  processStockInRecord: async (id) => {
    return apiRequest.post(`/inventory/stockin/${id}/process`)
  },

  // 批量处理入库记录
  batchProcessStockInRecords: async (ids) => {
    return apiRequest.post('/inventory/stockin/batch/process', { ids })
  },

  // 取消入库记录
  cancelStockInRecord: async (id) => {
    return apiRequest.post(`/inventory/stockin/${id}/cancel`)
  },

  // 获取入库统计信息
  getStockInStatistics: async () => {
    return apiRequest.get('/inventory/stockin/statistics')
  },

  // 获取库存预警列表
  getAlerts: async () => {
    return apiRequest.get('/alerts')
  },

  // 获取预警摘要
  getAlertsSummary: async () => {
    return apiRequest.get('/alerts/summary')
  },

  // 手动触发预警检查
  triggerAlertCheck: async () => {
    return apiRequest.post('/alerts/check')
  },

  // 更新预警配置
  updateAlertConfig: async (config) => {
    return apiRequest.put('/alerts/config', config)
  },

  // 获取盘点任务列表
  getStockCounts: async (params = {}) => {
    const { page = 1, limit = 10, status, type } = params
    return apiRequest.get('/stock-count', {
      page,
      limit,
      status,
      type
    })
  },

  // 创建盘点任务
  createStockCount: async (data) => {
    return apiRequest.post('/stock-count', data)
  },

  // 获取盘点任务详情
  getStockCountDetail: async (id) => {
    return apiRequest.get(`/stock-count/${id}`)
  },

  // 更新盘点任务
  updateStockCount: async (id, data) => {
    return apiRequest.put(`/stock-count/${id}`, data)
  },

  // 开始盘点
  startStockCount: async (id) => {
    return apiRequest.post(`/stock-count/${id}/start`)
  },

  // 完成盘点
  completeStockCount: async (id, results) => {
    return apiRequest.post(`/stock-count/${id}/complete`, { results })
  },

  // 取消盘点
  cancelStockCount: async (id) => {
    return apiRequest.post(`/stock-count/${id}/cancel`)
  }
}

export default inventoryService
