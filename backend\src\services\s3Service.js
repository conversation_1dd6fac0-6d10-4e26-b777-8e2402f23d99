const AWS = require('aws-sdk');
const logger = require('../utils/logger');

// 抑制AWS SDK v2维护模式警告
process.env.AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE = '1';

/**
 * AWS S3服务类
 * 处理文件上传、下载、删除等S3操作
 */
class S3Service {
  constructor() {
    this.bucket = process.env.S3_BUCKET;
    this.isEnabled = !!this.bucket;

    if (this.isEnabled) {
      // 配置AWS S3客户端
      this.s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        region: process.env.AWS_REGION || 'us-east-1'
      });

      logger.info('S3Service初始化完成', {
        bucket: this.bucket,
        region: process.env.AWS_REGION || 'us-east-1'
      });
    } else {
      logger.warn('S3Service未配置，文件上传功能将被禁用');
      this.s3 = null;
    }
  }

  /**
   * 上传文件到S3
   * @param {Buffer} fileBuffer - 文件缓冲区
   * @param {string} fileName - 文件名
   * @param {string} contentType - 文件类型
   * @param {string} folder - 存储文件夹（可选）
   * @returns {Promise<Object>} 上传结果
   */
  async uploadFile(fileBuffer, fileName, contentType, folder = 'uploads') {
    if (!this.isEnabled) {
      throw new Error('S3服务未配置，无法上传文件');
    }

    try {
      // 生成唯一的文件键
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const fileExtension = fileName.split('.').pop();
      const uniqueFileName = `${timestamp}_${randomString}.${fileExtension}`;
      const key = folder ? `${folder}/${uniqueFileName}` : uniqueFileName;

      const uploadParams = {
        Bucket: this.bucket,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
        ACL: 'private', // 私有访问，通过预签名URL访问
        Metadata: {
          originalName: fileName,
          uploadTime: new Date().toISOString()
        }
      };

      logger.info('开始上传文件到S3', {
        fileName,
        contentType,
        key,
        size: fileBuffer.length
      });

      const result = await this.s3.upload(uploadParams).promise();

      logger.info('文件上传成功', {
        location: result.Location,
        key: result.Key,
        etag: result.ETag
      });

      return {
        success: true,
        data: {
          key: result.Key,
          location: result.Location,
          etag: result.ETag,
          bucket: this.bucket,
          originalName: fileName,
          contentType,
          size: fileBuffer.length
        }
      };

    } catch (error) {
      logger.error('文件上传失败', {
        fileName,
        error: error.message,
        stack: error.stack
      });

      throw new Error(`文件上传失败: ${error.message}`);
    }
  }

  /**
   * 生成预签名URL用于文件访问
   * @param {string} key - 文件键
   * @param {number} expiresIn - 过期时间（秒），默认1小时
   * @returns {Promise<string>} 预签名URL
   */
  async getSignedUrl(key, expiresIn = 3600) {
    if (!this.isEnabled) {
      throw new Error('S3服务未配置，无法生成预签名URL');
    }

    try {
      const params = {
        Bucket: this.bucket,
        Key: key,
        Expires: expiresIn
      };

      const url = await this.s3.getSignedUrlPromise('getObject', params);

      logger.info('生成预签名URL成功', {
        key,
        expiresIn,
        urlLength: url.length
      });

      return url;

    } catch (error) {
      logger.error('生成预签名URL失败', {
        key,
        error: error.message
      });

      throw new Error(`生成预签名URL失败: ${error.message}`);
    }
  }

  /**
   * 删除S3文件
   * @param {string} key - 文件键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteFile(key) {
    if (!this.isEnabled) {
      throw new Error('S3服务未配置，无法删除文件');
    }

    try {
      const params = {
        Bucket: this.bucket,
        Key: key
      };

      await this.s3.deleteObject(params).promise();

      logger.info('文件删除成功', { key });

      return {
        success: true,
        message: '文件删除成功'
      };

    } catch (error) {
      logger.error('文件删除失败', {
        key,
        error: error.message
      });

      throw new Error(`文件删除失败: ${error.message}`);
    }
  }

  /**
   * 检查文件是否存在
   * @param {string} key - 文件键
   * @returns {Promise<boolean>} 文件是否存在
   */
  async fileExists(key) {
    try {
      const params = {
        Bucket: this.bucket,
        Key: key
      };

      await this.s3.headObject(params).promise();
      return true;

    } catch (error) {
      if (error.code === 'NotFound') {
        return false;
      }
      
      logger.error('检查文件存在性失败', {
        key,
        error: error.message
      });
      
      throw new Error(`检查文件存在性失败: ${error.message}`);
    }
  }

  /**
   * 获取文件元数据
   * @param {string} key - 文件键
   * @returns {Promise<Object>} 文件元数据
   */
  async getFileMetadata(key) {
    try {
      const params = {
        Bucket: this.bucket,
        Key: key
      };

      const result = await this.s3.headObject(params).promise();

      return {
        contentType: result.ContentType,
        contentLength: result.ContentLength,
        lastModified: result.LastModified,
        etag: result.ETag,
        metadata: result.Metadata
      };

    } catch (error) {
      logger.error('获取文件元数据失败', {
        key,
        error: error.message
      });

      throw new Error(`获取文件元数据失败: ${error.message}`);
    }
  }

  /**
   * 批量删除文件
   * @param {Array<string>} keys - 文件键数组
   * @returns {Promise<Object>} 删除结果
   */
  async deleteFiles(keys) {
    try {
      if (!keys || keys.length === 0) {
        return {
          success: true,
          deleted: [],
          errors: []
        };
      }

      const objects = keys.map(key => ({ Key: key }));
      
      const params = {
        Bucket: this.bucket,
        Delete: {
          Objects: objects,
          Quiet: false
        }
      };

      const result = await this.s3.deleteObjects(params).promise();

      logger.info('批量删除文件完成', {
        totalFiles: keys.length,
        deletedCount: result.Deleted?.length || 0,
        errorCount: result.Errors?.length || 0
      });

      return {
        success: true,
        deleted: result.Deleted || [],
        errors: result.Errors || []
      };

    } catch (error) {
      logger.error('批量删除文件失败', {
        keys,
        error: error.message
      });

      throw new Error(`批量删除文件失败: ${error.message}`);
    }
  }

  /**
   * 验证S3连接
   * @returns {Promise<boolean>} 连接是否正常
   */
  async testConnection() {
    try {
      await this.s3.headBucket({ Bucket: this.bucket }).promise();
      logger.info('S3连接测试成功', { bucket: this.bucket });
      return true;

    } catch (error) {
      logger.error('S3连接测试失败', {
        bucket: this.bucket,
        error: error.message
      });
      return false;
    }
  }
}

module.exports = new S3Service();
